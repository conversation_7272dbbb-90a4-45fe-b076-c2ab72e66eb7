{"version": 3, "file": "memory-manager.js", "sourceRoot": "", "sources": ["../../src/utils/memory-manager.ts"], "names": [], "mappings": ";;;AAEA,MAAa,aAAa;IAGxB,MAAM,CAAC,UAAU,CAAC,SAAiB;QACjC,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC;IAC9C,CAAC;IAED,MAAM,CAAC,UAAU,CAAC,SAAiB,EAAE,OAAqB;QACxD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IACxC,CAAC;IAED,MAAM,CAAC,aAAa,CAAC,SAAiB,EAAE,OAA8B;QACpE,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QAC5C,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,gCAAgC,SAAS,EAAE,CAAC,CAAC;QAC/D,CAAC;QAED,MAAM,OAAO,iDACR,QAAQ,GACR,OAAO,KACV,MAAM,kCACD,QAAQ,CAAC,MAAM,GACf,OAAO,CAAC,MAAM,GAEnB,aAAa,kCACR,QAAQ,CAAC,aAAa,GACtB,OAAO,CAAC,aAAa,IAE3B,CAAC;QAEF,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACpC,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,MAAM,CAAC,iBAAiB,CAAC,SAAiB,EAAE,SAAiB,EAAE,MAAmB;QAChF,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QAC3C,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC;YAC1C,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;IAED,MAAM,CAAC,cAAc,CAAC,SAAiB,EAAE,SAAiB;QACxD,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QAC3C,OAAO,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,aAAa,CAAC,SAAS,CAAC,KAAI,IAAI,CAAC;IACnD,CAAC;IAED,MAAM,CAAC,SAAS,CAAC,SAAiB,EAAE,GAAW,EAAE,KAAU;QACzD,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QAC3C,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;YAC5B,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;IAED,MAAM,CAAC,SAAS,CAAC,SAAiB,EAAE,GAAW;QAC7C,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QAC3C,OAAO,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,CAAC,GAAG,CAAC,CAAC;IAC9B,CAAC;IAED,MAAM,CAAC,YAAY,CAAC,SAAiB;QACnC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IAClC,CAAC;IAED,MAAM,CAAC,oBAAoB,CAAC,SAAiB,EAAE,MAAc,EAAE,aAAqB,EAAE;QACpF,MAAM,OAAO,GAAiB;YAC5B,SAAS;YACT,MAAM;YACN,UAAU;YACV,kBAAkB,EAAE,EAAE;YACtB,aAAa,EAAE,EAAE;YACjB,MAAM,EAAE,EAAE;SACX,CAAC;QAEF,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACpC,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,MAAM,CAAC,oBAAoB;QACzB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;IAC1C,CAAC;IAED,MAAM,CAAC,iBAAiB,CAAC,SAAiB;;QACxC,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QAC3C,IAAI,CAAC,OAAO;YAAE,OAAO,IAAI,CAAC;QAE1B,OAAO;YACL,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,aAAa,EAAE,CAAC,CAAC,OAAO,CAAC,UAAU;YACnC,gBAAgB,EAAE,CAAA,MAAA,OAAO,CAAC,UAAU,0CAAE,MAAM,KAAI,CAAC;YACjD,mBAAmB,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,MAAM;YACnE,YAAY,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,MAAM,CACrD,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,YAAY,CAC9D;YACD,eAAe,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,MAAM,CACxD,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,UAAU,CAC5D;YACD,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;SACxC,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,aAAa,CAAC,SAAiB;QACpC,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAED,MAAM,CAAC,aAAa,CAAC,SAAiB,EAAE,OAAqB;QAC3D,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IACtC,CAAC;IAED,2CAA2C;IAC3C,MAAM,CAAC,OAAO,CAAC,cAAsB,EAAE;QACrC,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,WAAW,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAC/D,IAAI,OAAO,GAAG,CAAC,CAAC;QAEhB,KAAK,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC;YAC3D,8CAA8C;YAC9C,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAC3B,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC;iBACpC,GAAG,CAAC,MAAM,CAAC,EAAE,eAAC,OAAA,CAAA,MAAA,MAAM,CAAC,OAAO,0CAAE,OAAO,EAAE,MAAI,MAAA,MAAM,CAAC,SAAS,0CAAE,OAAO,EAAE,CAAA,IAAI,CAAC,CAAA,EAAA,CAAC,CAChF,CAAC;YAEF,IAAI,YAAY,GAAG,UAAU,EAAE,CAAC;gBAC9B,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBAChC,OAAO,EAAE,CAAC;YACZ,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;;AAjIH,sCAkIC;AAjIgB,sBAAQ,GAA8B,IAAI,GAAG,EAAE,CAAC"}