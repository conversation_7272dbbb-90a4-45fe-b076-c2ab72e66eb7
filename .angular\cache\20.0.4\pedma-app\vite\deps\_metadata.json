{"hash": "89ba09d7", "configHash": "15bcf021", "lockfileHash": "869bcc34", "browserHash": "87598654", "optimized": {"@angular/common": {"src": "../../../../../../node_modules/@angular/common/fesm2022/common.mjs", "file": "@angular_common.js", "fileHash": "99bc6144", "needsInterop": false}, "@angular/core": {"src": "../../../../../../node_modules/@angular/core/fesm2022/core.mjs", "file": "@angular_core.js", "fileHash": "a010c2d7", "needsInterop": false}, "@angular/fire/app": {"src": "../../../../../../node_modules/@angular/fire/fesm2022/angular-fire-app.mjs", "file": "@angular_fire_app.js", "fileHash": "0706a56b", "needsInterop": false}, "@angular/fire/auth": {"src": "../../../../../../node_modules/@angular/fire/fesm2022/angular-fire-auth.mjs", "file": "@angular_fire_auth.js", "fileHash": "80c148a0", "needsInterop": false}, "@angular/fire/firestore": {"src": "../../../../../../node_modules/@angular/fire/fesm2022/angular-fire-firestore.mjs", "file": "@angular_fire_firestore.js", "fileHash": "f485ed9d", "needsInterop": false}, "@angular/fire/functions": {"src": "../../../../../../node_modules/@angular/fire/fesm2022/angular-fire-functions.mjs", "file": "@angular_fire_functions.js", "fileHash": "927a86c7", "needsInterop": false}, "@angular/fire/storage": {"src": "../../../../../../node_modules/@angular/fire/fesm2022/angular-fire-storage.mjs", "file": "@angular_fire_storage.js", "fileHash": "ebe8f4cb", "needsInterop": false}, "@angular/forms": {"src": "../../../../../../node_modules/@angular/forms/fesm2022/forms.mjs", "file": "@angular_forms.js", "fileHash": "61591be7", "needsInterop": false}, "@angular/platform-browser": {"src": "../../../../../../node_modules/@angular/platform-browser/fesm2022/platform-browser.mjs", "file": "@angular_platform-browser.js", "fileHash": "31c6af90", "needsInterop": false}, "@angular/router": {"src": "../../../../../../node_modules/@angular/router/fesm2022/router.mjs", "file": "@angular_router.js", "fileHash": "9521a5e5", "needsInterop": false}, "rxjs": {"src": "../../../../../../node_modules/rxjs/dist/esm5/index.js", "file": "rxjs.js", "fileHash": "3bc7ffef", "needsInterop": false}}, "chunks": {"chunk-6TODO5FC": {"file": "chunk-6TODO5FC.js"}, "chunk-7MTRFNIW": {"file": "chunk-7MTRFNIW.js"}, "chunk-MDTI5QPY": {"file": "chunk-MDTI5QPY.js"}, "chunk-ULTTM7ZI": {"file": "chunk-ULTTM7ZI.js"}, "chunk-S6GV7V7B": {"file": "chunk-S6GV7V7B.js"}, "chunk-3STIUTR6": {"file": "chunk-3STIUTR6.js"}}}