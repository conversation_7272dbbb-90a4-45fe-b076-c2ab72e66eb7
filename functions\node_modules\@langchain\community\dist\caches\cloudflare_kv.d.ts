import type { KVNamespace } from "@cloudflare/workers-types";
import { BaseCache } from "@langchain/core/caches";
import { Generation } from "@langchain/core/outputs";
/**
 * @deprecated Install and import from "@langchain/cloudflare" instead.
 *
 * Represents a specific implementation of a caching mechanism using Cloudflare KV
 * as the underlying storage system. It extends the `BaseCache` class and
 * overrides its methods to provide the Cloudflare KV-specific logic.
 * @example
 * ```typescript
 * // Example of using OpenAI with Cloudflare KV as cache in a Cloudflare Worker
 * const cache = new CloudflareKVCache(env.KV_NAMESPACE);
 * const model = new ChatAnthropic({
 *   cache,
 * });
 * const response = await model.invoke("How are you today?");
 * return new Response(JSON.stringify(response), {
 *   headers: { "content-type": "application/json" },
 * });
 *
 * ```
 */
export declare class CloudflareKVCache extends BaseCache {
    private binding;
    constructor(binding: KVNamespace);
    /**
     * Retrieves data from the cache. It constructs a cache key from the given
     * `prompt` and `llm<PERSON>ey`, and retrieves the corresponding value from the
     * Cloudflare KV namespace.
     * @param prompt The prompt used to construct the cache key.
     * @param llmKey The LLM key used to construct the cache key.
     * @returns An array of Generations if found, null otherwise.
     */
    lookup(prompt: string, llmKey: string): Promise<Generation[] | null>;
    /**
     * Updates the cache with new data. It constructs a cache key from the
     * given `prompt` and `llmKey`, and stores the `value` in the Cloudflare KV
     * namespace.
     * @param prompt The prompt used to construct the cache key.
     * @param llmKey The LLM key used to construct the cache key.
     * @param value The value to be stored in the cache.
     */
    update(prompt: string, llmKey: string, value: Generation[]): Promise<void>;
}
