rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Sessions can only be accessed by the owner
    match /sessions/{sessionId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
    }
    
    // Templates can be read by anyone, but only created/modified by the owner
    match /templates/{templateId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        (request.auth.uid == resource.data.createdBy || 
         request.auth.uid == request.resource.data.createdBy);
    }
    
    // Public templates can be read by anyone
    match /public-templates/{templateId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        request.auth.token.admin == true; // Only admins can create public templates
    }
    
    // Analytics data (read-only for users, write for system)
    match /analytics/{document=**} {
      allow read: if request.auth != null;
      allow write: if false; // Only server-side functions can write analytics
    }
  }
}
