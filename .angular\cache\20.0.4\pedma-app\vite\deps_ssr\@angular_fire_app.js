import { createRequire } from 'module';const require = createRequire(import.meta.url);
import {
  DEFAULT_ENTRY_NAME,
  FirebaseApp,
  FirebaseAppModule,
  FirebaseApps,
  FirebaseError,
  SDK_VERSION,
  _addComponent,
  _addOrOverwriteComponent,
  _apps,
  _clearComponents,
  _components,
  _getProvider,
  _isFirebaseApp,
  _isFirebaseServerApp,
  _registerComponent,
  _removeServiceInstance,
  _serverApps,
  deleteApp,
  firebaseApp$,
  getApp2 as getApp,
  getApps,
  initializeApp,
  initializeServerApp,
  onLog,
  provideFirebaseApp,
  registerVersion2 as registerVersion,
  setLogLevel
} from "./chunk-FT7RHIZK.js";
import "./chunk-QRQSPDYV.js";
import "./chunk-DMO44UNM.js";
import "./chunk-6DU2HRTW.js";
export {
  FirebaseApp,
  FirebaseAppModule,
  FirebaseApps,
  FirebaseError,
  SDK_VERSION,
  DEFAULT_ENTRY_NAME as _DEFAULT_ENTRY_NAME,
  _addComponent,
  _addOrOverwriteComponent,
  _apps,
  _clearComponents,
  _components,
  _getProvider,
  _isFirebaseApp,
  _isFirebaseServerApp,
  _registerComponent,
  _removeServiceInstance,
  _serverApps,
  deleteApp,
  firebaseApp$,
  getApp,
  getApps,
  initializeApp,
  initializeServerApp,
  onLog,
  provideFirebaseApp,
  registerVersion,
  setLogLevel
};
