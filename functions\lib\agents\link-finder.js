"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LinkFinderAgent = void 0;
const langchain_config_1 = require("../utils/langchain-config");
const memory_manager_1 = require("../utils/memory-manager");
const axios_1 = __importDefault(require("axios"));
class LinkFinderAgent {
    static getInstance() {
        if (!this.instance) {
            this.instance = new LinkFinderAgent();
        }
        return this.instance;
    }
    async findLinks(sessionId, parameters) {
        const startTime = new Date();
        try {
            const status = {
                name: 'linkFinder',
                status: 'processing',
                progress: 0,
                message: 'Scanning transcript for resources...',
                startTime
            };
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'linkFinder', status);
            const context = memory_manager_1.MemoryManager.getContext(sessionId);
            if (!context) {
                throw new Error(`No context found for session ${sessionId}`);
            }
            const transcript = parameters.transcript || context.transcript;
            if (!transcript) {
                throw new Error('No transcript available for link finding');
            }
            // Update progress
            status.progress = 20;
            status.message = 'Identifying mentioned resources...';
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'linkFinder', status);
            // Extract mentioned resources using AI
            const mentionedResources = await this.extractMentionedResources(transcript, context);
            // Update progress
            status.progress = 40;
            status.message = 'Searching for URLs and links...';
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'linkFinder', status);
            // Search for actual URLs and validate them
            const foundLinks = await this.searchAndValidateLinks(mentionedResources, context);
            // Update progress
            status.progress = 70;
            status.message = 'Categorizing and scoring resources...';
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'linkFinder', status);
            // Categorize and score the links
            const categorizedLinks = await this.categorizeLinks(foundLinks, context);
            // Update progress
            status.progress = 90;
            status.message = 'Finalizing resource list...';
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'linkFinder', status);
            // Filter and sort by confidence
            const finalResources = categorizedLinks
                .filter(link => link.confidence > 0.3)
                .sort((a, b) => b.confidence - a.confidence)
                .slice(0, 15); // Limit to top 15 resources
            // Store results in memory
            memory_manager_1.MemoryManager.addMemory(sessionId, 'resources', finalResources);
            // Complete
            status.status = 'complete';
            status.progress = 100;
            status.message = `Found ${finalResources.length} relevant resources`;
            status.endTime = new Date();
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'linkFinder', status);
            const updatedContext = memory_manager_1.MemoryManager.getContext(sessionId);
            return {
                success: true,
                data: finalResources,
                status,
                updatedContext: updatedContext || undefined
            };
        }
        catch (error) {
            const errorStatus = {
                name: 'linkFinder',
                status: 'error',
                progress: 0,
                message: `Link finding failed: ${error}`,
                startTime,
                endTime: new Date()
            };
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'linkFinder', errorStatus);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown link finding error',
                status: errorStatus
            };
        }
    }
    async extractMentionedResources(transcript, context) {
        const prompt = `Analyze this podcast transcript and identify all resources, tools, books, websites, and products mentioned:

Transcript: ${transcript.substring(0, 4000)}...

Look for mentions of:
- Books and publications
- Software tools and platforms
- Websites and online services
- Products and brands
- Companies and organizations
- Social media accounts
- Online courses or educational content

For each resource, extract:
- Name/title
- Type (book, tool, website, product, etc.)
- Context where it was mentioned
- Approximate timestamp if possible

Respond with JSON array of objects with: name, type, context, confidence`;
        const response = await langchain_config_1.LangChainConfig.generateResponse(prompt, context, "You are an expert at identifying resources and references. Respond only with valid JSON.");
        try {
            return JSON.parse(response);
        }
        catch (error) {
            // Fallback: simple pattern matching
            return this.fallbackResourceExtraction(transcript);
        }
    }
    async searchAndValidateLinks(resources, context) {
        const links = [];
        for (const resource of resources) {
            try {
                // Try to find actual URLs for the mentioned resources
                const searchResults = await this.searchForResource(resource.name, resource.type);
                for (const result of searchResults) {
                    const validatedLink = await this.validateLink(result.url);
                    if (validatedLink.isValid) {
                        links.push({
                            label: resource.name,
                            url: result.url,
                            type: this.mapResourceType(resource.type),
                            confidence: resource.confidence * 0.8, // Reduce confidence slightly
                            extractedFrom: resource.context
                        });
                    }
                }
            }
            catch (error) {
                console.warn(`Failed to search for resource: ${resource.name}`, error);
            }
        }
        return links;
    }
    async searchForResource(name, type) {
        var _a;
        // Use SerpAPI if available, otherwise return empty array
        const serpApiKey = process.env.SERPAPI_KEY;
        if (!serpApiKey) {
            return [];
        }
        try {
            const searchQuery = `${name} ${type} official website`;
            const response = await axios_1.default.get('https://serpapi.com/search', {
                params: {
                    q: searchQuery,
                    api_key: serpApiKey,
                    engine: 'google',
                    num: 3
                },
                timeout: 10000
            });
            return ((_a = response.data.organic_results) === null || _a === void 0 ? void 0 : _a.map((result) => ({
                url: result.link,
                title: result.title,
                description: result.snippet
            }))) || [];
        }
        catch (error) {
            console.warn('SerpAPI search failed:', error);
            return [];
        }
    }
    async validateLink(url) {
        try {
            const response = await axios_1.default.head(url, {
                timeout: 5000,
                maxRedirects: 3
            });
            return {
                isValid: response.status >= 200 && response.status < 400,
                statusCode: response.status
            };
        }
        catch (error) {
            return {
                isValid: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
    async categorizeLinks(links, context) {
        // Use AI to better categorize and score the links
        const prompt = `Categorize and score these resources for a podcast description:

Resources: ${JSON.stringify(links.slice(0, 10))}

For each resource:
1. Verify the type is correct (website, book, tool, social, product)
2. Assign a relevance score (0-1) based on how useful it would be in a YouTube description
3. Improve the label if needed

Respond with JSON array of the same structure but with updated types, confidence scores, and labels.`;
        try {
            const response = await langchain_config_1.LangChainConfig.generateResponse(prompt, context, "You are an expert at categorizing and scoring online resources. Respond only with valid JSON.");
            const categorized = JSON.parse(response);
            return Array.isArray(categorized) ? categorized : links;
        }
        catch (error) {
            return links;
        }
    }
    mapResourceType(type) {
        const typeMap = {
            'book': 'book',
            'publication': 'book',
            'software': 'tool',
            'tool': 'tool',
            'platform': 'tool',
            'app': 'tool',
            'website': 'website',
            'site': 'website',
            'social': 'social',
            'twitter': 'social',
            'linkedin': 'social',
            'instagram': 'social',
            'product': 'product',
            'service': 'product'
        };
        return typeMap[type.toLowerCase()] || 'website';
    }
    fallbackResourceExtraction(transcript) {
        const resources = [];
        // Simple pattern matching for common resource types
        const patterns = [
            { regex: /(?:book|read|author).*?"([^"]+)"/gi, type: 'book' },
            { regex: /(?:website|site|check out).*?(https?:\/\/[^\s]+)/gi, type: 'website' },
            { regex: /(?:tool|software|platform|app).*?"([^"]+)"/gi, type: 'tool' },
            { regex: /@(\w+)/gi, type: 'social' }
        ];
        patterns.forEach(pattern => {
            let match;
            while ((match = pattern.regex.exec(transcript)) !== null) {
                resources.push({
                    name: match[1],
                    type: pattern.type,
                    context: match[0],
                    confidence: 0.6
                });
            }
        });
        return resources.slice(0, 10); // Limit to 10 resources
    }
}
exports.LinkFinderAgent = LinkFinderAgent;
//# sourceMappingURL=link-finder.js.map