"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TranscriberAgent = void 0;
const sdk_1 = require("@deepgram/sdk");
const memory_manager_1 = require("../utils/memory-manager");
const axios_1 = __importDefault(require("axios"));
class TranscriberAgent {
    constructor() {
        const deepgramApiKey = process.env.DEEPGRAM_API_KEY;
        if (!deepgramApiKey) {
            throw new Error('DEEPGRAM_API_KEY environment variable is required');
        }
        this.deepgram = (0, sdk_1.createClient)(deepgramApiKey);
    }
    static getInstance() {
        if (!this.instance) {
            this.instance = new TranscriberAgent();
        }
        return this.instance;
    }
    async processAudio(sessionId, parameters) {
        const startTime = new Date();
        try {
            const status = {
                name: 'transcriber',
                status: 'processing',
                progress: 0,
                message: 'Starting audio transcription...',
                startTime
            };
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'transcriber', status);
            const context = memory_manager_1.MemoryManager.getContext(sessionId);
            if (!context) {
                throw new Error(`No context found for session ${sessionId}`);
            }
            // Get audio file URL from context or parameters
            const audioUrl = parameters.audioUrl || context.memory.audioFileUrl;
            if (!audioUrl) {
                throw new Error('No audio file URL provided');
            }
            // Update progress
            status.progress = 20;
            status.message = 'Downloading audio file...';
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'transcriber', status);
            // Download audio file
            const audioBuffer = await this.downloadAudioFile(audioUrl);
            // Update progress
            status.progress = 40;
            status.message = 'Sending to Deepgram for transcription...';
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'transcriber', status);
            // Transcribe with Deepgram
            const transcriptionResult = await this.transcribeWithDeepgram(audioBuffer);
            // Update progress
            status.progress = 80;
            status.message = 'Processing transcription results...';
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'transcriber', status);
            // Store transcription in context
            memory_manager_1.MemoryManager.updateContext(sessionId, {
                transcript: transcriptionResult.text,
                memory: Object.assign(Object.assign({}, context.memory), { transcriptionResult, audioProcessed: true })
            });
            // Complete
            status.status = 'complete';
            status.progress = 100;
            status.message = `Transcription completed: ${transcriptionResult.text.length} characters`;
            status.endTime = new Date();
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'transcriber', status);
            return {
                success: true,
                data: transcriptionResult,
                status,
                updatedContext: memory_manager_1.MemoryManager.getContext(sessionId)
            };
        }
        catch (error) {
            const errorStatus = {
                name: 'transcriber',
                status: 'error',
                progress: 0,
                message: `Transcription failed: ${error}`,
                startTime,
                endTime: new Date()
            };
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'transcriber', errorStatus);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown transcription error',
                status: errorStatus
            };
        }
    }
    async downloadAudioFile(url) {
        try {
            const response = await axios_1.default.get(url, {
                responseType: 'arraybuffer',
                timeout: 300000, // 5 minutes timeout
            });
            return Buffer.from(response.data);
        }
        catch (error) {
            throw new Error(`Failed to download audio file: ${error}`);
        }
    }
    async transcribeWithDeepgram(audioBuffer) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0, _1, _2, _3, _4, _5, _6, _7, _8, _9, _10;
        try {
            const { result, error } = await this.deepgram.listen.prerecorded.transcribeFile(audioBuffer, {
                model: 'nova-2',
                language: 'en-US',
                smart_format: true,
                punctuate: true,
                diarize: true,
                utterances: true,
                words: true,
                paragraphs: true,
                summarize: 'v2',
                detect_topics: true,
                sentiment: true,
            });
            if (error) {
                throw new Error(`Deepgram API error: ${error}`);
            }
            // Extract the main transcript
            const transcript = ((_e = (_d = (_c = (_b = (_a = result.results) === null || _a === void 0 ? void 0 : _a.channels) === null || _b === void 0 ? void 0 : _b[0]) === null || _c === void 0 ? void 0 : _c.alternatives) === null || _d === void 0 ? void 0 : _d[0]) === null || _e === void 0 ? void 0 : _e.transcript) || '';
            // Extract word-level timestamps
            const words = ((_k = (_j = (_h = (_g = (_f = result.results) === null || _f === void 0 ? void 0 : _f.channels) === null || _g === void 0 ? void 0 : _g[0]) === null || _h === void 0 ? void 0 : _h.alternatives) === null || _j === void 0 ? void 0 : _j[0]) === null || _k === void 0 ? void 0 : _k.words) || [];
            // Extract confidence score
            const confidence = ((_q = (_p = (_o = (_m = (_l = result.results) === null || _l === void 0 ? void 0 : _l.channels) === null || _m === void 0 ? void 0 : _m[0]) === null || _o === void 0 ? void 0 : _o.alternatives) === null || _p === void 0 ? void 0 : _p[0]) === null || _q === void 0 ? void 0 : _q.confidence) || 0;
            // Extract detected language
            const language = ((_t = (_s = (_r = result.results) === null || _r === void 0 ? void 0 : _r.channels) === null || _s === void 0 ? void 0 : _s[0]) === null || _t === void 0 ? void 0 : _t.detected_language) || 'en';
            // Extract duration
            const duration = ((_u = result.metadata) === null || _u === void 0 ? void 0 : _u.duration) || 0;
            // Extract additional insights
            const topics = ((_w = (_v = result.results) === null || _v === void 0 ? void 0 : _v.topics) === null || _w === void 0 ? void 0 : _w.segments) || [];
            const sentiment = ((_y = (_x = result.results) === null || _x === void 0 ? void 0 : _x.sentiment) === null || _y === void 0 ? void 0 : _y.segments) || [];
            const summary = ((_0 = (_z = result.results) === null || _z === void 0 ? void 0 : _z.summary) === null || _0 === void 0 ? void 0 : _0.short) || '';
            const transcriptionResult = {
                text: transcript,
                words: words.map((word) => ({
                    word: word.word,
                    start: word.start,
                    end: word.end,
                    confidence: word.confidence
                })),
                confidence,
                language,
                duration
            };
            // Store additional insights in memory for other agents
            const insights = {
                topics,
                sentiment,
                summary,
                speakers: ((_5 = (_4 = (_3 = (_2 = (_1 = result.results) === null || _1 === void 0 ? void 0 : _1.channels) === null || _2 === void 0 ? void 0 : _2[0]) === null || _3 === void 0 ? void 0 : _3.alternatives) === null || _4 === void 0 ? void 0 : _4[0]) === null || _5 === void 0 ? void 0 : _5.speakers) || [],
                paragraphs: ((_10 = (_9 = (_8 = (_7 = (_6 = result.results) === null || _6 === void 0 ? void 0 : _6.channels) === null || _7 === void 0 ? void 0 : _7[0]) === null || _8 === void 0 ? void 0 : _8.alternatives) === null || _9 === void 0 ? void 0 : _9[0]) === null || _10 === void 0 ? void 0 : _10.paragraphs) || []
            };
            return Object.assign(Object.assign({}, transcriptionResult), { insights });
        }
        catch (error) {
            throw new Error(`Deepgram transcription failed: ${error}`);
        }
    }
    async getTranscriptionStatus(sessionId) {
        return memory_manager_1.MemoryManager.getAgentStatus(sessionId, 'transcriber');
    }
    async retryTranscription(sessionId) {
        // Reset the agent status and retry
        const context = memory_manager_1.MemoryManager.getContext(sessionId);
        if (!context) {
            throw new Error(`No context found for session ${sessionId}`);
        }
        // Clear previous transcription data
        memory_manager_1.MemoryManager.updateContext(sessionId, {
            transcript: '',
            memory: Object.assign(Object.assign({}, context.memory), { transcriptionResult: null, audioProcessed: false })
        });
        // Retry transcription
        return await this.processAudio(sessionId, {
            audioUrl: context.memory.audioFileUrl
        });
    }
}
exports.TranscriberAgent = TranscriberAgent;
//# sourceMappingURL=transcriber.js.map