{"version": 3, "file": "editor.js", "sourceRoot": "", "sources": ["../../src/agents/editor.ts"], "names": [], "mappings": ";;;AACA,gEAA4F;AAC5F,4DAAwD;AAExD,MAAa,WAAW;IAGtB,MAAM,CAAC,WAAW;QAChB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,IAAI,CAAC,QAAQ,GAAG,IAAI,WAAW,EAAE,CAAC;QACpC,CAAC;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,SAAiB,EAAE,UAAe;QACtD,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,MAAM,GAAgB;gBAC1B,IAAI,EAAE,QAAQ;gBACd,MAAM,EAAE,YAAY;gBACpB,QAAQ,EAAE,CAAC;gBACX,OAAO,EAAE,2BAA2B;gBACpC,SAAS;aACV,CAAC;YAEF,8BAAa,CAAC,iBAAiB,CAAC,SAAS,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;YAE7D,MAAM,OAAO,GAAG,8BAAa,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YACpD,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,gCAAgC,SAAS,EAAE,CAAC,CAAC;YAC/D,CAAC;YAED,MAAM,kBAAkB,GAAG,UAAU,CAAC,WAAW,IAAI,OAAO,CAAC,kBAAkB,CAAC;YAChF,MAAM,WAAW,GAAG,UAAU,CAAC,WAAW,CAAC;YAE3C,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBACxB,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;YACtD,CAAC;YAED,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;YAC9C,CAAC;YAED,kBAAkB;YAClB,MAAM,CAAC,QAAQ,GAAG,EAAE,CAAC;YACrB,MAAM,CAAC,OAAO,GAAG,oCAAoC,CAAC;YACtD,8BAAa,CAAC,iBAAiB,CAAC,SAAS,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;YAE7D,2BAA2B;YAC3B,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,kBAAkB,EAAE,OAAO,CAAC,CAAC;YAE7F,kBAAkB;YAClB,MAAM,CAAC,QAAQ,GAAG,EAAE,CAAC;YACrB,MAAM,CAAC,OAAO,GAAG,qBAAqB,YAAY,CAAC,MAAM,KAAK,CAAC;YAC/D,8BAAa,CAAC,iBAAiB,CAAC,SAAS,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;YAE7D,kBAAkB;YAClB,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,UAAU,CAC9C,kBAAkB,EAClB,WAAW,EACX,YAAY,EACZ,OAAO,CACR,CAAC;YAEF,kBAAkB;YAClB,MAAM,CAAC,QAAQ,GAAG,EAAE,CAAC;YACrB,MAAM,CAAC,OAAO,GAAG,uBAAuB,CAAC;YACzC,8BAAa,CAAC,iBAAiB,CAAC,SAAS,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;YAE7D,uBAAuB;YACvB,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,eAAe,CACrD,kBAAkB,EAClB,kBAAkB,EAClB,WAAW,EACX,OAAO,CACR,CAAC;YAEF,sCAAsC;YACtC,8BAAa,CAAC,aAAa,CAAC,SAAS,EAAE;gBACrC,kBAAkB,kCACb,oBAAoB,KACvB,YAAY,EAAE,IAAI,IAAI,EAAE,EACxB,OAAO,EAAE,CAAC,kBAAkB,CAAC,OAAO,IAAI,CAAC,CAAC,GAAG,CAAC,GAC/C;aACF,CAAC,CAAC;YAEH,WAAW;YACX,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC;YAC3B,MAAM,CAAC,QAAQ,GAAG,GAAG,CAAC;YACtB,MAAM,CAAC,OAAO,GAAG,oBAAoB,YAAY,CAAC,OAAO,EAAE,CAAC;YAC5D,MAAM,CAAC,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;YAC5B,8BAAa,CAAC,iBAAiB,CAAC,SAAS,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;YAE7D,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,kBAAkB,EAAE,oBAAoB;oBACxC,OAAO,EAAE,YAAY;oBACrB,OAAO,EAAE,QAAQ,YAAY,CAAC,MAAM,KAAK,YAAY,CAAC,OAAO,EAAE;iBAChE;gBACD,MAAM;gBACN,cAAc,EAAE,8BAAa,CAAC,UAAU,CAAC,SAAS,CAAC;aACpD,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,WAAW,GAAgB;gBAC/B,IAAI,EAAE,QAAQ;gBACd,MAAM,EAAE,OAAO;gBACf,QAAQ,EAAE,CAAC;gBACX,OAAO,EAAE,gBAAgB,KAAK,EAAE;gBAChC,SAAS;gBACT,OAAO,EAAE,IAAI,IAAI,EAAE;aACpB,CAAC;YAEF,8BAAa,CAAC,iBAAiB,CAAC,SAAS,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;YAElE,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,uBAAuB;gBACvE,MAAM,EAAE,WAAW;aACpB,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAC9B,WAAmB,EACnB,kBAAsC,EACtC,OAAqB;;QAErB,MAAM,MAAM,GAAG;;iBAEF,WAAW;;;cAGd,MAAA,kBAAkB,CAAC,QAAQ,0CAAE,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;eAC7C,MAAA,kBAAkB,CAAC,QAAQ,0CAAE,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;cAC/C,MAAA,kBAAkB,CAAC,OAAO,0CAAE,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;gBAC3C,MAAA,kBAAkB,CAAC,SAAS,0CAAE,IAAI,CAAC,IAAI,CAAC;eACzC,CAAA,MAAA,kBAAkB,CAAC,SAAS,0CAAE,MAAM,KAAI,CAAC;gBACxC,CAAA,MAAA,kBAAkB,CAAC,UAAU,0CAAE,MAAM,KAAI,CAAC;sBACpC,MAAA,kBAAkB,CAAC,QAAQ,0CAAE,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;SAC3D,CAAA,MAAA,MAAA,kBAAkB,CAAC,GAAG,0CAAE,QAAQ,0CAAE,MAAM,KAAI,CAAC,cAAc,CAAA,MAAA,MAAA,kBAAkB,CAAC,GAAG,0CAAE,QAAQ,0CAAE,MAAM,KAAI,CAAC;;;;;;;;;;;;;;EAc/G,CAAC;QAEC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,kCAAe,CAAC,gBAAgB,CACrD,MAAM,EACN,OAAO,EACP,6FAA6F,CAC9F,CAAC;YAEF,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC9B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,oBAAoB;YACpB,OAAO,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,UAAU,CACtB,kBAAsC,EACtC,WAAmB,EACnB,YAAiB,EACjB,OAAqB;QAErB,MAAM,kBAAkB,qBAAQ,kBAAkB,CAAE,CAAC;QAErD,oCAAoC;QACpC,KAAK,MAAM,OAAO,IAAI,YAAY,CAAC,QAAQ,EAAE,CAAC;YAC5C,QAAQ,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;gBAC9B,KAAK,UAAU;oBACb,kBAAkB,CAAC,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,CACnD,kBAAkB,CAAC,QAAQ,EAC3B,WAAW,EACX,YAAY,EACZ,OAAO,CACR,CAAC;oBACF,MAAM;gBAER,KAAK,UAAU,CAAC;gBAChB,KAAK,WAAW;oBACd,kBAAkB,CAAC,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAC9C,kBAAkB,CAAC,QAAQ,EAC3B,OAAO,EACP,WAAW,EACX,YAAY,EACZ,OAAO,CACR,CAAC;oBACF,MAAM;gBAER,KAAK,SAAS,CAAC;gBACf,KAAK,UAAU;oBACb,kBAAkB,CAAC,OAAO,GAAG,MAAM,IAAI,CAAC,OAAO,CAC7C,kBAAkB,CAAC,OAAO,EAC1B,MAAM,EACN,WAAW,EACX,YAAY,EACZ,OAAO,CACR,CAAC;oBACF,MAAM;gBAER,KAAK,QAAQ,CAAC;gBACd,KAAK,YAAY;oBACf,kBAAkB,CAAC,SAAS,GAAG,MAAM,IAAI,CAAC,UAAU,CAClD,kBAAkB,CAAC,SAAS,EAC5B,WAAW,EACX,YAAY,EACZ,OAAO,CACR,CAAC;oBACF,MAAM;gBAER,KAAK,UAAU,CAAC;gBAChB,KAAK,SAAS,CAAC;gBACf,KAAK,kBAAkB;oBACrB,kBAAkB,CAAC,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,CACnD,kBAAkB,CAAC,QAAQ,EAC3B,WAAW,EACX,YAAY,EACZ,OAAO,CACR,CAAC;oBACF,MAAM;gBAER,KAAK,KAAK,CAAC;gBACX,KAAK,UAAU,CAAC;gBAChB,KAAK,UAAU;oBACb,kBAAkB,CAAC,GAAG,GAAG,MAAM,IAAI,CAAC,OAAO,CACzC,kBAAkB,CAAC,GAAG,EACtB,WAAW,EACX,YAAY,EACZ,OAAO,CACR,CAAC;oBACF,MAAM;gBAER,KAAK,YAAY;oBACf,kBAAkB,CAAC,UAAU,GAAG,MAAM,IAAI,CAAC,cAAc,CACvD,kBAAkB,CAAC,UAAU,EAC7B,WAAW,EACX,YAAY,EACZ,OAAO,CACR,CAAC;oBACF,MAAM;YACV,CAAC;QACH,CAAC;QAED,OAAO,kBAAkB,CAAC;IAC5B,CAAC;IAEO,KAAK,CAAC,YAAY,CACxB,eAAuB,EACvB,WAAmB,EACnB,YAAiB,EACjB,OAAqB;QAErB,MAAM,MAAM,GAAG;;qBAEE,eAAe;iBACnB,WAAW;oBACR,YAAY,CAAC,MAAM;QAC/B,YAAY,CAAC,IAAI;;;;;;;;sCAQa,CAAC;QAEnC,MAAM,QAAQ,GAAG,MAAM,kCAAe,CAAC,gBAAgB,CACrD,MAAM,EACN,OAAO,EACP,wEAAwE,CACzE,CAAC;QAEF,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;IACzB,CAAC;IAEO,KAAK,CAAC,OAAO,CACnB,UAAkB,EAClB,IAAsB,EACtB,WAAmB,EACnB,YAAiB,EACjB,OAAqB;QAErB,MAAM,MAAM,GAAG,aAAa,IAAI;;UAE1B,IAAI,UAAU,UAAU;iBACjB,WAAW;oBACR,YAAY,CAAC,MAAM;;;;;;;;iCAQN,CAAC;QAE9B,MAAM,QAAQ,GAAG,MAAM,kCAAe,CAAC,gBAAgB,CACrD,MAAM,EACN,OAAO,EACP,gEAAgE,CACjE,CAAC;QAEF,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;IACzB,CAAC;IAEO,KAAK,CAAC,UAAU,CACtB,aAAuB,EACvB,WAAmB,EACnB,YAAiB,EACjB,OAAqB;QAErB,MAAM,MAAM,GAAG;;kBAED,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;iBACzB,WAAW;oBACR,YAAY,CAAC,MAAM;;;;;;;;iDAQU,CAAC;QAE9C,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,kCAAe,CAAC,gBAAgB,CACrD,MAAM,EACN,OAAO,EACP,uEAAuE,CACxE,CAAC;YAEF,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAC1C,OAAO,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,aAAa,CAAC;QACpE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,aAAa,CAAC;QACvB,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,YAAY,CACxB,eAAuB,EACvB,WAAmB,EACnB,YAAiB,EACjB,OAAqB;QAErB,MAAM,MAAM,GAAG;;6BAEU,eAAe;iBAC3B,WAAW;oBACR,YAAY,CAAC,MAAM;;;;;;;;8CAQO,CAAC;QAE3C,MAAM,QAAQ,GAAG,MAAM,kCAAe,CAAC,gBAAgB,CACrD,MAAM,EACN,OAAO,EACP,gEAAgE,CACjE,CAAC;QAEF,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;IACzB,CAAC;IAEO,KAAK,CAAC,OAAO,CACnB,UAAe,EACf,WAAmB,EACnB,YAAiB,EACjB,OAAqB;;QAErB,MAAM,MAAM,GAAG;;;cAGL,CAAA,MAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,QAAQ,0CAAE,IAAI,CAAC,IAAI,CAAC,KAAI,MAAM;cAC1C,CAAA,MAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,QAAQ,0CAAE,IAAI,CAAC,IAAI,CAAC,KAAI,MAAM;qBACnC,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,cAAc,KAAI,MAAM;;iBAExC,WAAW;oBACR,YAAY,CAAC,MAAM;;;;oFAI6C,CAAC;QAEjF,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,kCAAe,CAAC,gBAAgB,CACrD,MAAM,EACN,OAAO,EACP,sDAAsD,CACvD,CAAC;YAEF,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC9B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,UAAU,CAAC;QACpB,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,cAAc,CAC1B,iBAAwB,EACxB,WAAmB,EACnB,YAAiB,EACjB,OAAqB;QAErB,qEAAqE;QACrE,qEAAqE;QACrE,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAEO,KAAK,CAAC,eAAe,CAC3B,mBAAuC,EACvC,kBAAsC,EACtC,WAAmB,EACnB,OAAqB;QAErB,6DAA6D;QAC7D,MAAM,SAAS,qBAAQ,kBAAkB,CAAE,CAAC;QAE5C,wCAAwC;QACxC,IAAI,CAAC,SAAS,CAAC,QAAQ,IAAI,mBAAmB,CAAC,QAAQ,EAAE,CAAC;YACxD,SAAS,CAAC,QAAQ,GAAG,mBAAmB,CAAC,QAAQ,CAAC;QACpD,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,SAAS,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC7D,SAAS,CAAC,SAAS,GAAG,mBAAmB,CAAC,SAAS,CAAC;QACtD,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,oBAAoB,CAAC,WAAmB;QAC9C,MAAM,OAAO,GAAG,WAAW,CAAC,WAAW,EAAE,CAAC;QAE1C,IAAI,MAAM,GAAG,gBAAgB,CAAC;QAC9B,IAAI,QAAQ,GAAG,CAAC,UAAU,CAAC,CAAC;QAC5B,IAAI,IAAI,GAAG,UAAU,CAAC;QAEtB,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAC3D,MAAM,GAAG,+BAA+B,CAAC;YACzC,QAAQ,GAAG,CAAC,UAAU,CAAC,CAAC;YACxB,IAAI,GAAG,QAAQ,CAAC;QAClB,CAAC;aAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YACtE,MAAM,GAAG,iBAAiB,CAAC;YAC3B,IAAI,GAAG,SAAS,CAAC;QACnB,CAAC;aAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YACvC,MAAM,GAAG,iBAAiB,CAAC;YAC3B,QAAQ,GAAG,CAAC,KAAK,CAAC,CAAC;QACrB,CAAC;aAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACrC,MAAM,GAAG,eAAe,CAAC;YACzB,QAAQ,GAAG,CAAC,QAAQ,CAAC,CAAC;QACxB,CAAC;QAED,OAAO;YACL,MAAM;YACN,QAAQ;YACR,IAAI;YACJ,YAAY,EAAE,EAAE;YAChB,OAAO,EAAE,WAAW,MAAM,eAAe;SAC1C,CAAC;IACJ,CAAC;CACF;AA1dD,kCA0dC"}