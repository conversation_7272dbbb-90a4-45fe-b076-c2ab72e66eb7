
export default {
  bootstrap: () => import('./main.server.mjs').then(m => m.default),
  inlineCriticalCss: true,
  baseHref: '/',
  locale: undefined,
  routes: [
  {
    "renderMode": 2,
    "route": "/"
  }
],
  entryPointToBrowserMapping: undefined,
  assets: {
    'index.csr.html': {size: 2328, hash: '65eed1eff0ec46ab1df949bbb08e28871178fc962506fd4704d58d175abc3a58', text: () => import('./assets-chunks/index_csr_html.mjs').then(m => m.default)},
    'index.server.html': {size: 946, hash: '775aed146c9a48f2c8b6d1930855ac0c32d531cde2d5332936e18610a2593275', text: () => import('./assets-chunks/index_server_html.mjs').then(m => m.default)},
    'index.html': {size: 9154, hash: 'a381ee498a5d90d3865a0b7d2d7929cd8b4258bd0a75d9bbc3a9b9f3c8a120cd', text: () => import('./assets-chunks/index_html.mjs').then(m => m.default)},
    'styles-R5OPNWDT.css': {size: 20618, hash: '0UDiL+KCDVU', text: () => import('./assets-chunks/styles-R5OPNWDT_css.mjs').then(m => m.default)}
  },
};
