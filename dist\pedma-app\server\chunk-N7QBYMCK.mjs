import './polyfills.server.mjs';
var k=Object.create;var g=Object.defineProperty,l=Object.defineProperties,m=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertyNames,f=Object.getOwnPropertySymbols,p=Object.getPrototypeOf,h=Object.prototype.hasOwnProperty,j=Object.prototype.propertyIsEnumerable;var i=(a,b,c)=>b in a?g(a,b,{enumerable:!0,configurable:!0,writable:!0,value:c}):a[b]=c,r=(a,b)=>{for(var c in b||={})h.call(b,c)&&i(a,c,b[c]);if(f)for(var c of f(b))j.call(b,c)&&i(a,c,b[c]);return a},s=(a,b)=>l(a,n(b));var t=(a=>typeof require<"u"?require:typeof Proxy<"u"?new Proxy(a,{get:(b,c)=>(typeof require<"u"?require:b)[c]}):a)(function(a){if(typeof require<"u")return require.apply(this,arguments);throw Error('Dynamic require of "'+a+'" is not supported')});var u=(a,b)=>{var c={};for(var d in a)h.call(a,d)&&b.indexOf(d)<0&&(c[d]=a[d]);if(a!=null&&f)for(var d of f(a))b.indexOf(d)<0&&j.call(a,d)&&(c[d]=a[d]);return c};var v=(a,b)=>()=>(b||a((b={exports:{}}).exports,b),b.exports);var q=(a,b,c,d)=>{if(b&&typeof b=="object"||typeof b=="function")for(let e of o(b))!h.call(a,e)&&e!==c&&g(a,e,{get:()=>b[e],enumerable:!(d=m(b,e))||d.enumerable});return a};var w=(a,b,c)=>(c=a!=null?k(p(a)):{},q(b||!a||!a.__esModule?g(c,"default",{value:a,enumerable:!0}):c,a));export{r as a,s as b,t as c,u as d,v as e,w as f};
