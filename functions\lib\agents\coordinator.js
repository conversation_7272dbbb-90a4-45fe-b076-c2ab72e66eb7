"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoordinatorAgent = void 0;
const langchain_config_1 = require("../utils/langchain-config");
const memory_manager_1 = require("../utils/memory-manager");
const COORDINATOR_PROMPT = `You are the Coordinator Agent for a podcast-to-YouTube-description system.
Your role is to orchestrate other agents and manage the overall workflow.

Current context: {context}
User request: {input}

Decide which agents to activate and in what order. Respond with a JSON object containing:
- nextAgent: string (name of next agent to run)
- reasoning: string (why this agent should run next)
- parameters: object (parameters to pass to the agent)
- shouldAskUser: boolean (whether to ask user for input)`;
const COORDINATOR_SYSTEM_MESSAGE = "You are an intelligent workflow coordinator. Always respond with valid JSON.";
class CoordinatorAgent {
    static getInstance() {
        if (!this.instance) {
            this.instance = new CoordinatorAgent();
        }
        return this.instance;
    }
    async orchestrateWorkflow(sessionId, userInput, autoMode = false) {
        const startTime = new Date();
        try {
            // Update agent status
            const status = {
                name: 'coordinator',
                status: 'processing',
                progress: 0,
                message: 'Analyzing workflow requirements...',
                startTime
            };
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'coordinator', status);
            const context = memory_manager_1.MemoryManager.getContext(sessionId);
            if (!context) {
                throw new Error(`No context found for session ${sessionId}`);
            }
            // Determine next steps based on current state
            const nextAction = await this.determineNextAction(context, userInput, autoMode);
            // Update progress
            status.progress = 50;
            status.message = `Determined next action: ${nextAction.nextAgent}`;
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'coordinator', status);
            // Execute the determined action
            const result = await this.executeAction(context, nextAction);
            // Complete
            status.status = 'complete';
            status.progress = 100;
            status.message = `Workflow step completed: ${nextAction.nextAgent}`;
            status.endTime = new Date();
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'coordinator', status);
            return {
                success: true,
                data: result,
                status,
                updatedContext: context
            };
        }
        catch (error) {
            const errorStatus = {
                name: 'coordinator',
                status: 'error',
                progress: 0,
                message: `Error: ${error}`,
                startTime,
                endTime: new Date()
            };
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'coordinator', errorStatus);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
                status: errorStatus
            };
        }
    }
    async determineNextAction(context, userInput, autoMode = false) {
        const contextSummary = memory_manager_1.MemoryManager.getContextSummary(context.sessionId);
        const prompt = COORDINATOR_PROMPT
            .replace('{context}', JSON.stringify(contextSummary))
            .replace('{input}', userInput || 'Continue processing');
        const response = await langchain_config_1.LangChainConfig.generateResponse(prompt, context, COORDINATOR_SYSTEM_MESSAGE);
        try {
            return JSON.parse(response);
        }
        catch (error) {
            // Fallback logic if JSON parsing fails
            return this.getFallbackAction(context, userInput, autoMode);
        }
    }
    getFallbackAction(context, userInput, autoMode = false) {
        const { agentStatuses, transcript, currentDescription } = context;
        // If no transcript, start with transcription
        if (!transcript) {
            return {
                nextAgent: 'transcriber',
                reasoning: 'No transcript available, need to process audio',
                parameters: {},
                shouldAskUser: false
            };
        }
        // If transcript exists but no topics extracted
        if (!agentStatuses.topicExtractor || agentStatuses.topicExtractor.status !== 'complete') {
            return {
                nextAgent: 'topicExtractor',
                reasoning: 'Transcript available, need to extract topics',
                parameters: { transcript },
                shouldAskUser: false
            };
        }
        // If topics extracted but no links found
        if (!agentStatuses.linkFinder || agentStatuses.linkFinder.status !== 'complete') {
            return {
                nextAgent: 'linkFinder',
                reasoning: 'Topics extracted, need to find relevant links',
                parameters: { transcript },
                shouldAskUser: false
            };
        }
        // If links found but no profiles searched
        if (!agentStatuses.profileFinder || agentStatuses.profileFinder.status !== 'complete') {
            return {
                nextAgent: 'profileFinder',
                reasoning: 'Links found, need to search for participant profiles',
                parameters: { transcript },
                shouldAskUser: false
            };
        }
        // If profiles found but no description written
        if (!agentStatuses.descriptionWriter || agentStatuses.descriptionWriter.status !== 'complete') {
            return {
                nextAgent: 'descriptionWriter',
                reasoning: 'All data gathered, ready to write description',
                parameters: {
                    transcript,
                    topics: context.memory.topics,
                    resources: context.memory.resources,
                    profiles: context.memory.profiles
                },
                shouldAskUser: false
            };
        }
        // If user provided input, use editor
        if (userInput) {
            return {
                nextAgent: 'editor',
                reasoning: 'User requested changes to description',
                parameters: {
                    description: currentDescription,
                    userRequest: userInput
                },
                shouldAskUser: false
            };
        }
        // All done
        return {
            nextAgent: 'complete',
            reasoning: 'All processing steps completed',
            parameters: {},
            shouldAskUser: !autoMode
        };
    }
    async executeAction(context, action) {
        const { nextAgent, parameters } = action;
        switch (nextAgent) {
            case 'transcriber':
                const { TranscriberAgent } = await Promise.resolve().then(() => __importStar(require('./transcriber.js')));
                return await TranscriberAgent.getInstance().processAudio(context.sessionId, parameters);
            case 'topicExtractor':
                const { TopicExtractorAgent } = await Promise.resolve().then(() => __importStar(require('./topic-extractor.js')));
                return await TopicExtractorAgent.getInstance().extractTopics(context.sessionId, parameters);
            case 'linkFinder':
                const { LinkFinderAgent } = await Promise.resolve().then(() => __importStar(require('./link-finder.js')));
                return await LinkFinderAgent.getInstance().findLinks(context.sessionId, parameters);
            case 'profileFinder':
                const { ProfileFinderAgent } = await Promise.resolve().then(() => __importStar(require('./profile-finder.js')));
                return await ProfileFinderAgent.getInstance().findProfiles(context.sessionId, parameters);
            case 'descriptionWriter':
                const { DescriptionWriterAgent } = await Promise.resolve().then(() => __importStar(require('./description-writer.js')));
                return await DescriptionWriterAgent.getInstance().writeDescription(context.sessionId, parameters);
            case 'editor':
                const { EditorAgent } = await Promise.resolve().then(() => __importStar(require('./editor.js')));
                return await EditorAgent.getInstance().editDescription(context.sessionId, parameters);
            case 'complete':
                return {
                    message: 'Processing completed successfully',
                    description: context.currentDescription
                };
            default:
                throw new Error(`Unknown agent: ${nextAgent}`);
        }
    }
    async handleUserMessage(sessionId, message) {
        return await this.orchestrateWorkflow(sessionId, message, false);
    }
    async continueAutoMode(sessionId) {
        return await this.orchestrateWorkflow(sessionId, undefined, true);
    }
}
exports.CoordinatorAgent = CoordinatorAgent;
//# sourceMappingURL=coordinator.js.map