import { AgentContext, AgentResponse, AgentStatus, PodcastDescription } from '../types';
import { LangChainConfig, AGENT_PROMPTS, SYSTEM_MESSAGES } from '../utils/langchain-config';
import { MemoryManager } from '../utils/memory-manager';

export class EditorAgent {
  private static instance: EditorAgent;

  static getInstance(): EditorAgent {
    if (!this.instance) {
      this.instance = new EditorAgent();
    }
    return this.instance;
  }

  async editDescription(sessionId: string, parameters: any): Promise<AgentResponse> {
    const startTime = new Date();
    
    try {
      const status: AgentStatus = {
        name: 'editor',
        status: 'processing',
        progress: 0,
        message: 'Analyzing edit request...',
        startTime
      };
      
      MemoryManager.updateAgentStatus(sessionId, 'editor', status);

      const context = MemoryManager.getContext(sessionId);
      if (!context) {
        throw new Error(`No context found for session ${sessionId}`);
      }

      const currentDescription = parameters.description || context.currentDescription;
      const userRequest = parameters.userRequest;

      if (!currentDescription) {
        throw new Error('No description available to edit');
      }

      if (!userRequest) {
        throw new Error('No edit request provided');
      }

      // Update progress
      status.progress = 20;
      status.message = 'Understanding edit requirements...';
      MemoryManager.updateAgentStatus(sessionId, 'editor', status);

      // Analyze the edit request
      const editAnalysis = await this.analyzeEditRequest(userRequest, currentDescription, context);

      // Update progress
      status.progress = 40;
      status.message = `Applying changes: ${editAnalysis.action}...`;
      MemoryManager.updateAgentStatus(sessionId, 'editor', status);

      // Apply the edits
      const updatedDescription = await this.applyEdits(
        currentDescription, 
        userRequest, 
        editAnalysis, 
        context
      );

      // Update progress
      status.progress = 80;
      status.message = 'Validating changes...';
      MemoryManager.updateAgentStatus(sessionId, 'editor', status);

      // Validate the changes
      const validatedDescription = await this.validateChanges(
        currentDescription,
        updatedDescription,
        userRequest,
        context
      );

      // Update context with new description
      MemoryManager.updateContext(sessionId, {
        currentDescription: {
          ...validatedDescription,
          lastModified: new Date(),
          version: (currentDescription.version || 1) + 1
        }
      });

      // Complete
      status.status = 'complete';
      status.progress = 100;
      status.message = `Changes applied: ${editAnalysis.summary}`;
      status.endTime = new Date();
      MemoryManager.updateAgentStatus(sessionId, 'editor', status);

      return {
        success: true,
        data: {
          updatedDescription: validatedDescription,
          changes: editAnalysis,
          message: `I've ${editAnalysis.action}. ${editAnalysis.summary}`
        },
        status,
        updatedContext: MemoryManager.getContext(sessionId)
      };

    } catch (error) {
      const errorStatus: AgentStatus = {
        name: 'editor',
        status: 'error',
        progress: 0,
        message: `Edit failed: ${error}`,
        startTime,
        endTime: new Date()
      };
      
      MemoryManager.updateAgentStatus(sessionId, 'editor', errorStatus);

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown editing error',
        status: errorStatus
      };
    }
  }

  private async analyzeEditRequest(
    userRequest: string, 
    currentDescription: PodcastDescription, 
    context: AgentContext
  ): Promise<any> {
    const prompt = `Analyze this edit request for a YouTube description:

User request: "${userRequest}"

Current description sections:
- Overview: ${currentDescription.overview?.substring(0, 200)}...
- Guest bio: ${currentDescription.guestBio?.substring(0, 100)}...
- Host bio: ${currentDescription.hostBio?.substring(0, 100)}...
- Key topics: ${currentDescription.keyTopics?.join(', ')}
- Resources: ${currentDescription.resources?.length || 0} items
- Timestamps: ${currentDescription.timestamps?.length || 0} items
- Extended summary: ${currentDescription.extended?.substring(0, 200)}...
- SEO: ${currentDescription.seo?.keywords?.length || 0} keywords, ${currentDescription.seo?.hashtags?.length || 0} hashtags

Determine:
1. What specific action to take (e.g., "make punchier", "shorten", "add more detail", "remove hashtags")
2. Which sections to modify
3. The tone/style requested
4. Any specific requirements

Respond with JSON: {
  action: string,
  sections: string[],
  tone: string,
  requirements: string[],
  summary: string
}`;

    try {
      const response = await LangChainConfig.generateResponse(
        prompt,
        context,
        "You are an expert content editor. Analyze requests precisely. Respond only with valid JSON."
      );

      return JSON.parse(response);
    } catch (error) {
      // Fallback analysis
      return this.fallbackEditAnalysis(userRequest);
    }
  }

  private async applyEdits(
    currentDescription: PodcastDescription,
    userRequest: string,
    editAnalysis: any,
    context: AgentContext
  ): Promise<PodcastDescription> {
    const updatedDescription = { ...currentDescription };

    // Apply edits based on the analysis
    for (const section of editAnalysis.sections) {
      switch (section.toLowerCase()) {
        case 'overview':
          updatedDescription.overview = await this.editOverview(
            currentDescription.overview,
            userRequest,
            editAnalysis,
            context
          );
          break;

        case 'guestbio':
        case 'guest bio':
          updatedDescription.guestBio = await this.editBio(
            currentDescription.guestBio,
            'guest',
            userRequest,
            editAnalysis,
            context
          );
          break;

        case 'hostbio':
        case 'host bio':
          updatedDescription.hostBio = await this.editBio(
            currentDescription.hostBio,
            'host',
            userRequest,
            editAnalysis,
            context
          );
          break;

        case 'topics':
        case 'key topics':
          updatedDescription.keyTopics = await this.editTopics(
            currentDescription.keyTopics,
            userRequest,
            editAnalysis,
            context
          );
          break;

        case 'extended':
        case 'summary':
        case 'extended summary':
          updatedDescription.extended = await this.editExtended(
            currentDescription.extended,
            userRequest,
            editAnalysis,
            context
          );
          break;

        case 'seo':
        case 'hashtags':
        case 'keywords':
          updatedDescription.seo = await this.editSEO(
            currentDescription.seo,
            userRequest,
            editAnalysis,
            context
          );
          break;

        case 'timestamps':
          updatedDescription.timestamps = await this.editTimestamps(
            currentDescription.timestamps,
            userRequest,
            editAnalysis,
            context
          );
          break;
      }
    }

    return updatedDescription;
  }

  private async editOverview(
    currentOverview: string,
    userRequest: string,
    editAnalysis: any,
    context: AgentContext
  ): Promise<string> {
    const prompt = `Edit this YouTube description overview based on the user's request:

Current overview: "${currentOverview}"
User request: "${userRequest}"
Requested action: ${editAnalysis.action}
Tone: ${editAnalysis.tone}

Apply the requested changes while maintaining:
- Engaging, hook-worthy opening
- Clear value proposition
- Appropriate length (2-3 sentences)
- YouTube-optimized language

Return only the edited overview text.`;

    const response = await LangChainConfig.generateResponse(
      prompt,
      context,
      "You are an expert YouTube content editor. Return only the edited text."
    );

    return response.trim();
  }

  private async editBio(
    currentBio: string,
    type: 'guest' | 'host',
    userRequest: string,
    editAnalysis: any,
    context: AgentContext
  ): Promise<string> {
    const prompt = `Edit this ${type} bio based on the user's request:

Current ${type} bio: "${currentBio}"
User request: "${userRequest}"
Requested action: ${editAnalysis.action}

Apply the requested changes while maintaining:
- Professional tone
- Key credentials
- Appropriate length (1-2 sentences)
- Relevance to the podcast topic

Return only the edited bio text.`;

    const response = await LangChainConfig.generateResponse(
      prompt,
      context,
      "You are an expert content editor. Return only the edited text."
    );

    return response.trim();
  }

  private async editTopics(
    currentTopics: string[],
    userRequest: string,
    editAnalysis: any,
    context: AgentContext
  ): Promise<string[]> {
    const prompt = `Edit these key topics based on the user's request:

Current topics: ${currentTopics.join(', ')}
User request: "${userRequest}"
Requested action: ${editAnalysis.action}

Apply the requested changes while maintaining:
- Clear, specific topics
- Benefit-focused language
- Appropriate number of topics (5-8)
- Engaging descriptions

Respond with JSON array of edited topic strings.`;

    try {
      const response = await LangChainConfig.generateResponse(
        prompt,
        context,
        "You are an expert content editor. Respond only with valid JSON array."
      );

      const editedTopics = JSON.parse(response);
      return Array.isArray(editedTopics) ? editedTopics : currentTopics;
    } catch (error) {
      return currentTopics;
    }
  }

  private async editExtended(
    currentExtended: string,
    userRequest: string,
    editAnalysis: any,
    context: AgentContext
  ): Promise<string> {
    const prompt = `Edit this extended summary based on the user's request:

Current extended summary: "${currentExtended}"
User request: "${userRequest}"
Requested action: ${editAnalysis.action}

Apply the requested changes while maintaining:
- Detailed but engaging content
- Key insights and takeaways
- Natural, conversational tone
- Appropriate length for YouTube

Return only the edited extended summary text.`;

    const response = await LangChainConfig.generateResponse(
      prompt,
      context,
      "You are an expert content editor. Return only the edited text."
    );

    return response.trim();
  }

  private async editSEO(
    currentSEO: any,
    userRequest: string,
    editAnalysis: any,
    context: AgentContext
  ): Promise<any> {
    const prompt = `Edit these SEO elements based on the user's request:

Current SEO:
- Keywords: ${currentSEO?.keywords?.join(', ') || 'None'}
- Hashtags: ${currentSEO?.hashtags?.join(', ') || 'None'}
- Suggested title: ${currentSEO?.suggestedTitle || 'None'}

User request: "${userRequest}"
Requested action: ${editAnalysis.action}

Apply the requested changes while maintaining SEO best practices.

Respond with JSON: {keywords: string[], hashtags: string[], suggestedTitle: string}`;

    try {
      const response = await LangChainConfig.generateResponse(
        prompt,
        context,
        "You are an SEO expert. Respond only with valid JSON."
      );

      return JSON.parse(response);
    } catch (error) {
      return currentSEO;
    }
  }

  private async editTimestamps(
    currentTimestamps: any[],
    userRequest: string,
    editAnalysis: any,
    context: AgentContext
  ): Promise<any[]> {
    // For now, return current timestamps as timestamp editing is complex
    // In a full implementation, this would analyze and modify timestamps
    return currentTimestamps;
  }

  private async validateChanges(
    originalDescription: PodcastDescription,
    updatedDescription: PodcastDescription,
    userRequest: string,
    context: AgentContext
  ): Promise<PodcastDescription> {
    // Basic validation - ensure no critical information was lost
    const validated = { ...updatedDescription };

    // Ensure essential fields are not empty
    if (!validated.overview && originalDescription.overview) {
      validated.overview = originalDescription.overview;
    }

    if (!validated.keyTopics || validated.keyTopics.length === 0) {
      validated.keyTopics = originalDescription.keyTopics;
    }

    return validated;
  }

  private fallbackEditAnalysis(userRequest: string): any {
    const request = userRequest.toLowerCase();
    
    let action = 'modify content';
    let sections = ['overview'];
    let tone = 'engaging';
    
    if (request.includes('punchy') || request.includes('hook')) {
      action = 'make more engaging and punchy';
      sections = ['overview'];
      tone = 'punchy';
    } else if (request.includes('shorten') || request.includes('shorter')) {
      action = 'shorten content';
      tone = 'concise';
    } else if (request.includes('hashtag')) {
      action = 'modify hashtags';
      sections = ['seo'];
    } else if (request.includes('topic')) {
      action = 'modify topics';
      sections = ['topics'];
    }

    return {
      action,
      sections,
      tone,
      requirements: [],
      summary: `Applied ${action} as requested`
    };
  }
}
