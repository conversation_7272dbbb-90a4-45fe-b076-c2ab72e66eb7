import { z } from "zod";
import { InferInteropZodOutput } from "@langchain/core/utils/types";
import { GmailBaseTool, GmailBaseToolParams } from "./base.js";
export declare class G<PERSON><PERSON>reate<PERSON>raft extends GmailBaseTool {
    name: string;
    schema: z.ZodObject<{
        message: z.ZodString;
        to: z.Z<PERSON><z.ZodString, "many">;
        subject: z.ZodString;
        cc: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
        bcc: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, "strip", z.ZodTypeAny, {
        message: string;
        subject: string;
        to: string[];
        cc?: string[] | undefined;
        bcc?: string[] | undefined;
    }, {
        message: string;
        subject: string;
        to: string[];
        cc?: string[] | undefined;
        bcc?: string[] | undefined;
    }>;
    description: string;
    constructor(fields?: GmailBaseToolParams);
    private prepareDraftMessage;
    _call(arg: InferInteropZodOutput<typeof this.schema>): Promise<string>;
}
export type CreateDraftSchema = {
    message: string;
    to: string[];
    subject: string;
    cc?: string[];
    bcc?: string[];
};
