import { LLM, type BaseLLMParams } from "@langchain/core/language_models/llms";
/**
 * Type definition for AI21 penalty data.
 */
export type AI21PenaltyData = {
    scale: number;
    applyToWhitespaces: boolean;
    applyToPunctuations: boolean;
    applyToNumbers: boolean;
    applyToStopwords: boolean;
    applyToEmojis: boolean;
};
/**
 * Interface for AI21 input parameters.
 */
export interface AI21Input extends BaseLLMParams {
    ai21ApiKey?: string;
    model?: string;
    temperature?: number;
    minTokens?: number;
    maxTokens?: number;
    topP?: number;
    presencePenalty?: AI21PenaltyData;
    countPenalty?: AI21PenaltyData;
    frequencyPenalty?: AI21PenaltyData;
    numResults?: number;
    logitBias?: Record<string, number>;
    stop?: string[];
    baseUrl?: string;
}
/**
 * Class representing the AI21 language model. It extends the LLM (Large
 * Language Model) class, providing a standard interface for interacting
 * with the AI21 language model.
 */
export declare class AI21 extends LLM implements AI21Input {
    lc_serializable: boolean;
    model: string;
    temperature: number;
    maxTokens: number;
    minTokens: number;
    topP: number;
    presencePenalty: AI21PenaltyData;
    countPenalty: AI21PenaltyData;
    frequencyPenalty: AI21PenaltyData;
    numResults: number;
    logitBias?: Record<string, number>;
    ai21ApiKey?: string;
    stop?: string[];
    baseUrl?: string;
    constructor(fields?: AI21Input);
    /**
     * Method to validate the environment. It checks if the AI21 API key is
     * set. If not, it throws an error.
     */
    validateEnvironment(): void;
    /**
     * Static method to get the default penalty data for AI21.
     * @returns AI21PenaltyData
     */
    static getDefaultAI21PenaltyData(): AI21PenaltyData;
    /** Get the type of LLM. */
    _llmType(): string;
    /** Get the default parameters for calling AI21 API. */
    get defaultParams(): {
        temperature: number;
        maxTokens: number;
        minTokens: number;
        topP: number;
        presencePenalty: AI21PenaltyData;
        countPenalty: AI21PenaltyData;
        frequencyPenalty: AI21PenaltyData;
        numResults: number;
        logitBias: Record<string, number> | undefined;
    };
    /** Get the identifying parameters for this LLM. */
    get identifyingParams(): {
        model: string;
        temperature: number;
        maxTokens: number;
        minTokens: number;
        topP: number;
        presencePenalty: AI21PenaltyData;
        countPenalty: AI21PenaltyData;
        frequencyPenalty: AI21PenaltyData;
        numResults: number;
        logitBias: Record<string, number> | undefined;
    };
    /** Call out to AI21's complete endpoint.
     Args:
         prompt: The prompt to pass into the model.
     stop: Optional list of stop words to use when generating.
  
         Returns:
     The string generated by the model.
  
     Example:
     let response = ai21._call("Tell me a joke.");
     */
    _call(prompt: string, options: this["ParsedCallOptions"]): Promise<string>;
}
