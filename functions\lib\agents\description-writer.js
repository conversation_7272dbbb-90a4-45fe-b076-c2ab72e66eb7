"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DescriptionWriterAgent = void 0;
const langchain_config_1 = require("../utils/langchain-config");
const memory_manager_1 = require("../utils/memory-manager");
class DescriptionWriterAgent {
    static getInstance() {
        if (!this.instance) {
            this.instance = new DescriptionWriterAgent();
        }
        return this.instance;
    }
    async writeDescription(sessionId, parameters) {
        const startTime = new Date();
        try {
            const status = {
                name: 'descriptionWriter',
                status: 'processing',
                progress: 0,
                message: 'Gathering all data for description...',
                startTime
            };
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'descriptionWriter', status);
            const context = memory_manager_1.MemoryManager.getContext(sessionId);
            if (!context) {
                throw new Error(`No context found for session ${sessionId}`);
            }
            // Gather all available data
            const transcript = parameters.transcript || context.transcript;
            const topics = context.memory.topics || parameters.topics;
            const resources = context.memory.resources || parameters.resources;
            const profiles = context.memory.profiles || parameters.profiles;
            const participants = context.memory.participants || [];
            if (!transcript) {
                throw new Error('No transcript available for description writing');
            }
            // Update progress
            status.progress = 15;
            status.message = 'Writing engaging overview...';
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'descriptionWriter', status);
            // Generate overview
            const overview = await this.generateOverview(transcript, topics, context);
            // Update progress
            status.progress = 30;
            status.message = 'Creating participant bios...';
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'descriptionWriter', status);
            // Generate bios
            const { guestBio, hostBio } = await this.generateBios(participants, profiles, transcript, context);
            // Update progress
            status.progress = 45;
            status.message = 'Organizing key topics...';
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'descriptionWriter', status);
            // Format key topics
            const keyTopics = await this.formatKeyTopics(topics, transcript, context);
            // Update progress
            status.progress = 60;
            status.message = 'Generating timestamps...';
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'descriptionWriter', status);
            // Generate timestamps
            const timestamps = await this.generateTimestamps(transcript, topics, context);
            // Update progress
            status.progress = 75;
            status.message = 'Writing extended summary...';
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'descriptionWriter', status);
            // Generate extended summary
            const extended = await this.generateExtendedSummary(transcript, topics, overview, context);
            // Update progress
            status.progress = 90;
            status.message = 'Optimizing SEO elements...';
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'descriptionWriter', status);
            // Generate SEO data
            const seo = await this.generateSEO(transcript, topics, overview, context);
            // Compile final description
            const description = {
                guestBio,
                hostBio,
                overview,
                keyTopics,
                resources: resources || [],
                timestamps,
                extended,
                seo,
                lastModified: new Date(),
                version: 1
            };
            // Store in context
            memory_manager_1.MemoryManager.updateContext(sessionId, {
                currentDescription: description
            });
            // Complete
            status.status = 'complete';
            status.progress = 100;
            status.message = 'YouTube description generated successfully!';
            status.endTime = new Date();
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'descriptionWriter', status);
            const updatedContext = memory_manager_1.MemoryManager.getContext(sessionId);
            return {
                success: true,
                data: description,
                status,
                updatedContext: updatedContext || undefined
            };
        }
        catch (error) {
            const errorStatus = {
                name: 'descriptionWriter',
                status: 'error',
                progress: 0,
                message: `Description writing failed: ${error}`,
                startTime,
                endTime: new Date()
            };
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'descriptionWriter', errorStatus);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown description writing error',
                status: errorStatus
            };
        }
    }
    async generateOverview(transcript, topics, context) {
        const topicsList = (topics === null || topics === void 0 ? void 0 : topics.topics) || [];
        const prompt = `Create an engaging YouTube description overview for this podcast:

Transcript sample: ${transcript.substring(0, 3000)}...
Main topics: ${topicsList.join(', ')}

Write a compelling 2-3 sentence overview that:
- Hooks viewers immediately
- Summarizes the main value/insights
- Makes people want to watch
- Uses engaging, conversational tone
- Includes key topics naturally

Focus on the value viewers will get from watching.`;
        const response = await langchain_config_1.LangChainConfig.generateResponse(prompt, context, "You are an expert YouTube content creator. Write engaging, click-worthy descriptions.");
        return response.trim();
    }
    async generateBios(participants, profiles, transcript, context) {
        var _a, _b;
        const prompt = `Create professional bios for the podcast participants:

Participants: ${JSON.stringify(participants)}
Profiles found: ${JSON.stringify((profiles === null || profiles === void 0 ? void 0 : profiles.slice(0, 5)) || [])}
Transcript context: ${transcript.substring(0, 2000)}...

For each participant, write a concise bio (1-2 sentences) that includes:
- Name and primary role/title
- Key credentials or achievements
- Why they're qualified to discuss the topic
- Company/organization if relevant

Identify who is the host vs guest(s) based on the conversation flow.

Respond with JSON: {guestBio: string, hostBio: string}`;
        try {
            const response = await langchain_config_1.LangChainConfig.generateResponse(prompt, context, "You are an expert at writing professional bios. Respond only with valid JSON.");
            const bios = JSON.parse(response);
            return {
                guestBio: bios.guestBio || '',
                hostBio: bios.hostBio || ''
            };
        }
        catch (error) {
            return {
                guestBio: ((_a = participants.find(p => p.role === 'guest')) === null || _a === void 0 ? void 0 : _a.name) ?
                    `${participants.find(p => p.role === 'guest').name} shares insights on today's topic.` : '',
                hostBio: ((_b = participants.find(p => p.role === 'host')) === null || _b === void 0 ? void 0 : _b.name) ?
                    `Hosted by ${participants.find(p => p.role === 'host').name}.` : ''
            };
        }
    }
    async formatKeyTopics(topics, transcript, context) {
        const topicsList = (topics === null || topics === void 0 ? void 0 : topics.topics) || [];
        if (topicsList.length === 0) {
            return [];
        }
        const prompt = `Refine these podcast topics for a YouTube description:

Original topics: ${topicsList.join(', ')}
Transcript context: ${transcript.substring(0, 2000)}...

Rewrite each topic to be:
- Clear and specific
- Benefit-focused (what viewers learn)
- Engaging and clickable
- 5-8 words max each

Respond with JSON array of refined topic strings.`;
        try {
            const response = await langchain_config_1.LangChainConfig.generateResponse(prompt, context, "You are an expert at writing engaging topic descriptions. Respond only with valid JSON array.");
            const refinedTopics = JSON.parse(response);
            return Array.isArray(refinedTopics) ? refinedTopics : topicsList;
        }
        catch (error) {
            return topicsList;
        }
    }
    async generateTimestamps(transcript, topics, context) {
        var _a;
        const prompt = `Generate YouTube timestamps for this podcast:

Transcript: ${transcript.substring(0, 4000)}...
Topics discussed: ${((_a = topics === null || topics === void 0 ? void 0 : topics.topics) === null || _a === void 0 ? void 0 : _a.join(', ')) || 'Various topics'}

Create 5-8 timestamps that mark:
- Introduction/welcome
- Major topic transitions
- Key insights or revelations
- Important discussions
- Conclusion/wrap-up

Each timestamp should:
- Be in MM:SS format
- Have a clear, descriptive label
- Mark genuinely interesting moments
- Help viewers navigate the content

Respond with JSON array: [{time: "MM:SS", label: "Description", confidence: 0.8}]`;
        try {
            const response = await langchain_config_1.LangChainConfig.generateResponse(prompt, context, "You are an expert at creating YouTube timestamps. Respond only with valid JSON array.");
            const timestamps = JSON.parse(response);
            return Array.isArray(timestamps) ? timestamps : [];
        }
        catch (error) {
            // Fallback: generate basic timestamps
            return [
                { time: "00:00", label: "Introduction", confidence: 0.9 },
                { time: "05:00", label: "Main Discussion", confidence: 0.8 },
                { time: "15:00", label: "Key Insights", confidence: 0.7 },
                { time: "25:00", label: "Wrap-up", confidence: 0.8 }
            ];
        }
    }
    async generateExtendedSummary(transcript, topics, overview, context) {
        var _a;
        const prompt = `Write an extended summary for this podcast YouTube description:

Overview: ${overview}
Topics: ${((_a = topics === null || topics === void 0 ? void 0 : topics.topics) === null || _a === void 0 ? void 0 : _a.join(', ')) || 'Various topics'}
Transcript sample: ${transcript.substring(0, 3000)}...

Write a detailed summary (3-4 sentences) that:
- Expands on the overview
- Highlights key insights and takeaways
- Mentions specific valuable information discussed
- Encourages engagement and watching
- Uses natural, conversational tone

This will appear in the "Show more" section of YouTube.`;
        const response = await langchain_config_1.LangChainConfig.generateResponse(prompt, context, "You are an expert YouTube content creator. Write detailed, engaging summaries.");
        return response.trim();
    }
    async generateSEO(transcript, topics, overview, context) {
        var _a, _b;
        const prompt = `Generate SEO elements for this podcast YouTube description:

Overview: ${overview}
Topics: ${((_a = topics === null || topics === void 0 ? void 0 : topics.topics) === null || _a === void 0 ? void 0 : _a.join(', ')) || 'Various topics'}
Keywords from content: ${((_b = topics === null || topics === void 0 ? void 0 : topics.keywords) === null || _b === void 0 ? void 0 : _b.join(', ')) || ''}

Generate:
1. 10-15 SEO keywords (mix of broad and long-tail)
2. 8-12 relevant hashtags
3. A suggested video title (60 chars max)

Focus on:
- Search terms people would use
- Trending topics in the niche
- Specific terminology mentioned
- Discoverable phrases

Respond with JSON: {keywords: string[], hashtags: string[], suggestedTitle: string}`;
        try {
            const response = await langchain_config_1.LangChainConfig.generateResponse(prompt, context, "You are an SEO expert for YouTube content. Respond only with valid JSON.");
            const seo = JSON.parse(response);
            return {
                keywords: seo.keywords || [],
                hashtags: seo.hashtags || [],
                suggestedTitle: seo.suggestedTitle || 'Podcast Episode'
            };
        }
        catch (error) {
            return {
                keywords: (topics === null || topics === void 0 ? void 0 : topics.keywords) || [],
                hashtags: ['#podcast', '#interview', '#insights'],
                suggestedTitle: 'Podcast Episode'
            };
        }
    }
}
exports.DescriptionWriterAgent = DescriptionWriterAgent;
//# sourceMappingURL=description-writer.js.map