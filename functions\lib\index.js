"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.healthCheck = exports.cleanupOldSessions = exports.continueAutoMode = exports.getSessionStatus = exports.chatWithAgent = exports.processAudio = void 0;
const functions = __importStar(require("firebase-functions"));
const admin = __importStar(require("firebase-admin"));
const cors_1 = __importDefault(require("cors"));
const coordinator_1 = require("./agents/coordinator");
const memory_manager_1 = require("./utils/memory-manager");
const langchain_config_1 = require("./utils/langchain-config");
// Initialize Firebase Admin
admin.initializeApp();
// Initialize LangChain
langchain_config_1.LangChainConfig.initialize();
// CORS configuration
const corsHandler = (0, cors_1.default)({ origin: true });
// Main processing function - triggered when a new session is created
exports.processAudio = functions.https.onCall(async (data, context) => {
    try {
        // Verify authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const { sessionId, audioFileUrl, userId, autoMode = false } = data;
        // Validate input
        if (!sessionId || !audioFileUrl || !userId) {
            throw new functions.https.HttpsError('invalid-argument', 'Missing required parameters');
        }
        // Verify user owns the session
        if (context.auth.uid !== userId) {
            throw new functions.https.HttpsError('permission-denied', 'User can only process their own sessions');
        }
        // Initialize context
        const agentContext = memory_manager_1.MemoryManager.createInitialContext(sessionId, userId);
        memory_manager_1.MemoryManager.addMemory(sessionId, 'audioFileUrl', audioFileUrl);
        // Start the coordinator
        const coordinator = coordinator_1.CoordinatorAgent.getInstance();
        const result = await coordinator.orchestrateWorkflow(sessionId, undefined, autoMode);
        // Update Firestore with the result
        await admin.firestore()
            .collection('sessions')
            .doc(sessionId)
            .update({
            status: result.success ? 'processing' : 'error',
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
            agentStatuses: agentContext.agentStatuses
        });
        return {
            success: result.success,
            sessionId,
            message: result.success ? 'Processing started' : result.error,
            agentStatus: result.status
        };
    }
    catch (error) {
        console.error('Error in processAudio:', error);
        throw new functions.https.HttpsError('internal', 'Processing failed');
    }
});
// Chat function - handles user messages and description editing
exports.chatWithAgent = functions.https.onCall(async (data, context) => {
    var _a, _b;
    try {
        // Verify authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const { sessionId, message, userId } = data;
        // Validate input
        if (!sessionId || !message || !userId) {
            throw new functions.https.HttpsError('invalid-argument', 'Missing required parameters');
        }
        // Verify user owns the session
        if (context.auth.uid !== userId) {
            throw new functions.https.HttpsError('permission-denied', 'User can only chat with their own sessions');
        }
        // Get session context
        const agentContext = memory_manager_1.MemoryManager.getContext(sessionId);
        if (!agentContext) {
            throw new functions.https.HttpsError('not-found', 'Session not found');
        }
        // Process the message with coordinator
        const coordinator = coordinator_1.CoordinatorAgent.getInstance();
        const result = await coordinator.handleUserMessage(sessionId, message);
        // Update Firestore with chat history
        const sessionRef = admin.firestore().collection('sessions').doc(sessionId);
        await sessionRef.update({
            chatHistory: admin.firestore.FieldValue.arrayUnion({
                id: Date.now().toString(),
                role: 'user',
                content: message,
                timestamp: admin.firestore.FieldValue.serverTimestamp()
            }),
            updatedAt: admin.firestore.FieldValue.serverTimestamp()
        });
        // Add AI response to chat history
        if (result.success && result.data) {
            await sessionRef.update({
                chatHistory: admin.firestore.FieldValue.arrayUnion({
                    id: (Date.now() + 1).toString(),
                    role: 'assistant',
                    content: result.data.message || 'Processing your request...',
                    timestamp: admin.firestore.FieldValue.serverTimestamp()
                })
            });
        }
        return {
            success: result.success,
            message: ((_a = result.data) === null || _a === void 0 ? void 0 : _a.message) || 'Message processed',
            updatedDescription: (_b = result.updatedContext) === null || _b === void 0 ? void 0 : _b.currentDescription,
            agentStatus: result.status
        };
    }
    catch (error) {
        console.error('Error in chatWithAgent:', error);
        throw new functions.https.HttpsError('internal', 'Chat processing failed');
    }
});
// Get session status
exports.getSessionStatus = functions.https.onCall(async (data, context) => {
    try {
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const { sessionId } = data;
        if (!sessionId) {
            throw new functions.https.HttpsError('invalid-argument', 'Session ID required');
        }
        // Get session from Firestore
        const sessionDoc = await admin.firestore()
            .collection('sessions')
            .doc(sessionId)
            .get();
        if (!sessionDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Session not found');
        }
        const sessionData = sessionDoc.data();
        // Verify user owns the session
        if ((sessionData === null || sessionData === void 0 ? void 0 : sessionData.userId) !== context.auth.uid) {
            throw new functions.https.HttpsError('permission-denied', 'Access denied');
        }
        // Get agent context if available
        const agentContext = memory_manager_1.MemoryManager.getContext(sessionId);
        const contextSummary = agentContext ? memory_manager_1.MemoryManager.getContextSummary(sessionId) : null;
        return {
            session: sessionData,
            agentContext: contextSummary,
            agentStatuses: (agentContext === null || agentContext === void 0 ? void 0 : agentContext.agentStatuses) || {}
        };
    }
    catch (error) {
        console.error('Error in getSessionStatus:', error);
        throw new functions.https.HttpsError('internal', 'Failed to get session status');
    }
});
// Continue auto mode processing
exports.continueAutoMode = functions.https.onCall(async (data, context) => {
    var _a;
    try {
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const { sessionId } = data;
        if (!sessionId) {
            throw new functions.https.HttpsError('invalid-argument', 'Session ID required');
        }
        // Verify session ownership
        const sessionDoc = await admin.firestore()
            .collection('sessions')
            .doc(sessionId)
            .get();
        if (!sessionDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Session not found');
        }
        const sessionData = sessionDoc.data();
        if ((sessionData === null || sessionData === void 0 ? void 0 : sessionData.userId) !== context.auth.uid) {
            throw new functions.https.HttpsError('permission-denied', 'Access denied');
        }
        // Continue auto mode processing
        const coordinator = coordinator_1.CoordinatorAgent.getInstance();
        const result = await coordinator.continueAutoMode(sessionId);
        return {
            success: result.success,
            message: ((_a = result.data) === null || _a === void 0 ? void 0 : _a.message) || 'Auto mode continued',
            agentStatus: result.status
        };
    }
    catch (error) {
        console.error('Error in continueAutoMode:', error);
        throw new functions.https.HttpsError('internal', 'Auto mode continuation failed');
    }
});
// Cleanup function - runs periodically to clean up old contexts
exports.cleanupOldSessions = functions.pubsub
    .schedule('every 24 hours')
    .onRun(async (context) => {
    try {
        const cleaned = memory_manager_1.MemoryManager.cleanup(24); // Clean up contexts older than 24 hours
        console.log(`Cleaned up ${cleaned} old session contexts`);
        return null;
    }
    catch (error) {
        console.error('Error in cleanup:', error);
        return null;
    }
});
// HTTP endpoint for health check
exports.healthCheck = functions.https.onRequest((req, res) => {
    corsHandler(req, res, () => {
        res.json({
            status: 'healthy',
            timestamp: new Date().toISOString(),
            version: '1.0.0'
        });
    });
});
//# sourceMappingURL=index.js.map