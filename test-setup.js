#!/usr/bin/env node

/**
 * Pedma AI - Setup Validation Script
 * Run this script to validate your local development setup
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 Pedma AI - Setup Validation\n');

// Check if required files exist
const requiredFiles = [
  '.env',
  'functions/.env',
  'src/environments/environment.ts',
  'firebase.json',
  'functions/lib/index.js'
];

console.log('📁 Checking required files...');
let missingFiles = [];

requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - MISSING`);
    missingFiles.push(file);
  }
});

// Check environment variables
console.log('\n🔑 Checking environment variables...');

function checkEnvFile(filePath, requiredVars) {
  if (!fs.existsSync(filePath)) {
    console.log(`❌ ${filePath} not found`);
    return false;
  }

  const envContent = fs.readFileSync(filePath, 'utf8');
  let allPresent = true;

  requiredVars.forEach(varName => {
    if (envContent.includes(`${varName}=`) && !envContent.includes(`${varName}=your_`)) {
      console.log(`✅ ${varName} (in ${filePath})`);
    } else {
      console.log(`❌ ${varName} - NOT SET (in ${filePath})`);
      allPresent = false;
    }
  });

  return allPresent;
}

const mainEnvVars = [
  'FIREBASE_API_KEY',
  'FIREBASE_PROJECT_ID',
  'GROQ_API_KEY',
  'DEEPGRAM_API_KEY'
];

const functionsEnvVars = [
  'GROQ_API_KEY',
  'DEEPGRAM_API_KEY'
];

const mainEnvOk = checkEnvFile('.env', mainEnvVars);
const functionsEnvOk = checkEnvFile('functions/.env', functionsEnvVars);

// Check if functions are built
console.log('\n🔨 Checking build status...');
if (fs.existsSync('functions/lib/index.js')) {
  console.log('✅ Firebase Functions compiled');
} else {
  console.log('❌ Firebase Functions not compiled - run: cd functions && npx tsc');
}

// Check package installations
console.log('\n📦 Checking dependencies...');
if (fs.existsSync('node_modules')) {
  console.log('✅ Main dependencies installed');
} else {
  console.log('❌ Main dependencies missing - run: npm install');
}

if (fs.existsSync('functions/node_modules')) {
  console.log('✅ Functions dependencies installed');
} else {
  console.log('❌ Functions dependencies missing - run: cd functions && npm install');
}

// Summary
console.log('\n📊 Setup Summary:');
const issues = [];

if (missingFiles.length > 0) {
  issues.push(`Missing files: ${missingFiles.join(', ')}`);
}

if (!mainEnvOk) {
  issues.push('Main .env file needs configuration');
}

if (!functionsEnvOk) {
  issues.push('Functions .env file needs configuration');
}

if (!fs.existsSync('functions/lib/index.js')) {
  issues.push('Functions need to be compiled');
}

if (issues.length === 0) {
  console.log('🎉 Setup looks good! Ready to start development.');
  console.log('\nNext steps:');
  console.log('1. Run: firebase emulators:start');
  console.log('2. In another terminal: ng serve');
  console.log('3. Open: http://localhost:4200');
} else {
  console.log('⚠️  Issues found:');
  issues.forEach(issue => console.log(`   - ${issue}`));
  console.log('\nPlease fix these issues before starting development.');
  console.log('See dev-setup.md for detailed instructions.');
}

console.log('\n🔗 Useful URLs (when running):');
console.log('   - Main App: http://localhost:4200');
console.log('   - Firebase Emulator UI: http://localhost:4000');
console.log('   - Functions: http://localhost:5001');
console.log('\n📚 Documentation: dev-setup.md');
