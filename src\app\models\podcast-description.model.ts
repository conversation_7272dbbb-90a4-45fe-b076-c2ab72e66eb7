export interface PodcastDescription {
  guestBio: string;
  hostBio: string;
  overview: string;
  keyTopics: string[];
  resources: ResourceLink[];
  timestamps: Timestamp[];
  extended: string;
  seo: SEOData;
  lastModified: Date;
  version: number;
}

export interface ResourceLink {
  label: string;
  url: string;
  type: 'website' | 'book' | 'tool' | 'social' | 'product';
  confidence: number;
  extractedFrom: string; // transcript segment
}

export interface Timestamp {
  time: string; // "MM:SS" format
  label: string;
  confidence: number;
}

export interface SEOData {
  keywords: string[];
  hashtags: string[];
  suggestedTitle: string;
}

export interface UserAPIKeys {
  openai?: string;
  deepgram?: string;
  serpapi?: string;
  anthropic?: string;
}

export interface ProcessingSession {
  id: string;
  userId: string;
  audioFileUrl: string;
  transcript: string;
  description: PodcastDescription;
  chatHistory: ChatMessage[];
  status: 'uploading' | 'transcribing' | 'processing' | 'ready' | 'error';
  createdAt: Date;
  updatedAt: Date;
}

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  type?: 'text' | 'system' | 'error';
}

export interface User {
  uid: string;
  email: string;
  displayName?: string;
  photoURL?: string;
  apiKeys: UserAPIKeys;
  preferences: UserPreferences;
  createdAt: Date;
  lastLoginAt: Date;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  autoMode: boolean;
  defaultTemplate?: string;
  notifications: {
    email: boolean;
    browser: boolean;
  };
}

export interface AgentStatus {
  name: string;
  status: 'idle' | 'processing' | 'complete' | 'error';
  progress: number;
  message?: string;
  startTime?: Date;
  endTime?: Date;
}
