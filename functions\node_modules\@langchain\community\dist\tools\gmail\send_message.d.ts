import { z } from "zod";
import { InferInteropZodOutput } from "@langchain/core/utils/types";
import { GmailBaseTool, GmailBaseToolParams } from "./base.js";
export declare class GmailSendMessage extends GmailBaseTool {
    name: string;
    schema: z.ZodObject<{
        message: z.ZodString;
        to: z.Z<PERSON><z.ZodString, "many">;
        subject: z.ZodString;
        cc: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
        bcc: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, "strip", z.ZodTypeAny, {
        message: string;
        subject: string;
        to: string[];
        cc?: string[] | undefined;
        bcc?: string[] | undefined;
    }, {
        message: string;
        subject: string;
        to: string[];
        cc?: string[] | undefined;
        bcc?: string[] | undefined;
    }>;
    description: string;
    constructor(fields?: GmailBaseToolParams);
    private createEmailMessage;
    _call({ message, to, subject, cc, bcc, }: InferInteropZodOutput<typeof this.schema>): Promise<string>;
}
export type SendMessageSchema = {
    message: string;
    to: string[];
    subject: string;
    cc?: string[];
    bcc?: string[];
};
