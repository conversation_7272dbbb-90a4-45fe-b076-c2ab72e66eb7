rules_version = '2';

service firebase.storage {
  match /b/{bucket}/o {
    // Development: Allow all authenticated users to read/write
    // TODO: Restrict in production
    match /{allPaths=**} {
      allow read, write: if request.auth != null;
    }

    // Audio files can only be accessed by the owner
    match /audio-files/{userId}/{sessionId}/{fileName} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow delete: if request.auth != null && request.auth.uid == userId;
    }
    
    // Transcripts can only be accessed by the owner
    match /transcripts/{userId}/{sessionId}/{fileName} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow delete: if request.auth != null && request.auth.uid == userId;
    }
    
    // Processed descriptions can only be accessed by the owner
    match /descriptions/{userId}/{sessionId}/{fileName} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow delete: if request.auth != null && request.auth.uid == userId;
    }
    
    // User profile images
    match /profile-images/{userId}/{fileName} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId;
      allow delete: if request.auth != null && request.auth.uid == userId;
    }
    
    // Temporary files (auto-cleanup after 24 hours)
    match /temp/{userId}/{fileName} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow delete: if request.auth != null && request.auth.uid == userId;
    }
    
    // Public assets (read-only)
    match /public/{fileName} {
      allow read: if true;
      allow write: if false; // Only admins via server-side
    }
  }
}
