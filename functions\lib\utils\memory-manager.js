"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MemoryManager = void 0;
class MemoryManager {
    static getContext(sessionId) {
        return this.contexts.get(sessionId) || null;
    }
    static setContext(sessionId, context) {
        this.contexts.set(sessionId, context);
    }
    static updateContext(sessionId, updates) {
        const existing = this.getContext(sessionId);
        if (!existing) {
            throw new Error(`No context found for session ${sessionId}`);
        }
        const updated = Object.assign(Object.assign(Object.assign({}, existing), updates), { memory: Object.assign(Object.assign({}, existing.memory), updates.memory), agentStatuses: Object.assign(Object.assign({}, existing.agentStatuses), updates.agentStatuses) });
        this.setContext(sessionId, updated);
        return updated;
    }
    static updateAgentStatus(sessionId, agentName, status) {
        const context = this.getContext(sessionId);
        if (context) {
            context.agentStatuses[agentName] = status;
            this.setContext(sessionId, context);
        }
    }
    static getAgentStatus(sessionId, agentName) {
        const context = this.getContext(sessionId);
        return (context === null || context === void 0 ? void 0 : context.agentStatuses[agentName]) || null;
    }
    static addMemory(sessionId, key, value) {
        const context = this.getContext(sessionId);
        if (context) {
            context.memory[key] = value;
            this.setContext(sessionId, context);
        }
    }
    static getMemory(sessionId, key) {
        const context = this.getContext(sessionId);
        return context === null || context === void 0 ? void 0 : context.memory[key];
    }
    static clearContext(sessionId) {
        this.contexts.delete(sessionId);
    }
    static createInitialContext(sessionId, userId, transcript = '') {
        const context = {
            sessionId,
            userId,
            transcript,
            currentDescription: {},
            agentStatuses: {},
            memory: {}
        };
        this.setContext(sessionId, context);
        return context;
    }
    static getAllActiveSessions() {
        return Array.from(this.contexts.keys());
    }
    static getContextSummary(sessionId) {
        var _a;
        const context = this.getContext(sessionId);
        if (!context)
            return null;
        return {
            sessionId: context.sessionId,
            userId: context.userId,
            hasTranscript: !!context.transcript,
            transcriptLength: ((_a = context.transcript) === null || _a === void 0 ? void 0 : _a.length) || 0,
            descriptionProgress: Object.keys(context.currentDescription).length,
            activeAgents: Object.keys(context.agentStatuses).filter(agent => context.agentStatuses[agent].status === 'processing'),
            completedAgents: Object.keys(context.agentStatuses).filter(agent => context.agentStatuses[agent].status === 'complete'),
            memoryKeys: Object.keys(context.memory)
        };
    }
    static exportContext(sessionId) {
        return this.getContext(sessionId);
    }
    static importContext(sessionId, context) {
        this.setContext(sessionId, context);
    }
    // Cleanup old contexts (call periodically)
    static cleanup(maxAgeHours = 24) {
        const cutoffTime = Date.now() - (maxAgeHours * 60 * 60 * 1000);
        let cleaned = 0;
        for (const [sessionId, context] of this.contexts.entries()) {
            // Check if any agent has been active recently
            const lastActivity = Math.max(...Object.values(context.agentStatuses)
                .map(status => { var _a, _b; return ((_a = status.endTime) === null || _a === void 0 ? void 0 : _a.getTime()) || ((_b = status.startTime) === null || _b === void 0 ? void 0 : _b.getTime()) || 0; }));
            if (lastActivity < cutoffTime) {
                this.contexts.delete(sessionId);
                cleaned++;
            }
        }
        return cleaned;
    }
}
exports.MemoryManager = MemoryManager;
MemoryManager.contexts = new Map();
//# sourceMappingURL=memory-manager.js.map