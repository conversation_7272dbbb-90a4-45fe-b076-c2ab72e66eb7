"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EditorAgent = void 0;
const langchain_config_1 = require("../utils/langchain-config");
const memory_manager_1 = require("../utils/memory-manager");
class EditorAgent {
    static getInstance() {
        if (!this.instance) {
            this.instance = new EditorAgent();
        }
        return this.instance;
    }
    async editDescription(sessionId, parameters) {
        const startTime = new Date();
        try {
            const status = {
                name: 'editor',
                status: 'processing',
                progress: 0,
                message: 'Analyzing edit request...',
                startTime
            };
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'editor', status);
            const context = memory_manager_1.MemoryManager.getContext(sessionId);
            if (!context) {
                throw new Error(`No context found for session ${sessionId}`);
            }
            const currentDescription = parameters.description || context.currentDescription;
            const userRequest = parameters.userRequest;
            if (!currentDescription) {
                throw new Error('No description available to edit');
            }
            if (!userRequest) {
                throw new Error('No edit request provided');
            }
            // Update progress
            status.progress = 20;
            status.message = 'Understanding edit requirements...';
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'editor', status);
            // Analyze the edit request
            const editAnalysis = await this.analyzeEditRequest(userRequest, currentDescription, context);
            // Update progress
            status.progress = 40;
            status.message = `Applying changes: ${editAnalysis.action}...`;
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'editor', status);
            // Apply the edits
            const updatedDescription = await this.applyEdits(currentDescription, userRequest, editAnalysis, context);
            // Update progress
            status.progress = 80;
            status.message = 'Validating changes...';
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'editor', status);
            // Validate the changes
            const validatedDescription = await this.validateChanges(currentDescription, updatedDescription, userRequest, context);
            // Update context with new description
            memory_manager_1.MemoryManager.updateContext(sessionId, {
                currentDescription: Object.assign(Object.assign({}, validatedDescription), { lastModified: new Date(), version: (currentDescription.version || 1) + 1 })
            });
            // Complete
            status.status = 'complete';
            status.progress = 100;
            status.message = `Changes applied: ${editAnalysis.summary}`;
            status.endTime = new Date();
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'editor', status);
            return {
                success: true,
                data: {
                    updatedDescription: validatedDescription,
                    changes: editAnalysis,
                    message: `I've ${editAnalysis.action}. ${editAnalysis.summary}`
                },
                status,
                updatedContext: memory_manager_1.MemoryManager.getContext(sessionId)
            };
        }
        catch (error) {
            const errorStatus = {
                name: 'editor',
                status: 'error',
                progress: 0,
                message: `Edit failed: ${error}`,
                startTime,
                endTime: new Date()
            };
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'editor', errorStatus);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown editing error',
                status: errorStatus
            };
        }
    }
    async analyzeEditRequest(userRequest, currentDescription, context) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;
        const prompt = `Analyze this edit request for a YouTube description:

User request: "${userRequest}"

Current description sections:
- Overview: ${(_a = currentDescription.overview) === null || _a === void 0 ? void 0 : _a.substring(0, 200)}...
- Guest bio: ${(_b = currentDescription.guestBio) === null || _b === void 0 ? void 0 : _b.substring(0, 100)}...
- Host bio: ${(_c = currentDescription.hostBio) === null || _c === void 0 ? void 0 : _c.substring(0, 100)}...
- Key topics: ${(_d = currentDescription.keyTopics) === null || _d === void 0 ? void 0 : _d.join(', ')}
- Resources: ${((_e = currentDescription.resources) === null || _e === void 0 ? void 0 : _e.length) || 0} items
- Timestamps: ${((_f = currentDescription.timestamps) === null || _f === void 0 ? void 0 : _f.length) || 0} items
- Extended summary: ${(_g = currentDescription.extended) === null || _g === void 0 ? void 0 : _g.substring(0, 200)}...
- SEO: ${((_j = (_h = currentDescription.seo) === null || _h === void 0 ? void 0 : _h.keywords) === null || _j === void 0 ? void 0 : _j.length) || 0} keywords, ${((_l = (_k = currentDescription.seo) === null || _k === void 0 ? void 0 : _k.hashtags) === null || _l === void 0 ? void 0 : _l.length) || 0} hashtags

Determine:
1. What specific action to take (e.g., "make punchier", "shorten", "add more detail", "remove hashtags")
2. Which sections to modify
3. The tone/style requested
4. Any specific requirements

Respond with JSON: {
  action: string,
  sections: string[],
  tone: string,
  requirements: string[],
  summary: string
}`;
        try {
            const response = await langchain_config_1.LangChainConfig.generateResponse(prompt, context, "You are an expert content editor. Analyze requests precisely. Respond only with valid JSON.");
            return JSON.parse(response);
        }
        catch (error) {
            // Fallback analysis
            return this.fallbackEditAnalysis(userRequest);
        }
    }
    async applyEdits(currentDescription, userRequest, editAnalysis, context) {
        const updatedDescription = Object.assign({}, currentDescription);
        // Apply edits based on the analysis
        for (const section of editAnalysis.sections) {
            switch (section.toLowerCase()) {
                case 'overview':
                    updatedDescription.overview = await this.editOverview(currentDescription.overview, userRequest, editAnalysis, context);
                    break;
                case 'guestbio':
                case 'guest bio':
                    updatedDescription.guestBio = await this.editBio(currentDescription.guestBio, 'guest', userRequest, editAnalysis, context);
                    break;
                case 'hostbio':
                case 'host bio':
                    updatedDescription.hostBio = await this.editBio(currentDescription.hostBio, 'host', userRequest, editAnalysis, context);
                    break;
                case 'topics':
                case 'key topics':
                    updatedDescription.keyTopics = await this.editTopics(currentDescription.keyTopics, userRequest, editAnalysis, context);
                    break;
                case 'extended':
                case 'summary':
                case 'extended summary':
                    updatedDescription.extended = await this.editExtended(currentDescription.extended, userRequest, editAnalysis, context);
                    break;
                case 'seo':
                case 'hashtags':
                case 'keywords':
                    updatedDescription.seo = await this.editSEO(currentDescription.seo, userRequest, editAnalysis, context);
                    break;
                case 'timestamps':
                    updatedDescription.timestamps = await this.editTimestamps(currentDescription.timestamps, userRequest, editAnalysis, context);
                    break;
            }
        }
        return updatedDescription;
    }
    async editOverview(currentOverview, userRequest, editAnalysis, context) {
        const prompt = `Edit this YouTube description overview based on the user's request:

Current overview: "${currentOverview}"
User request: "${userRequest}"
Requested action: ${editAnalysis.action}
Tone: ${editAnalysis.tone}

Apply the requested changes while maintaining:
- Engaging, hook-worthy opening
- Clear value proposition
- Appropriate length (2-3 sentences)
- YouTube-optimized language

Return only the edited overview text.`;
        const response = await langchain_config_1.LangChainConfig.generateResponse(prompt, context, "You are an expert YouTube content editor. Return only the edited text.");
        return response.trim();
    }
    async editBio(currentBio, type, userRequest, editAnalysis, context) {
        const prompt = `Edit this ${type} bio based on the user's request:

Current ${type} bio: "${currentBio}"
User request: "${userRequest}"
Requested action: ${editAnalysis.action}

Apply the requested changes while maintaining:
- Professional tone
- Key credentials
- Appropriate length (1-2 sentences)
- Relevance to the podcast topic

Return only the edited bio text.`;
        const response = await langchain_config_1.LangChainConfig.generateResponse(prompt, context, "You are an expert content editor. Return only the edited text.");
        return response.trim();
    }
    async editTopics(currentTopics, userRequest, editAnalysis, context) {
        const prompt = `Edit these key topics based on the user's request:

Current topics: ${currentTopics.join(', ')}
User request: "${userRequest}"
Requested action: ${editAnalysis.action}

Apply the requested changes while maintaining:
- Clear, specific topics
- Benefit-focused language
- Appropriate number of topics (5-8)
- Engaging descriptions

Respond with JSON array of edited topic strings.`;
        try {
            const response = await langchain_config_1.LangChainConfig.generateResponse(prompt, context, "You are an expert content editor. Respond only with valid JSON array.");
            const editedTopics = JSON.parse(response);
            return Array.isArray(editedTopics) ? editedTopics : currentTopics;
        }
        catch (error) {
            return currentTopics;
        }
    }
    async editExtended(currentExtended, userRequest, editAnalysis, context) {
        const prompt = `Edit this extended summary based on the user's request:

Current extended summary: "${currentExtended}"
User request: "${userRequest}"
Requested action: ${editAnalysis.action}

Apply the requested changes while maintaining:
- Detailed but engaging content
- Key insights and takeaways
- Natural, conversational tone
- Appropriate length for YouTube

Return only the edited extended summary text.`;
        const response = await langchain_config_1.LangChainConfig.generateResponse(prompt, context, "You are an expert content editor. Return only the edited text.");
        return response.trim();
    }
    async editSEO(currentSEO, userRequest, editAnalysis, context) {
        var _a, _b;
        const prompt = `Edit these SEO elements based on the user's request:

Current SEO:
- Keywords: ${((_a = currentSEO === null || currentSEO === void 0 ? void 0 : currentSEO.keywords) === null || _a === void 0 ? void 0 : _a.join(', ')) || 'None'}
- Hashtags: ${((_b = currentSEO === null || currentSEO === void 0 ? void 0 : currentSEO.hashtags) === null || _b === void 0 ? void 0 : _b.join(', ')) || 'None'}
- Suggested title: ${(currentSEO === null || currentSEO === void 0 ? void 0 : currentSEO.suggestedTitle) || 'None'}

User request: "${userRequest}"
Requested action: ${editAnalysis.action}

Apply the requested changes while maintaining SEO best practices.

Respond with JSON: {keywords: string[], hashtags: string[], suggestedTitle: string}`;
        try {
            const response = await langchain_config_1.LangChainConfig.generateResponse(prompt, context, "You are an SEO expert. Respond only with valid JSON.");
            return JSON.parse(response);
        }
        catch (error) {
            return currentSEO;
        }
    }
    async editTimestamps(currentTimestamps, userRequest, editAnalysis, context) {
        // For now, return current timestamps as timestamp editing is complex
        // In a full implementation, this would analyze and modify timestamps
        return currentTimestamps;
    }
    async validateChanges(originalDescription, updatedDescription, userRequest, context) {
        // Basic validation - ensure no critical information was lost
        const validated = Object.assign({}, updatedDescription);
        // Ensure essential fields are not empty
        if (!validated.overview && originalDescription.overview) {
            validated.overview = originalDescription.overview;
        }
        if (!validated.keyTopics || validated.keyTopics.length === 0) {
            validated.keyTopics = originalDescription.keyTopics;
        }
        return validated;
    }
    fallbackEditAnalysis(userRequest) {
        const request = userRequest.toLowerCase();
        let action = 'modify content';
        let sections = ['overview'];
        let tone = 'engaging';
        if (request.includes('punchy') || request.includes('hook')) {
            action = 'make more engaging and punchy';
            sections = ['overview'];
            tone = 'punchy';
        }
        else if (request.includes('shorten') || request.includes('shorter')) {
            action = 'shorten content';
            tone = 'concise';
        }
        else if (request.includes('hashtag')) {
            action = 'modify hashtags';
            sections = ['seo'];
        }
        else if (request.includes('topic')) {
            action = 'modify topics';
            sections = ['topics'];
        }
        return {
            action,
            sections,
            tone,
            requirements: [],
            summary: `Applied ${action} as requested`
        };
    }
}
exports.EditorAgent = EditorAgent;
//# sourceMappingURL=editor.js.map