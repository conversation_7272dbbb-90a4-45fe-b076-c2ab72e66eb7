{"version": 3, "file": "description-writer.js", "sourceRoot": "", "sources": ["../../src/agents/description-writer.ts"], "names": [], "mappings": ";;;AACA,gEAA4F;AAC5F,4DAAwD;AAExD,MAAa,sBAAsB;IAGjC,MAAM,CAAC,WAAW;QAChB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,IAAI,CAAC,QAAQ,GAAG,IAAI,sBAAsB,EAAE,CAAC;QAC/C,CAAC;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,SAAiB,EAAE,UAAe;QACvD,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,MAAM,GAAgB;gBAC1B,IAAI,EAAE,mBAAmB;gBACzB,MAAM,EAAE,YAAY;gBACpB,QAAQ,EAAE,CAAC;gBACX,OAAO,EAAE,uCAAuC;gBAChD,SAAS;aACV,CAAC;YAEF,8BAAa,CAAC,iBAAiB,CAAC,SAAS,EAAE,mBAAmB,EAAE,MAAM,CAAC,CAAC;YAExE,MAAM,OAAO,GAAG,8BAAa,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YACpD,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,gCAAgC,SAAS,EAAE,CAAC,CAAC;YAC/D,CAAC;YAED,4BAA4B;YAC5B,MAAM,UAAU,GAAG,UAAU,CAAC,UAAU,IAAI,OAAO,CAAC,UAAU,CAAC;YAC/D,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,IAAI,UAAU,CAAC,MAAM,CAAC;YAC1D,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,SAAS,IAAI,UAAU,CAAC,SAAS,CAAC;YACnE,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,QAAQ,IAAI,UAAU,CAAC,QAAQ,CAAC;YAChE,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,YAAY,IAAI,EAAE,CAAC;YAEvD,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;YACrE,CAAC;YAED,kBAAkB;YAClB,MAAM,CAAC,QAAQ,GAAG,EAAE,CAAC;YACrB,MAAM,CAAC,OAAO,GAAG,8BAA8B,CAAC;YAChD,8BAAa,CAAC,iBAAiB,CAAC,SAAS,EAAE,mBAAmB,EAAE,MAAM,CAAC,CAAC;YAExE,oBAAoB;YACpB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;YAE1E,kBAAkB;YAClB,MAAM,CAAC,QAAQ,GAAG,EAAE,CAAC;YACrB,MAAM,CAAC,OAAO,GAAG,8BAA8B,CAAC;YAChD,8BAAa,CAAC,iBAAiB,CAAC,SAAS,EAAE,mBAAmB,EAAE,MAAM,CAAC,CAAC;YAExE,gBAAgB;YAChB,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;YAEnG,kBAAkB;YAClB,MAAM,CAAC,QAAQ,GAAG,EAAE,CAAC;YACrB,MAAM,CAAC,OAAO,GAAG,0BAA0B,CAAC;YAC5C,8BAAa,CAAC,iBAAiB,CAAC,SAAS,EAAE,mBAAmB,EAAE,MAAM,CAAC,CAAC;YAExE,oBAAoB;YACpB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;YAE1E,kBAAkB;YAClB,MAAM,CAAC,QAAQ,GAAG,EAAE,CAAC;YACrB,MAAM,CAAC,OAAO,GAAG,0BAA0B,CAAC;YAC5C,8BAAa,CAAC,iBAAiB,CAAC,SAAS,EAAE,mBAAmB,EAAE,MAAM,CAAC,CAAC;YAExE,sBAAsB;YACtB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;YAE9E,kBAAkB;YAClB,MAAM,CAAC,QAAQ,GAAG,EAAE,CAAC;YACrB,MAAM,CAAC,OAAO,GAAG,6BAA6B,CAAC;YAC/C,8BAAa,CAAC,iBAAiB,CAAC,SAAS,EAAE,mBAAmB,EAAE,MAAM,CAAC,CAAC;YAExE,4BAA4B;YAC5B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;YAE3F,kBAAkB;YAClB,MAAM,CAAC,QAAQ,GAAG,EAAE,CAAC;YACrB,MAAM,CAAC,OAAO,GAAG,4BAA4B,CAAC;YAC9C,8BAAa,CAAC,iBAAiB,CAAC,SAAS,EAAE,mBAAmB,EAAE,MAAM,CAAC,CAAC;YAExE,oBAAoB;YACpB,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;YAE1E,4BAA4B;YAC5B,MAAM,WAAW,GAAuB;gBACtC,QAAQ;gBACR,OAAO;gBACP,QAAQ;gBACR,SAAS;gBACT,SAAS,EAAE,SAAS,IAAI,EAAE;gBAC1B,UAAU;gBACV,QAAQ;gBACR,GAAG;gBACH,YAAY,EAAE,IAAI,IAAI,EAAE;gBACxB,OAAO,EAAE,CAAC;aACX,CAAC;YAEF,mBAAmB;YACnB,8BAAa,CAAC,aAAa,CAAC,SAAS,EAAE;gBACrC,kBAAkB,EAAE,WAAW;aAChC,CAAC,CAAC;YAEH,WAAW;YACX,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC;YAC3B,MAAM,CAAC,QAAQ,GAAG,GAAG,CAAC;YACtB,MAAM,CAAC,OAAO,GAAG,6CAA6C,CAAC;YAC/D,MAAM,CAAC,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;YAC5B,8BAAa,CAAC,iBAAiB,CAAC,SAAS,EAAE,mBAAmB,EAAE,MAAM,CAAC,CAAC;YAExE,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,WAAW;gBACjB,MAAM;gBACN,cAAc,EAAE,8BAAa,CAAC,UAAU,CAAC,SAAS,CAAC;aACpD,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,WAAW,GAAgB;gBAC/B,IAAI,EAAE,mBAAmB;gBACzB,MAAM,EAAE,OAAO;gBACf,QAAQ,EAAE,CAAC;gBACX,OAAO,EAAE,+BAA+B,KAAK,EAAE;gBAC/C,SAAS;gBACT,OAAO,EAAE,IAAI,IAAI,EAAE;aACpB,CAAC;YAEF,8BAAa,CAAC,iBAAiB,CAAC,SAAS,EAAE,mBAAmB,EAAE,WAAW,CAAC,CAAC;YAE7E,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,mCAAmC;gBACnF,MAAM,EAAE,WAAW;aACpB,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,UAAkB,EAAE,MAAW,EAAE,OAAqB;QACnF,MAAM,UAAU,GAAG,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,MAAM,KAAI,EAAE,CAAC;QAExC,MAAM,MAAM,GAAG;;qBAEE,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC;eACnC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;;;;;mDASe,CAAC;QAEhD,MAAM,QAAQ,GAAG,MAAM,kCAAe,CAAC,gBAAgB,CACrD,MAAM,EACN,OAAO,EACP,uFAAuF,CACxF,CAAC;QAEF,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;IACzB,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,YAAmB,EAAE,QAAe,EAAE,UAAkB,EAAE,OAAqB;;QACxG,MAAM,MAAM,GAAG;;gBAEH,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC;kBAC1B,IAAI,CAAC,SAAS,CAAC,CAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAI,EAAE,CAAC;sBACvC,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC;;;;;;;;;;uDAUI,CAAC;QAEpD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,kCAAe,CAAC,gBAAgB,CACrD,MAAM,EACN,OAAO,EACP,+EAA+E,CAChF,CAAC;YAEF,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAClC,OAAO;gBACL,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,EAAE;gBAC7B,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,EAAE;aAC5B,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,QAAQ,EAAE,CAAA,MAAA,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,0CAAE,IAAI,EAAC,CAAC;oBAC1D,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC,IAAI,oCAAoC,CAAC,CAAC,CAAC,EAAE;gBAC7F,OAAO,EAAE,CAAA,MAAA,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,0CAAE,IAAI,EAAC,CAAC;oBACxD,aAAa,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE;aACtE,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,MAAW,EAAE,UAAkB,EAAE,OAAqB;QAClF,MAAM,UAAU,GAAG,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,MAAM,KAAI,EAAE,CAAC;QAExC,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5B,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,MAAM,MAAM,GAAG;;mBAEA,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;sBAClB,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC;;;;;;;;kDAQD,CAAC;QAE/C,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,kCAAe,CAAC,gBAAgB,CACrD,MAAM,EACN,OAAO,EACP,+FAA+F,CAChG,CAAC;YAEF,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAC3C,OAAO,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,UAAU,CAAC;QACnE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,UAAU,CAAC;QACpB,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,UAAkB,EAAE,MAAW,EAAE,OAAqB;;QACrF,MAAM,MAAM,GAAG;;cAEL,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC;oBACvB,CAAA,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,MAAM,0CAAE,IAAI,CAAC,IAAI,CAAC,KAAI,gBAAgB;;;;;;;;;;;;;;;kFAegB,CAAC;QAE/E,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,kCAAe,CAAC,gBAAgB,CACrD,MAAM,EACN,OAAO,EACP,uFAAuF,CACxF,CAAC;YAEF,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACxC,OAAO,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;QACrD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,sCAAsC;YACtC,OAAO;gBACL,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,UAAU,EAAE,GAAG,EAAE;gBACzD,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,iBAAiB,EAAE,UAAU,EAAE,GAAG,EAAE;gBAC5D,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,UAAU,EAAE,GAAG,EAAE;gBACzD,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,EAAE;aACrD,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,UAAkB,EAAE,MAAW,EAAE,QAAgB,EAAE,OAAqB;;QAC5G,MAAM,MAAM,GAAG;;YAEP,QAAQ;UACV,CAAA,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,MAAM,0CAAE,IAAI,CAAC,IAAI,CAAC,KAAI,gBAAgB;qBACnC,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC;;;;;;;;;wDASM,CAAC;QAErD,MAAM,QAAQ,GAAG,MAAM,kCAAe,CAAC,gBAAgB,CACrD,MAAM,EACN,OAAO,EACP,gFAAgF,CACjF,CAAC;QAEF,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;IACzB,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,UAAkB,EAAE,MAAW,EAAE,QAAgB,EAAE,OAAqB;;QAChG,MAAM,MAAM,GAAG;;YAEP,QAAQ;UACV,CAAA,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,MAAM,0CAAE,IAAI,CAAC,IAAI,CAAC,KAAI,gBAAgB;yBAC/B,CAAA,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,QAAQ,0CAAE,IAAI,CAAC,IAAI,CAAC,KAAI,EAAE;;;;;;;;;;;;;oFAayB,CAAC;QAEjF,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,kCAAe,CAAC,gBAAgB,CACrD,MAAM,EACN,OAAO,EACP,0EAA0E,CAC3E,CAAC;YAEF,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACjC,OAAO;gBACL,QAAQ,EAAE,GAAG,CAAC,QAAQ,IAAI,EAAE;gBAC5B,QAAQ,EAAE,GAAG,CAAC,QAAQ,IAAI,EAAE;gBAC5B,cAAc,EAAE,GAAG,CAAC,cAAc,IAAI,iBAAiB;aACxD,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,QAAQ,EAAE,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,QAAQ,KAAI,EAAE;gBAChC,QAAQ,EAAE,CAAC,UAAU,EAAE,YAAY,EAAE,WAAW,CAAC;gBACjD,cAAc,EAAE,iBAAiB;aAClC,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AA3VD,wDA2VC"}