"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReviewerAgent = void 0;
const langchain_config_1 = require("../utils/langchain-config");
const memory_manager_1 = require("../utils/memory-manager");
class ReviewerAgent {
    static getInstance() {
        if (!this.instance) {
            this.instance = new ReviewerAgent();
        }
        return this.instance;
    }
    async reviewData(sessionId, parameters) {
        const startTime = new Date();
        try {
            const status = {
                name: 'reviewer',
                status: 'processing',
                progress: 0,
                message: 'Starting data validation...',
                startTime
            };
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'reviewer', status);
            const context = memory_manager_1.MemoryManager.getContext(sessionId);
            if (!context) {
                throw new Error(`No context found for session ${sessionId}`);
            }
            const dataToReview = parameters.data || this.gatherAllData(context);
            // Update progress
            status.progress = 20;
            status.message = 'Validating profile matches...';
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'reviewer', status);
            // Review profiles
            const profileReview = await this.reviewProfiles(context.memory.profiles || [], context.memory.participants || [], context);
            // Update progress
            status.progress = 40;
            status.message = 'Checking resource links...';
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'reviewer', status);
            // Review resources
            const resourceReview = await this.reviewResources(context.memory.resources || [], context.transcript, context);
            // Update progress
            status.progress = 60;
            status.message = 'Validating topic accuracy...';
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'reviewer', status);
            // Review topics
            const topicReview = await this.reviewTopics(context.memory.topics || {}, context.transcript, context);
            // Update progress
            status.progress = 80;
            status.message = 'Generating validation report...';
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'reviewer', status);
            // Compile review results
            const reviewResults = {
                profiles: profileReview,
                resources: resourceReview,
                topics: topicReview,
                overallScore: this.calculateOverallScore(profileReview, resourceReview, topicReview),
                recommendations: this.generateRecommendations(profileReview, resourceReview, topicReview)
            };
            // Store review results
            memory_manager_1.MemoryManager.addMemory(sessionId, 'reviewResults', reviewResults);
            // Complete
            status.status = 'complete';
            status.progress = 100;
            status.message = `Validation complete. Overall confidence: ${Math.round(reviewResults.overallScore * 100)}%`;
            status.endTime = new Date();
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'reviewer', status);
            return {
                success: true,
                data: reviewResults,
                status,
                updatedContext: memory_manager_1.MemoryManager.getContext(sessionId)
            };
        }
        catch (error) {
            const errorStatus = {
                name: 'reviewer',
                status: 'error',
                progress: 0,
                message: `Review failed: ${error}`,
                startTime,
                endTime: new Date()
            };
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'reviewer', errorStatus);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown review error',
                status: errorStatus
            };
        }
    }
    gatherAllData(context) {
        return {
            transcript: context.transcript,
            topics: context.memory.topics,
            resources: context.memory.resources,
            profiles: context.memory.profiles,
            participants: context.memory.participants,
            description: context.currentDescription
        };
    }
    async reviewProfiles(profiles, participants, context) {
        if (!profiles || profiles.length === 0) {
            return {
                score: 0.5,
                issues: ['No profiles found to validate'],
                validated: [],
                rejected: []
            };
        }
        const prompt = `Review these profile matches for podcast participants:

Participants: ${JSON.stringify(participants)}
Profiles found: ${JSON.stringify(profiles)}

For each profile, evaluate:
1. Is this likely the correct person? (confidence 0-1)
2. Is the profile information accurate and relevant?
3. Are there any red flags or inconsistencies?
4. Should this profile be included in the final description?

Respond with JSON: {
  score: number (0-1),
  issues: string[],
  validated: object[],
  rejected: object[]
}`;
        try {
            const response = await langchain_config_1.LangChainConfig.generateResponse(prompt, context, "You are an expert fact-checker. Respond only with valid JSON.");
            return JSON.parse(response);
        }
        catch (error) {
            return {
                score: 0.7,
                issues: ['Could not validate profiles automatically'],
                validated: profiles.filter(p => p.confidence > 0.6),
                rejected: profiles.filter(p => p.confidence <= 0.6)
            };
        }
    }
    async reviewResources(resources, transcript, context) {
        if (!resources || resources.length === 0) {
            return {
                score: 0.5,
                issues: ['No resources found to validate'],
                validated: [],
                rejected: []
            };
        }
        const prompt = `Review these resources extracted from the podcast:

Resources: ${JSON.stringify(resources)}
Transcript context: ${transcript.substring(0, 2000)}...

For each resource, evaluate:
1. Is it actually mentioned in the podcast?
2. Is the URL valid and relevant?
3. Is the categorization correct?
4. Would it be valuable in a YouTube description?

Respond with JSON: {
  score: number (0-1),
  issues: string[],
  validated: object[],
  rejected: object[]
}`;
        try {
            const response = await langchain_config_1.LangChainConfig.generateResponse(prompt, context, "You are an expert fact-checker. Respond only with valid JSON.");
            return JSON.parse(response);
        }
        catch (error) {
            return {
                score: 0.7,
                issues: ['Could not validate resources automatically'],
                validated: resources.filter(r => r.confidence > 0.5),
                rejected: resources.filter(r => r.confidence <= 0.5)
            };
        }
    }
    async reviewTopics(topics, transcript, context) {
        if (!topics || !topics.topics || topics.topics.length === 0) {
            return {
                score: 0.5,
                issues: ['No topics found to validate'],
                validated: [],
                rejected: []
            };
        }
        const prompt = `Review these extracted topics for accuracy:

Topics: ${JSON.stringify(topics.topics)}
Keywords: ${JSON.stringify(topics.keywords || [])}
Transcript context: ${transcript.substring(0, 3000)}...

Evaluate:
1. Are the topics actually discussed in the podcast?
2. Are they the most important/relevant topics?
3. Are any key topics missing?
4. Are the keywords accurate and SEO-friendly?

Respond with JSON: {
  score: number (0-1),
  issues: string[],
  validated: string[],
  rejected: string[],
  missing: string[]
}`;
        try {
            const response = await langchain_config_1.LangChainConfig.generateResponse(prompt, context, "You are an expert content analyst. Respond only with valid JSON.");
            return JSON.parse(response);
        }
        catch (error) {
            return {
                score: 0.8,
                issues: ['Could not validate topics automatically'],
                validated: topics.topics || [],
                rejected: [],
                missing: []
            };
        }
    }
    calculateOverallScore(profileReview, resourceReview, topicReview) {
        const scores = [
            profileReview.score || 0.5,
            resourceReview.score || 0.5,
            topicReview.score || 0.5
        ];
        return scores.reduce((sum, score) => sum + score, 0) / scores.length;
    }
    generateRecommendations(profileReview, resourceReview, topicReview) {
        const recommendations = [];
        // Profile recommendations
        if (profileReview.score < 0.7) {
            recommendations.push('Consider manually verifying participant profiles');
        }
        if (profileReview.rejected && profileReview.rejected.length > 0) {
            recommendations.push(`${profileReview.rejected.length} profiles were rejected due to low confidence`);
        }
        // Resource recommendations
        if (resourceReview.score < 0.7) {
            recommendations.push('Some resources may need manual verification');
        }
        if (resourceReview.rejected && resourceReview.rejected.length > 0) {
            recommendations.push(`${resourceReview.rejected.length} resources were filtered out`);
        }
        // Topic recommendations
        if (topicReview.missing && topicReview.missing.length > 0) {
            recommendations.push(`Consider adding these missing topics: ${topicReview.missing.join(', ')}`);
        }
        if (topicReview.score < 0.8) {
            recommendations.push('Topic extraction could be improved');
        }
        if (recommendations.length === 0) {
            recommendations.push('All data looks good! No major issues found.');
        }
        return recommendations;
    }
    async validateSpecificItem(sessionId, itemType, item) {
        const context = memory_manager_1.MemoryManager.getContext(sessionId);
        if (!context)
            return false;
        const prompt = `Validate this ${itemType} for accuracy:

Item: ${JSON.stringify(item)}
Context: ${context.transcript.substring(0, 1000)}...

Is this item accurate and should it be included? Respond with only "true" or "false".`;
        try {
            const response = await langchain_config_1.LangChainConfig.generateResponse(prompt, context, "You are a fact-checker. Respond only with 'true' or 'false'.");
            return response.trim().toLowerCase() === 'true';
        }
        catch (error) {
            return false;
        }
    }
}
exports.ReviewerAgent = ReviewerAgent;
//# sourceMappingURL=reviewer.js.map