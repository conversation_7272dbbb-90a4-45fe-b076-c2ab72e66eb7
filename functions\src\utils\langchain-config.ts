import { ChatGroq } from '@langchain/groq';
import { ConversationBufferMemory } from 'langchain/memory';
import { PromptTemplate } from '@langchain/core/prompts';
import { LLMChain } from 'langchain/chains';
import { AgentContext } from '../types';

export class LangChainConfig {
  private static groqModel: ChatGroq;
  private static memory: ConversationBufferMemory;

  static initialize() {
    const groqApiKey = process.env.GROQ_API_KEY;
    if (!groqApiKey) {
      throw new Error('GROQ_API_KEY environment variable is required');
    }

    this.groqModel = new ChatGroq({
      apiKey: groqApiKey,
      model: 'llama-3.1-70b-versatile', // Using Groq's Llama model
      temperature: 0.7,
      maxTokens: 4000,
    });

    this.memory = new ConversationBufferMemory({
      memoryKey: 'chat_history',
      returnMessages: true,
    });
  }

  static getGroqModel(): ChatGroq {
    if (!this.groqModel) {
      this.initialize();
    }
    return this.groqModel;
  }

  static getMemory(): ConversationBufferMemory {
    if (!this.memory) {
      this.initialize();
    }
    return this.memory;
  }

  static createChain(template: string, inputVariables: string[]): LLMChain {
    const prompt = new PromptTemplate({
      template,
      inputVariables,
    });

    return new LLMChain({
      llm: this.getGroqModel(),
      prompt,
      memory: this.getMemory(),
    });
  }

  static async generateResponse(
    prompt: string,
    context: AgentContext,
    systemMessage?: string
  ): Promise<string> {
    try {
      const model = this.getGroqModel();
      
      const messages = [
        ...(systemMessage ? [{ role: 'system' as const, content: systemMessage }] : []),
        { role: 'user' as const, content: prompt }
      ];

      const response = await model.invoke(messages);
      return response.content as string;
    } catch (error) {
      console.error('Error generating response:', error);
      throw new Error(`Failed to generate response: ${error}`);
    }
  }
}

export const AGENT_PROMPTS = {
  COORDINATOR: `You are the Coordinator Agent for a podcast-to-YouTube-description system. 
Your role is to orchestrate other agents and manage the overall workflow.

Current context: {context}
User request: {input}

Decide which agents to activate and in what order. Respond with a JSON object containing:
- nextAgent: string (name of next agent to run)
- reasoning: string (why this agent should run next)
- parameters: object (parameters to pass to the agent)
- shouldAskUser: boolean (whether to ask user for input)`,

  TRANSCRIBER: `You are the Transcriber Agent. Your role is to process audio transcription results and extract meaningful information.

Audio transcription: {transcript}
Context: {context}

Extract and structure the transcription data. Focus on:
- Speaker identification
- Key moments and timestamps
- Overall quality assessment
- Any technical issues

Respond with structured data about the transcription.`,

  TOPIC_EXTRACTOR: `You are the Topic Extractor Agent. Analyze the transcript and extract key topics, themes, and insights.

Transcript: {transcript}
Context: {context}

Extract:
- Main topics discussed (5-8 key topics)
- Important quotes and insights
- Named entities (people, companies, products)
- Key takeaways for the audience
- SEO-relevant keywords

Respond with structured topic data.`,

  LINK_FINDER: `You are the Link Finder Agent. Your role is to identify and validate links, resources, and references mentioned in the podcast.

Transcript: {transcript}
Context: {context}

Find and categorize:
- Websites and URLs mentioned
- Books, tools, and products referenced
- Social media handles
- Companies and organizations
- Any other resources that would be valuable to include

For each resource, provide:
- Name/label
- Type (website, book, tool, social, product)
- Confidence level
- Where it was mentioned in the transcript

Respond with structured resource data.`,

  PROFILE_FINDER: `You are the Profile Finder Agent. Search for and validate social media profiles and professional information about the podcast participants.

Participants mentioned: {participants}
Context: {context}

For each participant, find:
- LinkedIn profile
- Twitter/X handle
- Professional website
- Brief bio information
- Verification status

Respond with structured profile data.`,

  REVIEWER: `You are the Reviewer Agent. Your role is to validate and verify information found by other agents.

Data to review: {data}
Context: {context}

Review for:
- Accuracy of information
- Relevance to the podcast content
- Confidence levels
- Potential issues or conflicts

Respond with validation results and recommendations.`,

  DESCRIPTION_WRITER: `You are the Description Writer Agent. Create a compelling YouTube description using all gathered information.

Available data:
- Transcript: {transcript}
- Topics: {topics}
- Resources: {resources}
- Profiles: {profiles}
Context: {context}

Create a YouTube description with:
- Engaging overview (2-3 sentences)
- Guest and host bios
- Key topics covered
- Timestamps for major sections
- Resource links
- Extended summary
- SEO keywords and hashtags

Make it engaging, informative, and optimized for YouTube discovery.`,

  EDITOR: `You are the Editor Agent. Your role is to modify and refine the YouTube description based on user feedback.

Current description: {description}
User request: {userRequest}
Context: {context}

Modify the description according to the user's request. Common requests include:
- Making it more engaging/punchy
- Shortening or expanding sections
- Adding or removing elements
- Changing tone or style
- Improving SEO

Respond with the updated description and explanation of changes made.`
};

export const SYSTEM_MESSAGES = {
  COORDINATOR: "You are an intelligent workflow coordinator. Always respond with valid JSON.",
  TRANSCRIBER: "You are an expert at processing and structuring audio transcription data.",
  TOPIC_EXTRACTOR: "You are an expert content analyst specializing in podcast content.",
  LINK_FINDER: "You are an expert at identifying and categorizing online resources and references.",
  PROFILE_FINDER: "You are an expert at finding and verifying professional profiles and social media accounts.",
  REVIEWER: "You are a meticulous fact-checker and data validator.",
  DESCRIPTION_WRITER: "You are an expert YouTube content creator and SEO specialist.",
  EDITOR: "You are an expert content editor who excels at refining and improving written content."
};
