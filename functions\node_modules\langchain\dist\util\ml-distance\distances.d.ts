/**
 *Returns the Inner Product similarity between vectors a and b
 * @link [Inner Product Similarity algorithm](https://www.naun.org/main/NAUN/ijmmas/mmmas-49.pdf)
 * @param a - first vector
 * @param b - second vector
 *
 */
export declare function innerProduct(a: number[], b: number[]): number;
/**
 *Returns the Chebyshev distance between vectors a and b
 * @link [Chebyshev algorithm](https://en.wikipedia.org/wiki/Chebyshev_distance)
 * @param a - first vector
 * @param b - second vector
 *
 */
export declare function chebyshev(a: number[], b: number[]): number;
/**
 *Returns the Manhattan distance between vectors a and b
 * @link [Manhattan algorithm](https://www.naun.org/main/NAUN/ijmmas/mmmas-49.pdf)
 * @param a - first vector
 * @param b - second vector
 *
 */
export declare function manhattan(a: number[], b: number[]): number;
