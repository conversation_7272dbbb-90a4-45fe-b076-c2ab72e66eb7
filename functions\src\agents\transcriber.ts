import { createClient } from '@deepgram/sdk';
import { AgentContext, AgentResponse, AgentStatus, TranscriptionResult } from '../types';
import { MemoryManager } from '../utils/memory-manager';
import axios from 'axios';

export class TranscriberAgent {
  private static instance: TranscriberAgent;
  private deepgram: any;

  constructor() {
    const deepgramApiKey = process.env.DEEPGRAM_API_KEY;
    if (!deepgramApiKey) {
      throw new Error('DEEPGRAM_API_KEY environment variable is required');
    }
    this.deepgram = createClient(deepgramApiKey);
  }

  static getInstance(): TranscriberAgent {
    if (!this.instance) {
      this.instance = new TranscriberAgent();
    }
    return this.instance;
  }

  async processAudio(sessionId: string, parameters: any): Promise<AgentResponse> {
    const startTime = new Date();
    
    try {
      const status: AgentStatus = {
        name: 'transcriber',
        status: 'processing',
        progress: 0,
        message: 'Starting audio transcription...',
        startTime
      };
      
      MemoryManager.updateAgentStatus(sessionId, 'transcriber', status);

      const context = MemoryManager.getContext(sessionId);
      if (!context) {
        throw new Error(`No context found for session ${sessionId}`);
      }

      // Get audio file URL from context or parameters
      const audioUrl = parameters.audioUrl || context.memory.audioFileUrl;
      if (!audioUrl) {
        throw new Error('No audio file URL provided');
      }

      // Update progress
      status.progress = 20;
      status.message = 'Downloading audio file...';
      MemoryManager.updateAgentStatus(sessionId, 'transcriber', status);

      // Download audio file
      const audioBuffer = await this.downloadAudioFile(audioUrl);

      // Update progress
      status.progress = 40;
      status.message = 'Sending to Deepgram for transcription...';
      MemoryManager.updateAgentStatus(sessionId, 'transcriber', status);

      // Transcribe with Deepgram
      const transcriptionResult = await this.transcribeWithDeepgram(audioBuffer);

      // Update progress
      status.progress = 80;
      status.message = 'Processing transcription results...';
      MemoryManager.updateAgentStatus(sessionId, 'transcriber', status);

      // Store transcription in context
      MemoryManager.updateContext(sessionId, {
        transcript: transcriptionResult.text,
        memory: {
          ...context.memory,
          transcriptionResult,
          audioProcessed: true
        }
      });

      // Complete
      status.status = 'complete';
      status.progress = 100;
      status.message = `Transcription completed: ${transcriptionResult.text.length} characters`;
      status.endTime = new Date();
      MemoryManager.updateAgentStatus(sessionId, 'transcriber', status);

      return {
        success: true,
        data: transcriptionResult,
        status,
        updatedContext: MemoryManager.getContext(sessionId)
      };

    } catch (error) {
      const errorStatus: AgentStatus = {
        name: 'transcriber',
        status: 'error',
        progress: 0,
        message: `Transcription failed: ${error}`,
        startTime,
        endTime: new Date()
      };
      
      MemoryManager.updateAgentStatus(sessionId, 'transcriber', errorStatus);

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown transcription error',
        status: errorStatus
      };
    }
  }

  private async downloadAudioFile(url: string): Promise<Buffer> {
    try {
      const response = await axios.get(url, {
        responseType: 'arraybuffer',
        timeout: 300000, // 5 minutes timeout
      });
      return Buffer.from(response.data);
    } catch (error) {
      throw new Error(`Failed to download audio file: ${error}`);
    }
  }

  private async transcribeWithDeepgram(audioBuffer: Buffer): Promise<TranscriptionResult> {
    try {
      const { result, error } = await this.deepgram.listen.prerecorded.transcribeFile(
        audioBuffer,
        {
          model: 'nova-2',
          language: 'en-US',
          smart_format: true,
          punctuate: true,
          diarize: true,
          utterances: true,
          words: true,
          paragraphs: true,
          summarize: 'v2',
          detect_topics: true,
          sentiment: true,
        }
      );

      if (error) {
        throw new Error(`Deepgram API error: ${error}`);
      }

      // Extract the main transcript
      const transcript = result.results?.channels?.[0]?.alternatives?.[0]?.transcript || '';
      
      // Extract word-level timestamps
      const words = result.results?.channels?.[0]?.alternatives?.[0]?.words || [];
      
      // Extract confidence score
      const confidence = result.results?.channels?.[0]?.alternatives?.[0]?.confidence || 0;
      
      // Extract detected language
      const language = result.results?.channels?.[0]?.detected_language || 'en';
      
      // Extract duration
      const duration = result.metadata?.duration || 0;

      // Extract additional insights
      const topics = result.results?.topics?.segments || [];
      const sentiment = result.results?.sentiment?.segments || [];
      const summary = result.results?.summary?.short || '';

      const transcriptionResult: TranscriptionResult = {
        text: transcript,
        words: words.map((word: any) => ({
          word: word.word,
          start: word.start,
          end: word.end,
          confidence: word.confidence
        })),
        confidence,
        language,
        duration
      };

      // Store additional insights in memory for other agents
      const insights = {
        topics,
        sentiment,
        summary,
        speakers: result.results?.channels?.[0]?.alternatives?.[0]?.speakers || [],
        paragraphs: result.results?.channels?.[0]?.alternatives?.[0]?.paragraphs || []
      };

      return {
        ...transcriptionResult,
        insights
      } as any;

    } catch (error) {
      throw new Error(`Deepgram transcription failed: ${error}`);
    }
  }

  async getTranscriptionStatus(sessionId: string): Promise<AgentStatus | null> {
    return MemoryManager.getAgentStatus(sessionId, 'transcriber');
  }

  async retryTranscription(sessionId: string): Promise<AgentResponse> {
    // Reset the agent status and retry
    const context = MemoryManager.getContext(sessionId);
    if (!context) {
      throw new Error(`No context found for session ${sessionId}`);
    }

    // Clear previous transcription data
    MemoryManager.updateContext(sessionId, {
      transcript: '',
      memory: {
        ...context.memory,
        transcriptionResult: null,
        audioProcessed: false
      }
    });

    // Retry transcription
    return await this.processAudio(sessionId, {
      audioUrl: context.memory.audioFileUrl
    });
  }
}
