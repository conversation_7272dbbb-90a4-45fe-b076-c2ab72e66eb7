"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProfileFinderAgent = void 0;
const langchain_config_1 = require("../utils/langchain-config");
const memory_manager_1 = require("../utils/memory-manager");
const axios_1 = __importDefault(require("axios"));
class ProfileFinderAgent {
    static getInstance() {
        if (!this.instance) {
            this.instance = new ProfileFinderAgent();
        }
        return this.instance;
    }
    async findProfiles(sessionId, parameters) {
        const startTime = new Date();
        try {
            const status = {
                name: 'profileFinder',
                status: 'processing',
                progress: 0,
                message: 'Identifying participants...',
                startTime
            };
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'profileFinder', status);
            const context = memory_manager_1.MemoryManager.getContext(sessionId);
            if (!context) {
                throw new Error(`No context found for session ${sessionId}`);
            }
            const transcript = parameters.transcript || context.transcript;
            if (!transcript) {
                throw new Error('No transcript available for profile finding');
            }
            // Update progress
            status.progress = 20;
            status.message = 'Extracting participant names...';
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'profileFinder', status);
            // Extract participant names from transcript
            const participants = await this.extractParticipants(transcript, context);
            // Update progress
            status.progress = 40;
            status.message = 'Searching for professional profiles...';
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'profileFinder', status);
            // Search for profiles for each participant
            const allProfiles = [];
            for (let i = 0; i < participants.length; i++) {
                const participant = participants[i];
                // Update progress for each participant
                const participantProgress = 40 + (i / participants.length) * 40;
                status.progress = participantProgress;
                status.message = `Searching profiles for ${participant.name}...`;
                memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'profileFinder', status);
                try {
                    const profiles = await this.searchParticipantProfiles(participant, context);
                    allProfiles.push(...profiles);
                }
                catch (error) {
                    console.warn(`Failed to search profiles for ${participant.name}:`, error);
                }
            }
            // Update progress
            status.progress = 85;
            status.message = 'Validating and ranking profiles...';
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'profileFinder', status);
            // Validate and rank profiles
            const validatedProfiles = await this.validateProfiles(allProfiles, participants, context);
            // Store results in memory
            memory_manager_1.MemoryManager.addMemory(sessionId, 'profiles', validatedProfiles);
            memory_manager_1.MemoryManager.addMemory(sessionId, 'participants', participants);
            // Complete
            status.status = 'complete';
            status.progress = 100;
            status.message = `Found ${validatedProfiles.length} verified profiles for ${participants.length} participants`;
            status.endTime = new Date();
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'profileFinder', status);
            return {
                success: true,
                data: { profiles: validatedProfiles, participants },
                status,
                updatedContext: memory_manager_1.MemoryManager.getContext(sessionId)
            };
        }
        catch (error) {
            const errorStatus = {
                name: 'profileFinder',
                status: 'error',
                progress: 0,
                message: `Profile search failed: ${error}`,
                startTime,
                endTime: new Date()
            };
            memory_manager_1.MemoryManager.updateAgentStatus(sessionId, 'profileFinder', errorStatus);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown profile search error',
                status: errorStatus
            };
        }
    }
    async extractParticipants(transcript, context) {
        const prompt = `Analyze this podcast transcript and identify all participants (host and guests):

Transcript: ${transcript.substring(0, 4000)}...

For each participant, extract:
- Full name
- Role (host, guest, co-host)
- Any mentioned credentials or titles
- Company or organization if mentioned
- Brief description of their expertise

Look for patterns like:
- "I'm [Name]" or "My name is [Name]"
- "Our guest today is [Name]"
- "[Name] is the CEO of..."
- Speaker introductions and bios

Respond with JSON array of objects with: name, role, title, company, expertise, confidence`;
        const response = await langchain_config_1.LangChainConfig.generateResponse(prompt, context, "You are an expert at identifying people and their roles from conversations. Respond only with valid JSON.");
        try {
            const participants = JSON.parse(response);
            return Array.isArray(participants) ? participants : [];
        }
        catch (error) {
            // Fallback: simple name extraction
            return this.fallbackParticipantExtraction(transcript);
        }
    }
    async searchParticipantProfiles(participant, context) {
        const profiles = [];
        // Search LinkedIn
        const linkedinProfiles = await this.searchLinkedIn(participant);
        profiles.push(...linkedinProfiles);
        // Search Twitter/X
        const twitterProfiles = await this.searchTwitter(participant);
        profiles.push(...twitterProfiles);
        // Search general web
        const webProfiles = await this.searchWeb(participant);
        profiles.push(...webProfiles);
        return profiles;
    }
    async searchLinkedIn(participant) {
        const serpApiKey = process.env.SERPAPI_KEY;
        if (!serpApiKey)
            return [];
        try {
            const searchQuery = `${participant.name} ${participant.company || ''} ${participant.title || ''} site:linkedin.com`;
            const response = await axios_1.default.get('https://serpapi.com/search', {
                params: {
                    q: searchQuery,
                    api_key: serpApiKey,
                    engine: 'google',
                    num: 3
                },
                timeout: 10000
            });
            return (response.data.organic_results || []).map((result) => ({
                name: participant.name,
                platform: 'linkedin',
                url: result.link,
                bio: result.snippet || '',
                verified: result.link.includes('linkedin.com/in/'),
                confidence: this.calculateConfidence(participant, result),
                imageUrl: undefined
            }));
        }
        catch (error) {
            console.warn('LinkedIn search failed:', error);
            return [];
        }
    }
    async searchTwitter(participant) {
        const serpApiKey = process.env.SERPAPI_KEY;
        if (!serpApiKey)
            return [];
        try {
            const searchQuery = `${participant.name} ${participant.company || ''} site:twitter.com OR site:x.com`;
            const response = await axios_1.default.get('https://serpapi.com/search', {
                params: {
                    q: searchQuery,
                    api_key: serpApiKey,
                    engine: 'google',
                    num: 2
                },
                timeout: 10000
            });
            return (response.data.organic_results || []).map((result) => ({
                name: participant.name,
                platform: 'twitter',
                url: result.link,
                bio: result.snippet || '',
                verified: false, // Would need Twitter API to verify
                confidence: this.calculateConfidence(participant, result),
                imageUrl: undefined
            }));
        }
        catch (error) {
            console.warn('Twitter search failed:', error);
            return [];
        }
    }
    async searchWeb(participant) {
        const serpApiKey = process.env.SERPAPI_KEY;
        if (!serpApiKey)
            return [];
        try {
            const searchQuery = `${participant.name} ${participant.company || ''} ${participant.title || ''} bio`;
            const response = await axios_1.default.get('https://serpapi.com/search', {
                params: {
                    q: searchQuery,
                    api_key: serpApiKey,
                    engine: 'google',
                    num: 2
                },
                timeout: 10000
            });
            return (response.data.organic_results || [])
                .filter((result) => !result.link.includes('linkedin.com') && !result.link.includes('twitter.com'))
                .map((result) => ({
                name: participant.name,
                platform: 'website',
                url: result.link,
                bio: result.snippet || '',
                verified: false,
                confidence: this.calculateConfidence(participant, result),
                imageUrl: undefined
            }));
        }
        catch (error) {
            console.warn('Web search failed:', error);
            return [];
        }
    }
    calculateConfidence(participant, searchResult) {
        let confidence = 0.5; // Base confidence
        const resultText = (searchResult.title + ' ' + searchResult.snippet).toLowerCase();
        const participantName = participant.name.toLowerCase();
        // Name match
        if (resultText.includes(participantName)) {
            confidence += 0.3;
        }
        // Company match
        if (participant.company && resultText.includes(participant.company.toLowerCase())) {
            confidence += 0.2;
        }
        // Title match
        if (participant.title && resultText.includes(participant.title.toLowerCase())) {
            confidence += 0.1;
        }
        // Platform credibility
        if (searchResult.link.includes('linkedin.com')) {
            confidence += 0.1;
        }
        return Math.min(confidence, 1.0);
    }
    async validateProfiles(profiles, participants, context) {
        // Use AI to validate profile matches
        const prompt = `Validate these profile search results for podcast participants:

Participants: ${JSON.stringify(participants)}
Profiles: ${JSON.stringify(profiles.slice(0, 10))}

For each profile, determine:
1. Is this likely the correct person? (confidence 0-1)
2. Is the profile information relevant and useful?
3. Should this profile be included in the final description?

Respond with JSON array of validated profiles with updated confidence scores.`;
        try {
            const response = await langchain_config_1.LangChainConfig.generateResponse(prompt, context, "You are an expert at validating profile matches. Respond only with valid JSON.");
            const validated = JSON.parse(response);
            return Array.isArray(validated) ? validated : profiles;
        }
        catch (error) {
            // Fallback: filter by confidence threshold
            return profiles.filter(profile => profile.confidence > 0.6);
        }
    }
    fallbackParticipantExtraction(transcript) {
        const participants = [];
        // Simple pattern matching for common introduction patterns
        const patterns = [
            /(?:I'm|I am|My name is)\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)/gi,
            /(?:guest|host).*?([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)/gi,
            /([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\s+(?:is|was)\s+(?:the|a|an)\s+(?:CEO|founder|director|president)/gi
        ];
        const foundNames = new Set();
        patterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(transcript)) !== null) {
                const name = match[1].trim();
                if (name.length > 2 && !foundNames.has(name)) {
                    foundNames.add(name);
                    participants.push({
                        name,
                        role: 'participant',
                        title: '',
                        company: '',
                        expertise: '',
                        confidence: 0.7
                    });
                }
            }
        });
        return participants.slice(0, 5); // Limit to 5 participants
    }
}
exports.ProfileFinderAgent = ProfileFinderAgent;
//# sourceMappingURL=profile-finder.js.map