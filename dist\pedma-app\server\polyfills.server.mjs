import { createRequire } from 'node:module';
globalThis['require'] ??= createRequire(import.meta.url);
var ti=Object.getOwnPropertyNames,Z=(M,O)=>function(){return O||(0,M[ti(M)[0]])((O={exports:{}}).exports,O),O.exports},Rt=Z({"external/npm/node_modules/domino/lib/Event.js"(M,O){O.exports=_,_.CAPTURING_PHASE=1,_.AT_TARGET=2,_.BUBBLING_PHASE=3;function _(f,i){if(this.type="",this.target=null,this.currentTarget=null,this.eventPhase=_.AT_TARGET,this.bubbles=!1,this.cancelable=!1,this.isTrusted=!1,this.defaultPrevented=!1,this.timeStamp=Date.now(),this._propagationStopped=!1,this._immediatePropagationStopped=!1,this._initialized=!0,this._dispatching=!1,f&&(this.type=f),i)for(var a in i)this[a]=i[a]}_.prototype=Object.create(Object.prototype,{constructor:{value:_},stopPropagation:{value:function(){this._propagationStopped=!0}},stopImmediatePropagation:{value:function(){this._propagationStopped=!0,this._immediatePropagationStopped=!0}},preventDefault:{value:function(){this.cancelable&&(this.defaultPrevented=!0)}},initEvent:{value:function(i,a,l){this._initialized=!0,!this._dispatching&&(this._propagationStopped=!1,this._immediatePropagationStopped=!1,this.defaultPrevented=!1,this.isTrusted=!1,this.target=null,this.type=i,this.bubbles=a,this.cancelable=l)}}})}}),En=Z({"external/npm/node_modules/domino/lib/UIEvent.js"(M,O){var _=Rt();O.exports=f;function f(){_.call(this),this.view=null,this.detail=0}f.prototype=Object.create(_.prototype,{constructor:{value:f},initUIEvent:{value:function(i,a,l,c,u){this.initEvent(i,a,l),this.view=c,this.detail=u}}})}}),yn=Z({"external/npm/node_modules/domino/lib/MouseEvent.js"(M,O){var _=En();O.exports=f;function f(){_.call(this),this.screenX=this.screenY=this.clientX=this.clientY=0,this.ctrlKey=this.altKey=this.shiftKey=this.metaKey=!1,this.button=0,this.buttons=1,this.relatedTarget=null}f.prototype=Object.create(_.prototype,{constructor:{value:f},initMouseEvent:{value:function(i,a,l,c,u,s,v,p,b,D,F,A,ee,R,y){switch(this.initEvent(i,a,l,c,u),this.screenX=s,this.screenY=v,this.clientX=p,this.clientY=b,this.ctrlKey=D,this.altKey=F,this.shiftKey=A,this.metaKey=ee,this.button=R,R){case 0:this.buttons=1;break;case 1:this.buttons=4;break;case 2:this.buttons=2;break;default:this.buttons=0;break}this.relatedTarget=y}},getModifierState:{value:function(i){switch(i){case"Alt":return this.altKey;case"Control":return this.ctrlKey;case"Shift":return this.shiftKey;case"Meta":return this.metaKey;default:return!1}}}})}}),Ir=Z({"external/npm/node_modules/domino/lib/config.js"(M){M.isApiWritable=!globalThis.__domino_frozen__}}),Se=Z({"external/npm/node_modules/domino/lib/utils.js"(M){var O=Ir().isApiWritable;M.NAMESPACE={HTML:"http://www.w3.org/1999/xhtml",XML:"http://www.w3.org/XML/1998/namespace",XMLNS:"http://www.w3.org/2000/xmlns/",MATHML:"http://www.w3.org/1998/Math/MathML",SVG:"http://www.w3.org/2000/svg",XLINK:"http://www.w3.org/1999/xlink"},M.IndexSizeError=()=>{throw new DOMException("The index is not in the allowed range","IndexSizeError")},M.HierarchyRequestError=()=>{throw new DOMException("The node tree hierarchy is not correct","HierarchyRequestError")},M.WrongDocumentError=()=>{throw new DOMException("The object is in the wrong Document","WrongDocumentError")},M.InvalidCharacterError=()=>{throw new DOMException("The string contains invalid characters","InvalidCharacterError")},M.NoModificationAllowedError=()=>{throw new DOMException("The object cannot be modified","NoModificationAllowedError")},M.NotFoundError=()=>{throw new DOMException("The object can not be found here","NotFoundError")},M.NotSupportedError=()=>{throw new DOMException("The operation is not supported","NotSupportedError")},M.InvalidStateError=()=>{throw new DOMException("The object is in an invalid state","InvalidStateError")},M.SyntaxError=()=>{throw new DOMException("The string did not match the expected pattern","SyntaxError")},M.InvalidModificationError=()=>{throw new DOMException("The object can not be modified in this way","InvalidModificationError")},M.NamespaceError=()=>{throw new DOMException("The operation is not allowed by Namespaces in XML","NamespaceError")},M.InvalidAccessError=()=>{throw new DOMException("The object does not support the operation or argument","InvalidAccessError")},M.TypeMismatchError=()=>{throw new DOMException("The type of the object does not match the expected type","TypeMismatchError")},M.SecurityError=()=>{throw new DOMException("The operation is insecure","SecurityError")},M.NetworkError=()=>{throw new DOMException("A network error occurred","NetworkError")},M.AbortError=()=>{throw new DOMException("The operation was aborted","AbortError")},M.UrlMismatchError=()=>{throw new DOMException("The given URL does not match another URL","URLMismatchError")},M.QuotaExceededError=()=>{throw new DOMException("The quota has been exceeded","QuotaExceededError")},M.TimeoutError=()=>{throw new DOMException("The operation timed out","TimeoutError")},M.InvalidNodeTypeError=()=>{throw new DOMException("The node is of an invalid type","InvalidNodeTypeError")},M.DataCloneError=()=>{throw new DOMException("The object can not be cloned","DataCloneError")},M.InUseAttributeError=()=>{throw new DOMException("The attribute is already in use","InUseAttributeError")},M.nyi=function(){throw new Error("NotYetImplemented")},M.shouldOverride=function(){throw new Error("Abstract function; should be overriding in subclass.")},M.assert=function(_,f){if(!_)throw new Error("Assertion failed: "+(f||"")+`
`+new Error().stack)},M.expose=function(_,f){for(var i in _)Object.defineProperty(f.prototype,i,{value:_[i],writable:O})},M.merge=function(_,f){for(var i in f)_[i]=f[i]},M.documentOrder=function(_,f){return 3-(_.compareDocumentPosition(f)&6)},M.toASCIILowerCase=function(_){return _.replace(/[A-Z]+/g,function(f){return f.toLowerCase()})},M.toASCIIUpperCase=function(_){return _.replace(/[a-z]+/g,function(f){return f.toUpperCase()})}}}),Tn=Z({"external/npm/node_modules/domino/lib/EventTarget.js"(M,O){var _=Rt(),f=yn(),i=Se();O.exports=a;function a(){}a.prototype={addEventListener:function(c,u,s){if(u){s===void 0&&(s=!1),this._listeners||(this._listeners=Object.create(null)),this._listeners[c]||(this._listeners[c]=[]);for(var v=this._listeners[c],p=0,b=v.length;p<b;p++){var D=v[p];if(D.listener===u&&D.capture===s)return}var F={listener:u,capture:s};typeof u=="function"&&(F.f=u),v.push(F)}},removeEventListener:function(c,u,s){if(s===void 0&&(s=!1),this._listeners){var v=this._listeners[c];if(v)for(var p=0,b=v.length;p<b;p++){var D=v[p];if(D.listener===u&&D.capture===s){v.length===1?this._listeners[c]=void 0:v.splice(p,1);return}}}},dispatchEvent:function(c){return this._dispatchEvent(c,!1)},_dispatchEvent:function(c,u){typeof u!="boolean"&&(u=!1);function s(A,ee){var R=ee.type,y=ee.eventPhase;if(ee.currentTarget=A,y!==_.CAPTURING_PHASE&&A._handlers&&A._handlers[R]){var m=A._handlers[R],T;if(typeof m=="function")T=m.call(ee.currentTarget,ee);else{var h=m.handleEvent;if(typeof h!="function")throw new TypeError("handleEvent property of event handler object isnot a function.");T=h.call(m,ee)}switch(ee.type){case"mouseover":T===!0&&ee.preventDefault();break;case"beforeunload":default:T===!1&&ee.preventDefault();break}}var se=A._listeners&&A._listeners[R];if(se){se=se.slice();for(var le=0,ke=se.length;le<ke;le++){if(ee._immediatePropagationStopped)return;var j=se[le];if(!(y===_.CAPTURING_PHASE&&!j.capture||y===_.BUBBLING_PHASE&&j.capture))if(j.f)j.f.call(ee.currentTarget,ee);else{var k=j.listener.handleEvent;if(typeof k!="function")throw new TypeError("handleEvent property of event listener object is not a function.");k.call(j.listener,ee)}}}}(!c._initialized||c._dispatching)&&i.InvalidStateError(),c.isTrusted=u,c._dispatching=!0,c.target=this;for(var v=[],p=this.parentNode;p;p=p.parentNode)v.push(p);c.eventPhase=_.CAPTURING_PHASE;for(var b=v.length-1;b>=0&&(s(v[b],c),!c._propagationStopped);b--);if(c._propagationStopped||(c.eventPhase=_.AT_TARGET,s(this,c)),c.bubbles&&!c._propagationStopped){c.eventPhase=_.BUBBLING_PHASE;for(var D=0,F=v.length;D<F&&(s(v[D],c),!c._propagationStopped);D++);}if(c._dispatching=!1,c.eventPhase=_.AT_TARGET,c.currentTarget=null,u&&!c.defaultPrevented&&c instanceof f)switch(c.type){case"mousedown":this._armed={x:c.clientX,y:c.clientY,t:c.timeStamp};break;case"mouseout":case"mouseover":this._armed=null;break;case"mouseup":this._isClick(c)&&this._doClick(c),this._armed=null;break}return!c.defaultPrevented},_isClick:function(l){return this._armed!==null&&l.type==="mouseup"&&l.isTrusted&&l.button===0&&l.timeStamp-this._armed.t<1e3&&Math.abs(l.clientX-this._armed.x)<10&&Math.abs(l.clientY-this._armed.Y)<10},_doClick:function(l){if(!this._click_in_progress){this._click_in_progress=!0;for(var c=this;c&&!c._post_click_activation_steps;)c=c.parentNode;c&&c._pre_click_activation_steps&&c._pre_click_activation_steps();var u=this.ownerDocument.createEvent("MouseEvent");u.initMouseEvent("click",!0,!0,this.ownerDocument.defaultView,1,l.screenX,l.screenY,l.clientX,l.clientY,l.ctrlKey,l.altKey,l.shiftKey,l.metaKey,l.button,null);var s=this._dispatchEvent(u,!0);c&&(s?c._post_click_activation_steps&&c._post_click_activation_steps(u):c._cancelled_activation_steps&&c._cancelled_activation_steps())}},_setEventHandler:function(c,u){this._handlers||(this._handlers=Object.create(null)),this._handlers[c]=u},_getEventHandler:function(c){return this._handlers&&this._handlers[c]||null}}}}),wn=Z({"external/npm/node_modules/domino/lib/LinkedList.js"(M,O){var _=Se(),f=O.exports={valid:function(i){return _.assert(i,"list falsy"),_.assert(i._previousSibling,"previous falsy"),_.assert(i._nextSibling,"next falsy"),!0},insertBefore:function(i,a){_.assert(f.valid(i)&&f.valid(a));var l=i,c=i._previousSibling,u=a,s=a._previousSibling;l._previousSibling=s,c._nextSibling=u,s._nextSibling=l,u._previousSibling=c,_.assert(f.valid(i)&&f.valid(a))},replace:function(i,a){_.assert(f.valid(i)&&(a===null||f.valid(a))),a!==null&&f.insertBefore(a,i),f.remove(i),_.assert(f.valid(i)&&(a===null||f.valid(a)))},remove:function(i){_.assert(f.valid(i));var a=i._previousSibling;if(a!==i){var l=i._nextSibling;a._nextSibling=l,l._previousSibling=a,i._previousSibling=i._nextSibling=i,_.assert(f.valid(i))}}}}}),Nn=Z({"external/npm/node_modules/domino/lib/NodeUtils.js"(M,O){O.exports={serializeOne:ee,\u0275escapeMatchingClosingTag:b,\u0275escapeClosingCommentTag:F,\u0275escapeProcessingInstructionContent:A};var _=Se(),f=_.NAMESPACE,i={STYLE:!0,SCRIPT:!0,XMP:!0,IFRAME:!0,NOEMBED:!0,NOFRAMES:!0,PLAINTEXT:!0},a={area:!0,base:!0,basefont:!0,bgsound:!0,br:!0,col:!0,embed:!0,frame:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0},l={},c=/[&<>\u00A0]/g,u=/[&"<>\u00A0]/g;function s(R){return c.test(R)?R.replace(c,y=>{switch(y){case"&":return"&amp;";case"<":return"&lt;";case">":return"&gt;";case"\xA0":return"&nbsp;"}}):R}function v(R){return u.test(R)?R.replace(u,y=>{switch(y){case"<":return"&lt;";case">":return"&gt;";case"&":return"&amp;";case'"':return"&quot;";case"\xA0":return"&nbsp;"}}):R}function p(R){var y=R.namespaceURI;return y?y===f.XML?"xml:"+R.localName:y===f.XLINK?"xlink:"+R.localName:y===f.XMLNS?R.localName==="xmlns"?"xmlns":"xmlns:"+R.localName:R.name:R.localName}function b(R,y){let m="</"+y;if(!R.toLowerCase().includes(m))return R;let T=[...R],h=R.matchAll(new RegExp(m,"ig"));for(let se of h)T[se.index]="&lt;";return T.join("")}var D=/--!?>/;function F(R){return D.test(R)?R.replace(/(--\!?)>/g,"$1&gt;"):R}function A(R){return R.includes(">")?R.replaceAll(">","&gt;"):R}function ee(R,y){var m="";switch(R.nodeType){case 1:var T=R.namespaceURI,h=T===f.HTML,se=h||T===f.SVG||T===f.MATHML?R.localName:R.tagName;m+="<"+se;for(var le=0,ke=R._numattrs;le<ke;le++){var j=R._attr(le);m+=" "+p(j),j.value!==void 0&&(m+='="'+v(j.value)+'"')}if(m+=">",!(h&&a[se])){var k=R.serialize();i[se.toUpperCase()]&&(k=b(k,se)),h&&l[se]&&k.charAt(0)===`
`&&(m+=`
`),m+=k,m+="</"+se+">"}break;case 3:case 4:var I;y.nodeType===1&&y.namespaceURI===f.HTML?I=y.tagName:I="",i[I]||I==="NOSCRIPT"&&y.ownerDocument._scripting_enabled?m+=R.data:m+=s(R.data);break;case 8:m+="<!--"+F(R.data)+"-->";break;case 7:let Y=A(R.data);m+="<?"+R.target+" "+Y+"?>";break;case 10:m+="<!DOCTYPE "+R.name,m+=">";break;default:_.InvalidStateError()}return m}}}),xe=Z({"external/npm/node_modules/domino/lib/Node.js"(M,O){O.exports=l;var _=Tn(),f=wn(),i=Nn(),a=Se();function l(){_.call(this),this.parentNode=null,this._nextSibling=this._previousSibling=this,this._index=void 0}var c=l.ELEMENT_NODE=1,u=l.ATTRIBUTE_NODE=2,s=l.TEXT_NODE=3,v=l.CDATA_SECTION_NODE=4,p=l.ENTITY_REFERENCE_NODE=5,b=l.ENTITY_NODE=6,D=l.PROCESSING_INSTRUCTION_NODE=7,F=l.COMMENT_NODE=8,A=l.DOCUMENT_NODE=9,ee=l.DOCUMENT_TYPE_NODE=10,R=l.DOCUMENT_FRAGMENT_NODE=11,y=l.NOTATION_NODE=12,m=l.DOCUMENT_POSITION_DISCONNECTED=1,T=l.DOCUMENT_POSITION_PRECEDING=2,h=l.DOCUMENT_POSITION_FOLLOWING=4,se=l.DOCUMENT_POSITION_CONTAINS=8,le=l.DOCUMENT_POSITION_CONTAINED_BY=16,ke=l.DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC=32;l.prototype=Object.create(_.prototype,{baseURI:{get:a.nyi},parentElement:{get:function(){return this.parentNode&&this.parentNode.nodeType===c?this.parentNode:null}},hasChildNodes:{value:a.shouldOverride},firstChild:{get:a.shouldOverride},lastChild:{get:a.shouldOverride},isConnected:{get:function(){let j=this;for(;j!=null;){if(j.nodeType===l.DOCUMENT_NODE)return!0;j=j.parentNode,j!=null&&j.nodeType===l.DOCUMENT_FRAGMENT_NODE&&(j=j.host)}return!1}},previousSibling:{get:function(){var j=this.parentNode;return!j||this===j.firstChild?null:this._previousSibling}},nextSibling:{get:function(){var j=this.parentNode,k=this._nextSibling;return!j||k===j.firstChild?null:k}},textContent:{get:function(){return null},set:function(j){}},innerText:{get:function(){return null},set:function(j){}},_countChildrenOfType:{value:function(j){for(var k=0,I=this.firstChild;I!==null;I=I.nextSibling)I.nodeType===j&&k++;return k}},_ensureInsertValid:{value:function(k,I,Y){var t=this,r,o;if(!k.nodeType)throw new TypeError("not a node");switch(t.nodeType){case A:case R:case c:break;default:a.HierarchyRequestError()}switch(k.isAncestor(t)&&a.HierarchyRequestError(),(I!==null||!Y)&&I.parentNode!==t&&a.NotFoundError(),k.nodeType){case R:case ee:case c:case s:case D:case F:break;default:a.HierarchyRequestError()}if(t.nodeType===A)switch(k.nodeType){case s:a.HierarchyRequestError();break;case R:switch(k._countChildrenOfType(s)>0&&a.HierarchyRequestError(),k._countChildrenOfType(c)){case 0:break;case 1:if(I!==null)for(Y&&I.nodeType===ee&&a.HierarchyRequestError(),o=I.nextSibling;o!==null;o=o.nextSibling)o.nodeType===ee&&a.HierarchyRequestError();r=t._countChildrenOfType(c),Y?r>0&&a.HierarchyRequestError():(r>1||r===1&&I.nodeType!==c)&&a.HierarchyRequestError();break;default:a.HierarchyRequestError()}break;case c:if(I!==null)for(Y&&I.nodeType===ee&&a.HierarchyRequestError(),o=I.nextSibling;o!==null;o=o.nextSibling)o.nodeType===ee&&a.HierarchyRequestError();r=t._countChildrenOfType(c),Y?r>0&&a.HierarchyRequestError():(r>1||r===1&&I.nodeType!==c)&&a.HierarchyRequestError();break;case ee:if(I===null)t._countChildrenOfType(c)&&a.HierarchyRequestError();else for(o=t.firstChild;o!==null&&o!==I;o=o.nextSibling)o.nodeType===c&&a.HierarchyRequestError();r=t._countChildrenOfType(ee),Y?r>0&&a.HierarchyRequestError():(r>1||r===1&&I.nodeType!==ee)&&a.HierarchyRequestError();break}else k.nodeType===ee&&a.HierarchyRequestError()}},insertBefore:{value:function(k,I){var Y=this;Y._ensureInsertValid(k,I,!0);var t=I;return t===k&&(t=k.nextSibling),Y.doc.adoptNode(k),k._insertOrReplace(Y,t,!1),k}},appendChild:{value:function(j){return this.insertBefore(j,null)}},_appendChild:{value:function(j){j._insertOrReplace(this,null,!1)}},removeChild:{value:function(k){var I=this;if(!k.nodeType)throw new TypeError("not a node");return k.parentNode!==I&&a.NotFoundError(),k.remove(),k}},replaceChild:{value:function(k,I){var Y=this;return Y._ensureInsertValid(k,I,!1),k.doc!==Y.doc&&Y.doc.adoptNode(k),k._insertOrReplace(Y,I,!0),I}},contains:{value:function(k){return k===null?!1:this===k?!0:(this.compareDocumentPosition(k)&le)!==0}},compareDocumentPosition:{value:function(k){if(this===k)return 0;if(this.doc!==k.doc||this.rooted!==k.rooted)return m+ke;for(var I=[],Y=[],t=this;t!==null;t=t.parentNode)I.push(t);for(t=k;t!==null;t=t.parentNode)Y.push(t);if(I.reverse(),Y.reverse(),I[0]!==Y[0])return m+ke;t=Math.min(I.length,Y.length);for(var r=1;r<t;r++)if(I[r]!==Y[r])return I[r].index<Y[r].index?h:T;return I.length<Y.length?h+le:T+se}},isSameNode:{value:function(k){return this===k}},isEqualNode:{value:function(k){if(!k||k.nodeType!==this.nodeType||!this.isEqual(k))return!1;for(var I=this.firstChild,Y=k.firstChild;I&&Y;I=I.nextSibling,Y=Y.nextSibling)if(!I.isEqualNode(Y))return!1;return I===null&&Y===null}},cloneNode:{value:function(j){var k=this.clone();if(j)for(var I=this.firstChild;I!==null;I=I.nextSibling)k._appendChild(I.cloneNode(!0));return k}},lookupPrefix:{value:function(k){var I;if(k===""||k===null||k===void 0)return null;switch(this.nodeType){case c:return this._lookupNamespacePrefix(k,this);case A:return I=this.documentElement,I?I.lookupPrefix(k):null;case b:case y:case R:case ee:return null;case u:return I=this.ownerElement,I?I.lookupPrefix(k):null;default:return I=this.parentElement,I?I.lookupPrefix(k):null}}},lookupNamespaceURI:{value:function(k){(k===""||k===void 0)&&(k=null);var I;switch(this.nodeType){case c:return a.shouldOverride();case A:return I=this.documentElement,I?I.lookupNamespaceURI(k):null;case b:case y:case ee:case R:return null;case u:return I=this.ownerElement,I?I.lookupNamespaceURI(k):null;default:return I=this.parentElement,I?I.lookupNamespaceURI(k):null}}},isDefaultNamespace:{value:function(k){(k===""||k===void 0)&&(k=null);var I=this.lookupNamespaceURI(null);return I===k}},index:{get:function(){var j=this.parentNode;if(this===j.firstChild)return 0;var k=j.childNodes;if(this._index===void 0||k[this._index]!==this){for(var I=0;I<k.length;I++)k[I]._index=I;a.assert(k[this._index]===this)}return this._index}},isAncestor:{value:function(j){if(this.doc!==j.doc||this.rooted!==j.rooted)return!1;for(var k=j;k;k=k.parentNode)if(k===this)return!0;return!1}},ensureSameDoc:{value:function(j){j.ownerDocument===null?j.ownerDocument=this.doc:j.ownerDocument!==this.doc&&a.WrongDocumentError()}},removeChildren:{value:a.shouldOverride},_insertOrReplace:{value:function(k,I,Y){var t=this,r,o;if(t.nodeType===R&&t.rooted&&a.HierarchyRequestError(),k._childNodes&&(r=I===null?k._childNodes.length:I.index,t.parentNode===k)){var w=t.index;w<r&&r--}Y&&(I.rooted&&I.doc.mutateRemove(I),I.parentNode=null);var C=I;C===null&&(C=k.firstChild);var P=t.rooted&&k.rooted;if(t.nodeType===R){for(var K=[0,Y?1:0],ce,oe=t.firstChild;oe!==null;oe=ce)ce=oe.nextSibling,K.push(oe),oe.parentNode=k;var g=K.length;if(Y?f.replace(C,g>2?K[2]:null):g>2&&C!==null&&f.insertBefore(K[2],C),k._childNodes)for(K[0]=I===null?k._childNodes.length:I._index,k._childNodes.splice.apply(k._childNodes,K),o=2;o<g;o++)K[o]._index=K[0]+(o-2);else k._firstChild===I&&(g>2?k._firstChild=K[2]:Y&&(k._firstChild=null));if(t._childNodes?t._childNodes.length=0:t._firstChild=null,k.rooted)for(k.modify(),o=2;o<g;o++)k.doc.mutateInsert(K[o])}else{if(I===t)return;P?t._remove():t.parentNode&&t.remove(),t.parentNode=k,Y?(f.replace(C,t),k._childNodes?(t._index=r,k._childNodes[r]=t):k._firstChild===I&&(k._firstChild=t)):(C!==null&&f.insertBefore(t,C),k._childNodes?(t._index=r,k._childNodes.splice(r,0,t)):k._firstChild===I&&(k._firstChild=t)),P?(k.modify(),k.doc.mutateMove(t)):k.rooted&&(k.modify(),k.doc.mutateInsert(t))}}},lastModTime:{get:function(){return this._lastModTime||(this._lastModTime=this.doc.modclock),this._lastModTime}},modify:{value:function(){if(this.doc.modclock)for(var j=++this.doc.modclock,k=this;k;k=k.parentElement)k._lastModTime&&(k._lastModTime=j)}},doc:{get:function(){return this.ownerDocument||this}},rooted:{get:function(){return!!this._nid}},normalize:{value:function(){for(var j,k=this.firstChild;k!==null;k=j)if(j=k.nextSibling,k.normalize&&k.normalize(),k.nodeType===l.TEXT_NODE){if(k.nodeValue===""){this.removeChild(k);continue}var I=k.previousSibling;I!==null&&I.nodeType===l.TEXT_NODE&&(I.appendData(k.nodeValue),this.removeChild(k))}}},serialize:{value:function(){if(this._innerHTML)return this._innerHTML;for(var j="",k=this.firstChild;k!==null;k=k.nextSibling)j+=i.serializeOne(k,this);return j}},outerHTML:{get:function(){return i.serializeOne(this,{nodeType:0})},set:a.nyi},ELEMENT_NODE:{value:c},ATTRIBUTE_NODE:{value:u},TEXT_NODE:{value:s},CDATA_SECTION_NODE:{value:v},ENTITY_REFERENCE_NODE:{value:p},ENTITY_NODE:{value:b},PROCESSING_INSTRUCTION_NODE:{value:D},COMMENT_NODE:{value:F},DOCUMENT_NODE:{value:A},DOCUMENT_TYPE_NODE:{value:ee},DOCUMENT_FRAGMENT_NODE:{value:R},NOTATION_NODE:{value:y},DOCUMENT_POSITION_DISCONNECTED:{value:m},DOCUMENT_POSITION_PRECEDING:{value:T},DOCUMENT_POSITION_FOLLOWING:{value:h},DOCUMENT_POSITION_CONTAINS:{value:se},DOCUMENT_POSITION_CONTAINED_BY:{value:le},DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC:{value:ke}})}}),ri=Z({"external/npm/node_modules/domino/lib/NodeList.es6.js"(M,O){O.exports=class extends Array{constructor(f){if(super(f&&f.length||0),f)for(var i in f)this[i]=f[i]}item(f){return this[f]||null}}}}),ni=Z({"external/npm/node_modules/domino/lib/NodeList.es5.js"(M,O){function _(i){return this[i]||null}function f(i){return i||(i=[]),i.item=_,i}O.exports=f}}),kt=Z({"external/npm/node_modules/domino/lib/NodeList.js"(M,O){var _;try{_=ri()}catch{_=ni()}O.exports=_}}),Hr=Z({"external/npm/node_modules/domino/lib/ContainerNode.js"(M,O){O.exports=i;var _=xe(),f=kt();function i(){_.call(this),this._firstChild=this._childNodes=null}i.prototype=Object.create(_.prototype,{hasChildNodes:{value:function(){return this._childNodes?this._childNodes.length>0:this._firstChild!==null}},childNodes:{get:function(){return this._ensureChildNodes(),this._childNodes}},firstChild:{get:function(){return this._childNodes?this._childNodes.length===0?null:this._childNodes[0]:this._firstChild}},lastChild:{get:function(){var a=this._childNodes,l;return a?a.length===0?null:a[a.length-1]:(l=this._firstChild,l===null?null:l._previousSibling)}},_ensureChildNodes:{value:function(){if(!this._childNodes){var a=this._firstChild,l=a,c=this._childNodes=new f;if(a)do c.push(l),l=l._nextSibling;while(l!==a);this._firstChild=null}}},removeChildren:{value:function(){for(var l=this.rooted?this.ownerDocument:null,c=this.firstChild,u;c!==null;)u=c,c=u.nextSibling,l&&l.mutateRemove(u),u.parentNode=null;this._childNodes?this._childNodes.length=0:this._firstChild=null,this.modify()}}})}}),qr=Z({"external/npm/node_modules/domino/lib/xmlnames.js"(M){M.isValidName=A,M.isValidQName=ee;var O=/^[_:A-Za-z][-.:\w]+$/,_=/^([_A-Za-z][-.\w]+|[_A-Za-z][-.\w]+:[_A-Za-z][-.\w]+)$/,f="_A-Za-z\xC0-\xD6\xD8-\xF6\xF8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD",i="-._A-Za-z0-9\xB7\xC0-\xD6\xD8-\xF6\xF8-\u02FF\u0300-\u037D\u037F-\u1FFF\u200C\u200D\u203F\u2040\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD",a="["+f+"]["+i+"]*",l=f+":",c=i+":",u=new RegExp("^["+l+"]["+c+"]*$"),s=new RegExp("^("+a+"|"+a+":"+a+")$"),v=/[\uD800-\uDB7F\uDC00-\uDFFF]/,p=/[\uD800-\uDB7F\uDC00-\uDFFF]/g,b=/[\uD800-\uDB7F][\uDC00-\uDFFF]/g;f+="\uD800-\u{EFC00}-\uDFFF",i+="\uD800-\u{EFC00}-\uDFFF",a="["+f+"]["+i+"]*",l=f+":",c=i+":";var D=new RegExp("^["+l+"]["+c+"]*$"),F=new RegExp("^("+a+"|"+a+":"+a+")$");function A(R){if(O.test(R)||u.test(R))return!0;if(!v.test(R)||!D.test(R))return!1;var y=R.match(p),m=R.match(b);return m!==null&&2*m.length===y.length}function ee(R){if(_.test(R)||s.test(R))return!0;if(!v.test(R)||!F.test(R))return!1;var y=R.match(p),m=R.match(b);return m!==null&&2*m.length===y.length}}}),Sn=Z({"external/npm/node_modules/domino/lib/attributes.js"(M){var O=Se();M.property=function(f){if(Array.isArray(f.type)){var i=Object.create(null);f.type.forEach(function(c){i[c.value||c]=c.alias||c});var a=f.missing;a===void 0&&(a=null);var l=f.invalid;return l===void 0&&(l=a),{get:function(){var c=this._getattr(f.name);return c===null?a:(c=i[c.toLowerCase()],c!==void 0?c:l!==null?l:c)},set:function(c){this._setattr(f.name,c)}}}else{if(f.type===Boolean)return{get:function(){return this.hasAttribute(f.name)},set:function(c){c?this._setattr(f.name,""):this.removeAttribute(f.name)}};if(f.type===Number||f.type==="long"||f.type==="unsigned long"||f.type==="limited unsigned long with fallback")return _(f);if(!f.type||f.type===String)return{get:function(){return this._getattr(f.name)||""},set:function(c){f.treatNullAsEmptyString&&c===null&&(c=""),this._setattr(f.name,c)}};if(typeof f.type=="function")return f.type(f.name,f)}throw new Error("Invalid attribute definition")};function _(f){var i;typeof f.default=="function"?i=f.default:typeof f.default=="number"?i=function(){return f.default}:i=function(){O.assert(!1,typeof f.default)};var a=f.type==="unsigned long",l=f.type==="long",c=f.type==="limited unsigned long with fallback",u=f.min,s=f.max,v=f.setmin;return u===void 0&&(a&&(u=0),l&&(u=-2147483648),c&&(u=1)),s===void 0&&(a||l||c)&&(s=2147483647),{get:function(){var p=this._getattr(f.name),b=f.float?parseFloat(p):parseInt(p,10);if(p===null||!isFinite(b)||u!==void 0&&b<u||s!==void 0&&b>s)return i.call(this);if(a||l||c){if(!/^[ \t\n\f\r]*[-+]?[0-9]/.test(p))return i.call(this);b=b|0}return b},set:function(p){f.float||(p=Math.floor(p)),v!==void 0&&p<v&&O.IndexSizeError(f.name+" set to "+p),a?p=p<0||p>2147483647?i.call(this):p|0:c?p=p<1||p>2147483647?i.call(this):p|0:l&&(p=p<-2147483648||p>2147483647?i.call(this):p|0),this._setattr(f.name,String(p))}}}M.registerChangeHandler=function(f,i,a){var l=f.prototype;Object.prototype.hasOwnProperty.call(l,"_attributeChangeHandlers")||(l._attributeChangeHandlers=Object.create(l._attributeChangeHandlers||null)),l._attributeChangeHandlers[i]=a}}}),ai=Z({"external/npm/node_modules/domino/lib/FilteredElementList.js"(M,O){O.exports=f;var _=xe();function f(i,a){this.root=i,this.filter=a,this.lastModTime=i.lastModTime,this.done=!1,this.cache=[],this.traverse()}f.prototype=Object.create(Object.prototype,{length:{get:function(){return this.checkcache(),this.done||this.traverse(),this.cache.length}},item:{value:function(i){return this.checkcache(),!this.done&&i>=this.cache.length&&this.traverse(),this.cache[i]}},checkcache:{value:function(){if(this.lastModTime!==this.root.lastModTime){for(var i=this.cache.length-1;i>=0;i--)this[i]=void 0;this.cache.length=0,this.done=!1,this.lastModTime=this.root.lastModTime}}},traverse:{value:function(i){i!==void 0&&i++;for(var a;(a=this.next())!==null;)if(this[this.cache.length]=a,this.cache.push(a),i&&this.cache.length===i)return;this.done=!0}},next:{value:function(){var i=this.cache.length===0?this.root:this.cache[this.cache.length-1],a;for(i.nodeType===_.DOCUMENT_NODE?a=i.documentElement:a=i.nextElement(this.root);a;){if(this.filter(a))return a;a=a.nextElement(this.root)}return null}}})}}),kn=Z({"external/npm/node_modules/domino/lib/DOMTokenList.js"(M,O){var _=Se();O.exports=f;function f(u,s){this._getString=u,this._setString=s,this._length=0,this._lastStringValue="",this._update()}Object.defineProperties(f.prototype,{length:{get:function(){return this._length}},item:{value:function(u){var s=c(this);return u<0||u>=s.length?null:s[u]}},contains:{value:function(u){u=String(u);var s=c(this);return s.indexOf(u)>-1}},add:{value:function(){for(var u=c(this),s=0,v=arguments.length;s<v;s++){var p=a(arguments[s]);u.indexOf(p)<0&&u.push(p)}this._update(u)}},remove:{value:function(){for(var u=c(this),s=0,v=arguments.length;s<v;s++){var p=a(arguments[s]),b=u.indexOf(p);b>-1&&u.splice(b,1)}this._update(u)}},toggle:{value:function(s,v){return s=a(s),this.contains(s)?v===void 0||v===!1?(this.remove(s),!1):!0:v===void 0||v===!0?(this.add(s),!0):!1}},replace:{value:function(s,v){String(v)===""&&_.SyntaxError(),s=a(s),v=a(v);var p=c(this),b=p.indexOf(s);if(b<0)return!1;var D=p.indexOf(v);return D<0?p[b]=v:b<D?(p[b]=v,p.splice(D,1)):p.splice(b,1),this._update(p),!0}},toString:{value:function(){return this._getString()}},value:{get:function(){return this._getString()},set:function(u){this._setString(u),this._update()}},_update:{value:function(u){u?(i(this,u),this._setString(u.join(" ").trim())):i(this,c(this)),this._lastStringValue=this._getString()}}});function i(u,s){var v=u._length,p;for(u._length=s.length,p=0;p<s.length;p++)u[p]=s[p];for(;p<v;p++)u[p]=void 0}function a(u){return u=String(u),u===""&&_.SyntaxError(),/[ \t\r\n\f]/.test(u)&&_.InvalidCharacterError(),u}function l(u){for(var s=u._length,v=Array(s),p=0;p<s;p++)v[p]=u[p];return v}function c(u){var s=u._getString();if(s===u._lastStringValue)return l(u);var v=s.replace(/(^[ \t\r\n\f]+)|([ \t\r\n\f]+$)/g,"");if(v==="")return[];var p=Object.create(null);return v.split(/[ \t\r\n\f]+/g).filter(function(b){var D="$"+b;return p[D]?!1:(p[D]=!0,!0)})}}}),Rr=Z({"external/npm/node_modules/domino/lib/select.js"(M,O){var _=Object.create(null,{location:{get:function(){throw new Error("window.location is not supported.")}}}),f=function(t,r){return t.compareDocumentPosition(r)},i=function(t,r){return f(t,r)&2?1:-1},a=function(t){for(;(t=t.nextSibling)&&t.nodeType!==1;);return t},l=function(t){for(;(t=t.previousSibling)&&t.nodeType!==1;);return t},c=function(t){if(t=t.firstChild)for(;t.nodeType!==1&&(t=t.nextSibling););return t},u=function(t){if(t=t.lastChild)for(;t.nodeType!==1&&(t=t.previousSibling););return t},s=function(t){if(!t.parentNode)return!1;var r=t.parentNode.nodeType;return r===1||r===9},v=function(t){if(!t)return t;var r=t[0];return r==='"'||r==="'"?(t[t.length-1]===r?t=t.slice(1,-1):t=t.slice(1),t.replace(h.str_escape,function(o){var w=/^\\(?:([0-9A-Fa-f]+)|([\r\n\f]+))/.exec(o);if(!w)return o.slice(1);if(w[2])return"";var C=parseInt(w[1],16);return String.fromCodePoint?String.fromCodePoint(C):String.fromCharCode(C)})):h.ident.test(t)?p(t):t},p=function(t){return t.replace(h.escape,function(r){var o=/^\\([0-9A-Fa-f]+)/.exec(r);if(!o)return r[1];var w=parseInt(o[1],16);return String.fromCodePoint?String.fromCodePoint(w):String.fromCharCode(w)})},b=function(){return Array.prototype.indexOf?Array.prototype.indexOf:function(t,r){for(var o=this.length;o--;)if(this[o]===r)return o;return-1}}(),D=function(t,r){var o=h.inside.source.replace(/</g,t).replace(/>/g,r);return new RegExp(o)},F=function(t,r,o){return t=t.source,t=t.replace(r,o.source||o),new RegExp(t)},A=function(t,r){return t.replace(/^(?:\w+:\/\/|\/+)/,"").replace(/(?:\/+|\/*#.*?)$/,"").split("/",r).join("/")},ee=function(t,r){var o=t.replace(/\s+/g,""),w;return o==="even"?o="2n+0":o==="odd"?o="2n+1":o.indexOf("n")===-1&&(o="0n"+o),w=/^([+-])?(\d+)?n([+-])?(\d+)?$/.exec(o),{group:w[1]==="-"?-(w[2]||1):+(w[2]||1),offset:w[4]?w[3]==="-"?-w[4]:+w[4]:0}},R=function(t,r,o){var w=ee(t),C=w.group,P=w.offset,K=o?u:c,ce=o?l:a;return function(oe){if(s(oe))for(var g=K(oe.parentNode),L=0;g;){if(r(g,oe)&&L++,g===oe)return L-=P,C&&L?L%C===0&&L<0==C<0:!L;g=ce(g)}}},y={"*":function(){return function(){return!0}}(),type:function(t){return t=t.toLowerCase(),function(r){return r.nodeName.toLowerCase()===t}},attr:function(t,r,o,w){return r=m[r],function(C){var P;switch(t){case"for":P=C.htmlFor;break;case"class":P=C.className,P===""&&C.getAttribute("class")==null&&(P=null);break;case"href":case"src":P=C.getAttribute(t,2);break;case"title":P=C.getAttribute("title")||null;break;case"id":case"lang":case"dir":case"accessKey":case"hidden":case"tabIndex":case"style":if(C.getAttribute){P=C.getAttribute(t);break}default:if(C.hasAttribute&&!C.hasAttribute(t))break;P=C[t]!=null?C[t]:C.getAttribute&&C.getAttribute(t);break}if(P!=null)return P=P+"",w&&(P=P.toLowerCase(),o=o.toLowerCase()),r(P,o)}},":first-child":function(t){return!l(t)&&s(t)},":last-child":function(t){return!a(t)&&s(t)},":only-child":function(t){return!l(t)&&!a(t)&&s(t)},":nth-child":function(t,r){return R(t,function(){return!0},r)},":nth-last-child":function(t){return y[":nth-child"](t,!0)},":root":function(t){return t.ownerDocument.documentElement===t},":empty":function(t){return!t.firstChild},":not":function(t){var r=I(t);return function(o){return!r(o)}},":first-of-type":function(t){if(s(t)){for(var r=t.nodeName;t=l(t);)if(t.nodeName===r)return;return!0}},":last-of-type":function(t){if(s(t)){for(var r=t.nodeName;t=a(t);)if(t.nodeName===r)return;return!0}},":only-of-type":function(t){return y[":first-of-type"](t)&&y[":last-of-type"](t)},":nth-of-type":function(t,r){return R(t,function(o,w){return o.nodeName===w.nodeName},r)},":nth-last-of-type":function(t){return y[":nth-of-type"](t,!0)},":checked":function(t){return!!(t.checked||t.selected)},":indeterminate":function(t){return!y[":checked"](t)},":enabled":function(t){return!t.disabled&&t.type!=="hidden"},":disabled":function(t){return!!t.disabled},":target":function(t){return t.id===_.location.hash.substring(1)},":focus":function(t){return t===t.ownerDocument.activeElement},":is":function(t){return I(t)},":matches":function(t){return y[":is"](t)},":nth-match":function(t,r){var o=t.split(/\s*,\s*/),w=o.shift(),C=I(o.join(","));return R(w,C,r)},":nth-last-match":function(t){return y[":nth-match"](t,!0)},":links-here":function(t){return t+""==_.location+""},":lang":function(t){return function(r){for(;r;){if(r.lang)return r.lang.indexOf(t)===0;r=r.parentNode}}},":dir":function(t){return function(r){for(;r;){if(r.dir)return r.dir===t;r=r.parentNode}}},":scope":function(t,r){var o=r||t.ownerDocument;return o.nodeType===9?t===o.documentElement:t===o},":any-link":function(t){return typeof t.href=="string"},":local-link":function(t){if(t.nodeName)return t.href&&t.host===_.location.host;var r=+t+1;return function(o){if(o.href){var w=_.location+"",C=o+"";return A(w,r)===A(C,r)}}},":default":function(t){return!!t.defaultSelected},":valid":function(t){return t.willValidate||t.validity&&t.validity.valid},":invalid":function(t){return!y[":valid"](t)},":in-range":function(t){return t.value>t.min&&t.value<=t.max},":out-of-range":function(t){return!y[":in-range"](t)},":required":function(t){return!!t.required},":optional":function(t){return!t.required},":read-only":function(t){if(t.readOnly)return!0;var r=t.getAttribute("contenteditable"),o=t.contentEditable,w=t.nodeName.toLowerCase();return w=w!=="input"&&w!=="textarea",(w||t.disabled)&&r==null&&o!=="true"},":read-write":function(t){return!y[":read-only"](t)},":hover":function(){throw new Error(":hover is not supported.")},":active":function(){throw new Error(":active is not supported.")},":link":function(){throw new Error(":link is not supported.")},":visited":function(){throw new Error(":visited is not supported.")},":column":function(){throw new Error(":column is not supported.")},":nth-column":function(){throw new Error(":nth-column is not supported.")},":nth-last-column":function(){throw new Error(":nth-last-column is not supported.")},":current":function(){throw new Error(":current is not supported.")},":past":function(){throw new Error(":past is not supported.")},":future":function(){throw new Error(":future is not supported.")},":contains":function(t){return function(r){var o=r.innerText||r.textContent||r.value||"";return o.indexOf(t)!==-1}},":has":function(t){return function(r){return Y(t,r).length>0}}},m={"-":function(){return!0},"=":function(t,r){return t===r},"*=":function(t,r){return t.indexOf(r)!==-1},"~=":function(t,r){var o,w,C,P;for(w=0;;w=o+1){if(o=t.indexOf(r,w),o===-1)return!1;if(C=t[o-1],P=t[o+r.length],(!C||C===" ")&&(!P||P===" "))return!0}},"|=":function(t,r){var o=t.indexOf(r),w;if(o===0)return w=t[o+r.length],w==="-"||!w},"^=":function(t,r){return t.indexOf(r)===0},"$=":function(t,r){var o=t.lastIndexOf(r);return o!==-1&&o+r.length===t.length},"!=":function(t,r){return t!==r}},T={" ":function(t){return function(r){for(;r=r.parentNode;)if(t(r))return r}},">":function(t){return function(r){if(r=r.parentNode)return t(r)&&r}},"+":function(t){return function(r){if(r=l(r))return t(r)&&r}},"~":function(t){return function(r){for(;r=l(r);)if(t(r))return r}},noop:function(t){return function(r){return t(r)&&r}},ref:function(t,r){var o;function w(C){for(var P=C.ownerDocument,K=P.getElementsByTagName("*"),ce=K.length;ce--;)if(o=K[ce],w.test(C))return o=null,!0;o=null}return w.combinator=function(C){if(!(!o||!o.getAttribute)){var P=o.getAttribute(r)||"";if(P[0]==="#"&&(P=P.substring(1)),P===C.id&&t(o))return o}},w}},h={escape:/\\(?:[^0-9A-Fa-f\r\n]|[0-9A-Fa-f]{1,6}[\r\n\t ]?)/g,str_escape:/(escape)|\\(\n|\r\n?|\f)/g,nonascii:/[\u00A0-\uFFFF]/,cssid:/(?:(?!-?[0-9])(?:escape|nonascii|[-_a-zA-Z0-9])+)/,qname:/^ *(cssid|\*)/,simple:/^(?:([.#]cssid)|pseudo|attr)/,ref:/^ *\/(cssid)\/ */,combinator:/^(?: +([^ \w*.#\\]) +|( )+|([^ \w*.#\\]))(?! *$)/,attr:/^\[(cssid)(?:([^\w]?=)(inside))?\]/,pseudo:/^(:cssid)(?:\((inside)\))?/,inside:/(?:"(?:\\"|[^"])*"|'(?:\\'|[^'])*'|<[^"'>]*>|\\["'>]|[^"'>])*/,ident:/^(cssid)$/};h.cssid=F(h.cssid,"nonascii",h.nonascii),h.cssid=F(h.cssid,"escape",h.escape),h.qname=F(h.qname,"cssid",h.cssid),h.simple=F(h.simple,"cssid",h.cssid),h.ref=F(h.ref,"cssid",h.cssid),h.attr=F(h.attr,"cssid",h.cssid),h.pseudo=F(h.pseudo,"cssid",h.cssid),h.inside=F(h.inside,`[^"'>]*`,h.inside),h.attr=F(h.attr,"inside",D("\\[","\\]")),h.pseudo=F(h.pseudo,"inside",D("\\(","\\)")),h.simple=F(h.simple,"pseudo",h.pseudo),h.simple=F(h.simple,"attr",h.attr),h.ident=F(h.ident,"cssid",h.cssid),h.str_escape=F(h.str_escape,"escape",h.escape);var se=function(t){for(var r=t.replace(/^\s+|\s+$/g,""),o,w=[],C=[],P,K,ce,oe,g;r;){if(ce=h.qname.exec(r))r=r.substring(ce[0].length),K=p(ce[1]),C.push(le(K,!0));else if(ce=h.simple.exec(r))r=r.substring(ce[0].length),K="*",C.push(le(K,!0)),C.push(le(ce));else throw new SyntaxError("Invalid selector.");for(;ce=h.simple.exec(r);)r=r.substring(ce[0].length),C.push(le(ce));if(r[0]==="!"&&(r=r.substring(1),P=k(),P.qname=K,C.push(P.simple)),ce=h.ref.exec(r)){r=r.substring(ce[0].length),g=T.ref(ke(C),p(ce[1])),w.push(g.combinator),C=[];continue}if(ce=h.combinator.exec(r)){if(r=r.substring(ce[0].length),oe=ce[1]||ce[2]||ce[3],oe===","){w.push(T.noop(ke(C)));break}}else oe="noop";if(!T[oe])throw new SyntaxError("Bad combinator.");w.push(T[oe](ke(C))),C=[]}return o=j(w),o.qname=K,o.sel=r,P&&(P.lname=o.qname,P.test=o,P.qname=P.qname,P.sel=o.sel,o=P),g&&(g.test=o,g.qname=o.qname,g.sel=o.sel,o=g),o},le=function(t,r){if(r)return t==="*"?y["*"]:y.type(t);if(t[1])return t[1][0]==="."?y.attr("class","~=",p(t[1].substring(1)),!1):y.attr("id","=",p(t[1].substring(1)),!1);if(t[2])return t[3]?y[p(t[2])](v(t[3])):y[p(t[2])];if(t[4]){var o=t[6],w=/["'\s]\s*I$/i.test(o);return w&&(o=o.replace(/\s*I$/i,"")),y.attr(p(t[4]),t[5]||"-",v(o),w)}throw new SyntaxError("Unknown Selector.")},ke=function(t){var r=t.length,o;return r<2?t[0]:function(w){if(w){for(o=0;o<r;o++)if(!t[o](w))return;return!0}}},j=function(t){return t.length<2?function(r){return!!t[0](r)}:function(r){for(var o=t.length;o--;)if(!(r=t[o](r)))return;return!0}},k=function(){var t;function r(o){for(var w=o.ownerDocument,C=w.getElementsByTagName(r.lname),P=C.length;P--;)if(r.test(C[P])&&t===o)return t=null,!0;t=null}return r.simple=function(o){return t=o,!0},r},I=function(t){for(var r=se(t),o=[r];r.sel;)r=se(r.sel),o.push(r);return o.length<2?r:function(w){for(var C=o.length,P=0;P<C;P++)if(o[P](w))return!0}},Y=function(t,r){for(var o=[],w=se(t),C=r.getElementsByTagName(w.qname),P=0,K;K=C[P++];)w(K)&&o.push(K);if(w.sel){for(;w.sel;)for(w=se(w.sel),C=r.getElementsByTagName(w.qname),P=0;K=C[P++];)w(K)&&b.call(o,K)===-1&&o.push(K);o.sort(i)}return o};O.exports=M=function(t,r){var o,w;if(r.nodeType!==11&&t.indexOf(" ")===-1){if(t[0]==="#"&&r.rooted&&/^#[A-Z_][-A-Z0-9_]*$/i.test(t)&&r.doc._hasMultipleElementsWithId&&(o=t.substring(1),!r.doc._hasMultipleElementsWithId(o)))return w=r.doc.getElementById(o),w?[w]:[];if(t[0]==="."&&/^\.\w+$/.test(t))return r.getElementsByClassName(t.substring(1));if(/^\w+$/.test(t))return r.getElementsByTagName(t)}return Y(t,r)},M.selectors=y,M.operators=m,M.combinators=T,M.matches=function(t,r){var o={sel:r};do if(o=se(o.sel),o(t))return!0;while(o.sel);return!1}}}),Br=Z({"external/npm/node_modules/domino/lib/ChildNode.js"(M,O){var _=xe(),f=wn(),i=function(l,c){for(var u=l.createDocumentFragment(),s=0;s<c.length;s++){var v=c[s],p=v instanceof _;u.appendChild(p?v:l.createTextNode(String(v)))}return u},a={after:{value:function(){var c=Array.prototype.slice.call(arguments),u=this.parentNode,s=this.nextSibling;if(u!==null){for(;s&&c.some(function(p){return p===s});)s=s.nextSibling;var v=i(this.doc,c);u.insertBefore(v,s)}}},before:{value:function(){var c=Array.prototype.slice.call(arguments),u=this.parentNode,s=this.previousSibling;if(u!==null){for(;s&&c.some(function(b){return b===s});)s=s.previousSibling;var v=i(this.doc,c),p=s?s.nextSibling:u.firstChild;u.insertBefore(v,p)}}},remove:{value:function(){this.parentNode!==null&&(this.doc&&(this.doc._preremoveNodeIterators(this),this.rooted&&this.doc.mutateRemove(this)),this._remove(),this.parentNode=null)}},_remove:{value:function(){var c=this.parentNode;c!==null&&(c._childNodes?c._childNodes.splice(this.index,1):c._firstChild===this&&(this._nextSibling===this?c._firstChild=null:c._firstChild=this._nextSibling),f.remove(this),c.modify())}},replaceWith:{value:function(){var c=Array.prototype.slice.call(arguments),u=this.parentNode,s=this.nextSibling;if(u!==null){for(;s&&c.some(function(p){return p===s});)s=s.nextSibling;var v=i(this.doc,c);this.parentNode===u?u.replaceChild(v,this):u.insertBefore(v,s)}}}};O.exports=a}}),Ln=Z({"external/npm/node_modules/domino/lib/NonDocumentTypeChildNode.js"(M,O){var _=xe(),f={nextElementSibling:{get:function(){if(this.parentNode){for(var i=this.nextSibling;i!==null;i=i.nextSibling)if(i.nodeType===_.ELEMENT_NODE)return i}return null}},previousElementSibling:{get:function(){if(this.parentNode){for(var i=this.previousSibling;i!==null;i=i.previousSibling)if(i.nodeType===_.ELEMENT_NODE)return i}return null}}};O.exports=f}}),Cn=Z({"external/npm/node_modules/domino/lib/NamedNodeMap.js"(M,O){O.exports=f;var _=Se();function f(i){this.element=i}Object.defineProperties(f.prototype,{length:{get:_.shouldOverride},item:{value:_.shouldOverride},getNamedItem:{value:function(a){return this.element.getAttributeNode(a)}},getNamedItemNS:{value:function(a,l){return this.element.getAttributeNodeNS(a,l)}},setNamedItem:{value:_.nyi},setNamedItemNS:{value:_.nyi},removeNamedItem:{value:function(a){var l=this.element.getAttributeNode(a);if(l)return this.element.removeAttribute(a),l;_.NotFoundError()}},removeNamedItemNS:{value:function(a,l){var c=this.element.getAttributeNodeNS(a,l);if(c)return this.element.removeAttributeNS(a,l),c;_.NotFoundError()}}})}}),Bt=Z({"external/npm/node_modules/domino/lib/Element.js"(M,O){O.exports=R;var _=qr(),f=Se(),i=f.NAMESPACE,a=Sn(),l=xe(),c=kt(),u=Nn(),s=ai(),v=kn(),p=Rr(),b=Hr(),D=Br(),F=Ln(),A=Cn(),ee=Object.create(null);function R(t,r,o,w){b.call(this),this.nodeType=l.ELEMENT_NODE,this.ownerDocument=t,this.localName=r,this.namespaceURI=o,this.prefix=w,this._tagName=void 0,this._attrsByQName=Object.create(null),this._attrsByLName=Object.create(null),this._attrKeys=[]}function y(t,r){if(t.nodeType===l.TEXT_NODE)r.push(t._data);else for(var o=0,w=t.childNodes.length;o<w;o++)y(t.childNodes[o],r)}R.prototype=Object.create(b.prototype,{isHTML:{get:function(){return this.namespaceURI===i.HTML&&this.ownerDocument.isHTML}},tagName:{get:function(){if(this._tagName===void 0){var r;if(this.prefix===null?r=this.localName:r=this.prefix+":"+this.localName,this.isHTML){var o=ee[r];o||(ee[r]=o=f.toASCIIUpperCase(r)),r=o}this._tagName=r}return this._tagName}},nodeName:{get:function(){return this.tagName}},nodeValue:{get:function(){return null},set:function(){}},textContent:{get:function(){var t=[];return y(this,t),t.join("")},set:function(t){this.removeChildren(),t!=null&&t!==""&&this._appendChild(this.ownerDocument.createTextNode(t))}},innerText:{get:function(){var t=[];return y(this,t),t.join("").replace(/[ \t\n\f\r]+/g," ").trim()},set:function(t){this.removeChildren(),t!=null&&t!==""&&this._appendChild(this.ownerDocument.createTextNode(t))}},innerHTML:{get:function(){return this.serialize()},set:f.nyi},outerHTML:{get:function(){return u.serializeOne(this,{nodeType:0})},set:function(t){var r=this.ownerDocument,o=this.parentNode;if(o!==null){o.nodeType===l.DOCUMENT_NODE&&f.NoModificationAllowedError(),o.nodeType===l.DOCUMENT_FRAGMENT_NODE&&(o=o.ownerDocument.createElement("body"));var w=r.implementation.mozHTMLParser(r._address,o);w.parse(t===null?"":String(t),!0),this.replaceWith(w._asDocumentFragment())}}},_insertAdjacent:{value:function(r,o){var w=!1;switch(r){case"beforebegin":w=!0;case"afterend":var C=this.parentNode;return C===null?null:C.insertBefore(o,w?this:this.nextSibling);case"afterbegin":w=!0;case"beforeend":return this.insertBefore(o,w?this.firstChild:null);default:return f.SyntaxError()}}},insertAdjacentElement:{value:function(r,o){if(o.nodeType!==l.ELEMENT_NODE)throw new TypeError("not an element");return r=f.toASCIILowerCase(String(r)),this._insertAdjacent(r,o)}},insertAdjacentText:{value:function(r,o){var w=this.ownerDocument.createTextNode(o);r=f.toASCIILowerCase(String(r)),this._insertAdjacent(r,w)}},insertAdjacentHTML:{value:function(r,o){r=f.toASCIILowerCase(String(r)),o=String(o);var w;switch(r){case"beforebegin":case"afterend":w=this.parentNode,(w===null||w.nodeType===l.DOCUMENT_NODE)&&f.NoModificationAllowedError();break;case"afterbegin":case"beforeend":w=this;break;default:f.SyntaxError()}(!(w instanceof R)||w.ownerDocument.isHTML&&w.localName==="html"&&w.namespaceURI===i.HTML)&&(w=w.ownerDocument.createElementNS(i.HTML,"body"));var C=this.ownerDocument.implementation.mozHTMLParser(this.ownerDocument._address,w);C.parse(o,!0),this._insertAdjacent(r,C._asDocumentFragment())}},children:{get:function(){return this._children||(this._children=new se(this)),this._children}},attributes:{get:function(){return this._attributes||(this._attributes=new T(this)),this._attributes}},firstElementChild:{get:function(){for(var t=this.firstChild;t!==null;t=t.nextSibling)if(t.nodeType===l.ELEMENT_NODE)return t;return null}},lastElementChild:{get:function(){for(var t=this.lastChild;t!==null;t=t.previousSibling)if(t.nodeType===l.ELEMENT_NODE)return t;return null}},childElementCount:{get:function(){return this.children.length}},nextElement:{value:function(t){t||(t=this.ownerDocument.documentElement);var r=this.firstElementChild;if(!r){if(this===t)return null;r=this.nextElementSibling}if(r)return r;for(var o=this.parentElement;o&&o!==t;o=o.parentElement)if(r=o.nextElementSibling,r)return r;return null}},getElementsByTagName:{value:function(r){var o;return r?(r==="*"?o=function(){return!0}:this.isHTML?o=ke(r):o=le(r),new s(this,o)):new c}},getElementsByTagNameNS:{value:function(r,o){var w;return r==="*"&&o==="*"?w=function(){return!0}:r==="*"?w=le(o):o==="*"?w=j(r):w=k(r,o),new s(this,w)}},getElementsByClassName:{value:function(r){if(r=String(r).trim(),r===""){var o=new c;return o}return r=r.split(/[ \t\r\n\f]+/),new s(this,I(r))}},getElementsByName:{value:function(r){return new s(this,Y(String(r)))}},clone:{value:function(){var r;this.namespaceURI!==i.HTML||this.prefix||!this.ownerDocument.isHTML?r=this.ownerDocument.createElementNS(this.namespaceURI,this.prefix!==null?this.prefix+":"+this.localName:this.localName):r=this.ownerDocument.createElement(this.localName);for(var o=0,w=this._attrKeys.length;o<w;o++){var C=this._attrKeys[o],P=this._attrsByLName[C],K=P.cloneNode();K._setOwnerElement(r),r._attrsByLName[C]=K,r._addQName(K)}return r._attrKeys=this._attrKeys.concat(),r}},isEqual:{value:function(r){if(this.localName!==r.localName||this.namespaceURI!==r.namespaceURI||this.prefix!==r.prefix||this._numattrs!==r._numattrs)return!1;for(var o=0,w=this._numattrs;o<w;o++){var C=this._attr(o);if(!r.hasAttributeNS(C.namespaceURI,C.localName)||r.getAttributeNS(C.namespaceURI,C.localName)!==C.value)return!1}return!0}},_lookupNamespacePrefix:{value:function(r,o){if(this.namespaceURI&&this.namespaceURI===r&&this.prefix!==null&&o.lookupNamespaceURI(this.prefix)===r)return this.prefix;for(var w=0,C=this._numattrs;w<C;w++){var P=this._attr(w);if(P.prefix==="xmlns"&&P.value===r&&o.lookupNamespaceURI(P.localName)===r)return P.localName}var K=this.parentElement;return K?K._lookupNamespacePrefix(r,o):null}},lookupNamespaceURI:{value:function(r){if((r===""||r===void 0)&&(r=null),this.namespaceURI!==null&&this.prefix===r)return this.namespaceURI;for(var o=0,w=this._numattrs;o<w;o++){var C=this._attr(o);if(C.namespaceURI===i.XMLNS&&(C.prefix==="xmlns"&&C.localName===r||r===null&&C.prefix===null&&C.localName==="xmlns"))return C.value||null}var P=this.parentElement;return P?P.lookupNamespaceURI(r):null}},getAttribute:{value:function(r){var o=this.getAttributeNode(r);return o?o.value:null}},getAttributeNS:{value:function(r,o){var w=this.getAttributeNodeNS(r,o);return w?w.value:null}},getAttributeNode:{value:function(r){r=String(r),/[A-Z]/.test(r)&&this.isHTML&&(r=f.toASCIILowerCase(r));var o=this._attrsByQName[r];return o?(Array.isArray(o)&&(o=o[0]),o):null}},getAttributeNodeNS:{value:function(r,o){r=r==null?"":String(r),o=String(o);var w=this._attrsByLName[r+"|"+o];return w||null}},hasAttribute:{value:function(r){return r=String(r),/[A-Z]/.test(r)&&this.isHTML&&(r=f.toASCIILowerCase(r)),this._attrsByQName[r]!==void 0}},hasAttributeNS:{value:function(r,o){r=r==null?"":String(r),o=String(o);var w=r+"|"+o;return this._attrsByLName[w]!==void 0}},hasAttributes:{value:function(){return this._numattrs>0}},toggleAttribute:{value:function(r,o){r=String(r),_.isValidName(r)||f.InvalidCharacterError(),/[A-Z]/.test(r)&&this.isHTML&&(r=f.toASCIILowerCase(r));var w=this._attrsByQName[r];return w===void 0?o===void 0||o===!0?(this._setAttribute(r,""),!0):!1:o===void 0||o===!1?(this.removeAttribute(r),!1):!0}},_setAttribute:{value:function(r,o){var w=this._attrsByQName[r],C;w?Array.isArray(w)&&(w=w[0]):(w=this._newattr(r),C=!0),w.value=o,this._attributes&&(this._attributes[r]=w),C&&this._newattrhook&&this._newattrhook(r,o)}},setAttribute:{value:function(r,o){r=String(r),_.isValidName(r)||f.InvalidCharacterError(),/[A-Z]/.test(r)&&this.isHTML&&(r=f.toASCIILowerCase(r)),this._setAttribute(r,String(o))}},_setAttributeNS:{value:function(r,o,w){var C=o.indexOf(":"),P,K;C<0?(P=null,K=o):(P=o.substring(0,C),K=o.substring(C+1)),(r===""||r===void 0)&&(r=null);var ce=(r===null?"":r)+"|"+K,oe=this._attrsByLName[ce],g;oe||(oe=new m(this,K,P,r),g=!0,this._attrsByLName[ce]=oe,this._attributes&&(this._attributes[this._attrKeys.length]=oe),this._attrKeys.push(ce),this._addQName(oe)),oe.value=w,g&&this._newattrhook&&this._newattrhook(o,w)}},setAttributeNS:{value:function(r,o,w){r=r==null||r===""?null:String(r),o=String(o),_.isValidQName(o)||f.InvalidCharacterError();var C=o.indexOf(":"),P=C<0?null:o.substring(0,C);(P!==null&&r===null||P==="xml"&&r!==i.XML||(o==="xmlns"||P==="xmlns")&&r!==i.XMLNS||r===i.XMLNS&&!(o==="xmlns"||P==="xmlns"))&&f.NamespaceError(),this._setAttributeNS(r,o,String(w))}},setAttributeNode:{value:function(r){r.ownerElement!==null&&r.ownerElement!==this&&f.InUseAttributeError();var o=null,w=this._attrsByQName[r.name];if(w){if(Array.isArray(w)||(w=[w]),w.some(function(C){return C===r}))return r;r.ownerElement!==null&&f.InUseAttributeError(),w.forEach(function(C){this.removeAttributeNode(C)},this),o=w[0]}return this.setAttributeNodeNS(r),o}},setAttributeNodeNS:{value:function(r){r.ownerElement!==null&&f.InUseAttributeError();var o=r.namespaceURI,w=(o===null?"":o)+"|"+r.localName,C=this._attrsByLName[w];return C&&this.removeAttributeNode(C),r._setOwnerElement(this),this._attrsByLName[w]=r,this._attributes&&(this._attributes[this._attrKeys.length]=r),this._attrKeys.push(w),this._addQName(r),this._newattrhook&&this._newattrhook(r.name,r.value),C||null}},removeAttribute:{value:function(r){r=String(r),/[A-Z]/.test(r)&&this.isHTML&&(r=f.toASCIILowerCase(r));var o=this._attrsByQName[r];if(o){Array.isArray(o)?o.length>2?o=o.shift():(this._attrsByQName[r]=o[1],o=o[0]):this._attrsByQName[r]=void 0;var w=o.namespaceURI,C=(w===null?"":w)+"|"+o.localName;this._attrsByLName[C]=void 0;var P=this._attrKeys.indexOf(C);this._attributes&&(Array.prototype.splice.call(this._attributes,P,1),this._attributes[r]=void 0),this._attrKeys.splice(P,1);var K=o.onchange;o._setOwnerElement(null),K&&K.call(o,this,o.localName,o.value,null),this.rooted&&this.ownerDocument.mutateRemoveAttr(o)}}},removeAttributeNS:{value:function(r,o){r=r==null?"":String(r),o=String(o);var w=r+"|"+o,C=this._attrsByLName[w];if(C){this._attrsByLName[w]=void 0;var P=this._attrKeys.indexOf(w);this._attributes&&Array.prototype.splice.call(this._attributes,P,1),this._attrKeys.splice(P,1),this._removeQName(C);var K=C.onchange;C._setOwnerElement(null),K&&K.call(C,this,C.localName,C.value,null),this.rooted&&this.ownerDocument.mutateRemoveAttr(C)}}},removeAttributeNode:{value:function(r){var o=r.namespaceURI,w=(o===null?"":o)+"|"+r.localName;return this._attrsByLName[w]!==r&&f.NotFoundError(),this.removeAttributeNS(o,r.localName),r}},getAttributeNames:{value:function(){var r=this;return this._attrKeys.map(function(o){return r._attrsByLName[o].name})}},_getattr:{value:function(r){var o=this._attrsByQName[r];return o?o.value:null}},_setattr:{value:function(r,o){var w=this._attrsByQName[r],C;w||(w=this._newattr(r),C=!0),w.value=String(o),this._attributes&&(this._attributes[r]=w),C&&this._newattrhook&&this._newattrhook(r,o)}},_newattr:{value:function(r){var o=new m(this,r,null,null),w="|"+r;return this._attrsByQName[r]=o,this._attrsByLName[w]=o,this._attributes&&(this._attributes[this._attrKeys.length]=o),this._attrKeys.push(w),o}},_addQName:{value:function(t){var r=t.name,o=this._attrsByQName[r];o?Array.isArray(o)?o.push(t):this._attrsByQName[r]=[o,t]:this._attrsByQName[r]=t,this._attributes&&(this._attributes[r]=t)}},_removeQName:{value:function(t){var r=t.name,o=this._attrsByQName[r];if(Array.isArray(o)){var w=o.indexOf(t);f.assert(w!==-1),o.length===2?(this._attrsByQName[r]=o[1-w],this._attributes&&(this._attributes[r]=this._attrsByQName[r])):(o.splice(w,1),this._attributes&&this._attributes[r]===t&&(this._attributes[r]=o[0]))}else f.assert(o===t),this._attrsByQName[r]=void 0,this._attributes&&(this._attributes[r]=void 0)}},_numattrs:{get:function(){return this._attrKeys.length}},_attr:{value:function(t){return this._attrsByLName[this._attrKeys[t]]}},id:a.property({name:"id"}),className:a.property({name:"class"}),classList:{get:function(){var t=this;if(this._classList)return this._classList;var r=new v(function(){return t.className||""},function(o){t.className=o});return this._classList=r,r},set:function(t){this.className=t}},matches:{value:function(t){return p.matches(this,t)}},closest:{value:function(t){var r=this;do{if(r.matches&&r.matches(t))return r;r=r.parentElement||r.parentNode}while(r!==null&&r.nodeType===l.ELEMENT_NODE);return null}},querySelector:{value:function(t){return p(t,this)[0]}},querySelectorAll:{value:function(t){var r=p(t,this);return r.item?r:new c(r)}}}),Object.defineProperties(R.prototype,D),Object.defineProperties(R.prototype,F),a.registerChangeHandler(R,"id",function(t,r,o,w){t.rooted&&(o&&t.ownerDocument.delId(o,t),w&&t.ownerDocument.addId(w,t))}),a.registerChangeHandler(R,"class",function(t,r,o,w){t._classList&&t._classList._update()});function m(t,r,o,w,C){this.localName=r,this.prefix=o===null||o===""?null:""+o,this.namespaceURI=w===null||w===""?null:""+w,this.data=C,this._setOwnerElement(t)}m.prototype=Object.create(Object.prototype,{ownerElement:{get:function(){return this._ownerElement}},_setOwnerElement:{value:function(r){this._ownerElement=r,this.prefix===null&&this.namespaceURI===null&&r?this.onchange=r._attributeChangeHandlers[this.localName]:this.onchange=null}},name:{get:function(){return this.prefix?this.prefix+":"+this.localName:this.localName}},specified:{get:function(){return!0}},value:{get:function(){return this.data},set:function(t){var r=this.data;t=t===void 0?"":t+"",t!==r&&(this.data=t,this.ownerElement&&(this.onchange&&this.onchange(this.ownerElement,this.localName,r,t),this.ownerElement.rooted&&this.ownerElement.ownerDocument.mutateAttr(this,r)))}},cloneNode:{value:function(r){return new m(null,this.localName,this.prefix,this.namespaceURI,this.data)}},nodeType:{get:function(){return l.ATTRIBUTE_NODE}},nodeName:{get:function(){return this.name}},nodeValue:{get:function(){return this.value},set:function(t){this.value=t}},textContent:{get:function(){return this.value},set:function(t){t==null&&(t=""),this.value=t}},innerText:{get:function(){return this.value},set:function(t){t==null&&(t=""),this.value=t}}}),R._Attr=m;function T(t){A.call(this,t);for(var r in t._attrsByQName)this[r]=t._attrsByQName[r];for(var o=0;o<t._attrKeys.length;o++)this[o]=t._attrsByLName[t._attrKeys[o]]}T.prototype=Object.create(A.prototype,{length:{get:function(){return this.element._attrKeys.length},set:function(){}},item:{value:function(t){return t=t>>>0,t>=this.length?null:this.element._attrsByLName[this.element._attrKeys[t]]}}});var h;(h=globalThis.Symbol)!=null&&h.iterator&&(T.prototype[globalThis.Symbol.iterator]=function(){var t=0,r=this.length,o=this;return{next:function(){return t<r?{value:o.item(t++)}:{done:!0}}}});function se(t){this.element=t,this.updateCache()}se.prototype=Object.create(Object.prototype,{length:{get:function(){return this.updateCache(),this.childrenByNumber.length}},item:{value:function(r){return this.updateCache(),this.childrenByNumber[r]||null}},namedItem:{value:function(r){return this.updateCache(),this.childrenByName[r]||null}},namedItems:{get:function(){return this.updateCache(),this.childrenByName}},updateCache:{value:function(){var r=/^(a|applet|area|embed|form|frame|frameset|iframe|img|object)$/;if(this.lastModTime!==this.element.lastModTime){this.lastModTime=this.element.lastModTime;for(var o=this.childrenByNumber&&this.childrenByNumber.length||0,w=0;w<o;w++)this[w]=void 0;this.childrenByNumber=[],this.childrenByName=Object.create(null);for(var C=this.element.firstChild;C!==null;C=C.nextSibling)if(C.nodeType===l.ELEMENT_NODE){this[this.childrenByNumber.length]=C,this.childrenByNumber.push(C);var P=C.getAttribute("id");P&&!this.childrenByName[P]&&(this.childrenByName[P]=C);var K=C.getAttribute("name");K&&this.element.namespaceURI===i.HTML&&r.test(this.element.localName)&&!this.childrenByName[K]&&(this.childrenByName[P]=C)}}}}});function le(t){return function(r){return r.localName===t}}function ke(t){var r=f.toASCIILowerCase(t);return r===t?le(t):function(o){return o.isHTML?o.localName===r:o.localName===t}}function j(t){return function(r){return r.namespaceURI===t}}function k(t,r){return function(o){return o.namespaceURI===t&&o.localName===r}}function I(t){return function(r){return t.every(function(o){return r.classList.contains(o)})}}function Y(t){return function(r){return r.namespaceURI!==i.HTML?!1:r.getAttribute("name")===t}}}}),xn=Z({"external/npm/node_modules/domino/lib/Leaf.js"(M,O){O.exports=c;var _=xe(),f=kt(),i=Se(),a=i.HierarchyRequestError,l=i.NotFoundError;function c(){_.call(this)}c.prototype=Object.create(_.prototype,{hasChildNodes:{value:function(){return!1}},firstChild:{value:null},lastChild:{value:null},insertBefore:{value:function(u,s){if(!u.nodeType)throw new TypeError("not a node");a()}},replaceChild:{value:function(u,s){if(!u.nodeType)throw new TypeError("not a node");a()}},removeChild:{value:function(u){if(!u.nodeType)throw new TypeError("not a node");l()}},removeChildren:{value:function(){}},childNodes:{get:function(){return this._childNodes||(this._childNodes=new f),this._childNodes}}})}}),fr=Z({"external/npm/node_modules/domino/lib/CharacterData.js"(M,O){O.exports=l;var _=xn(),f=Se(),i=Br(),a=Ln();function l(){_.call(this)}l.prototype=Object.create(_.prototype,{substringData:{value:function(u,s){if(arguments.length<2)throw new TypeError("Not enough arguments");return u=u>>>0,s=s>>>0,(u>this.data.length||u<0||s<0)&&f.IndexSizeError(),this.data.substring(u,u+s)}},appendData:{value:function(u){if(arguments.length<1)throw new TypeError("Not enough arguments");this.data+=String(u)}},insertData:{value:function(u,s){return this.replaceData(u,0,s)}},deleteData:{value:function(u,s){return this.replaceData(u,s,"")}},replaceData:{value:function(u,s,v){var p=this.data,b=p.length;u=u>>>0,s=s>>>0,v=String(v),(u>b||u<0)&&f.IndexSizeError(),u+s>b&&(s=b-u);var D=p.substring(0,u),F=p.substring(u+s);this.data=D+v+F}},isEqual:{value:function(u){return this._data===u._data}},length:{get:function(){return this.data.length}}}),Object.defineProperties(l.prototype,i),Object.defineProperties(l.prototype,a)}}),Dn=Z({"external/npm/node_modules/domino/lib/Text.js"(M,O){O.exports=a;var _=Se(),f=xe(),i=fr();function a(c,u){i.call(this),this.nodeType=f.TEXT_NODE,this.ownerDocument=c,this._data=u,this._index=void 0}var l={get:function(){return this._data},set:function(c){c==null?c="":c=String(c),c!==this._data&&(this._data=c,this.rooted&&this.ownerDocument.mutateValue(this),this.parentNode&&this.parentNode._textchangehook&&this.parentNode._textchangehook(this))}};a.prototype=Object.create(i.prototype,{nodeName:{value:"#text"},nodeValue:l,textContent:l,innerText:l,data:{get:l.get,set:function(c){l.set.call(this,c===null?"":String(c))}},splitText:{value:function(u){(u>this._data.length||u<0)&&_.IndexSizeError();var s=this._data.substring(u),v=this.ownerDocument.createTextNode(s);this.data=this.data.substring(0,u);var p=this.parentNode;return p!==null&&p.insertBefore(v,this.nextSibling),v}},wholeText:{get:function(){for(var u=this.textContent,s=this.nextSibling;s&&s.nodeType===f.TEXT_NODE;s=s.nextSibling)u+=s.textContent;return u}},replaceWholeText:{value:_.nyi},clone:{value:function(){return new a(this.ownerDocument,this._data)}}})}}),Mn=Z({"external/npm/node_modules/domino/lib/Comment.js"(M,O){O.exports=i;var _=xe(),f=fr();function i(l,c){f.call(this),this.nodeType=_.COMMENT_NODE,this.ownerDocument=l,this._data=c}var a={get:function(){return this._data},set:function(l){l==null?l="":l=String(l),this._data=l,this.rooted&&this.ownerDocument.mutateValue(this)}};i.prototype=Object.create(f.prototype,{nodeName:{value:"#comment"},nodeValue:a,textContent:a,innerText:a,data:{get:a.get,set:function(l){a.set.call(this,l===null?"":String(l))}},clone:{value:function(){return new i(this.ownerDocument,this._data)}}})}}),An=Z({"external/npm/node_modules/domino/lib/DocumentFragment.js"(M,O){O.exports=u;var _=xe(),f=kt(),i=Hr(),a=Bt(),l=Rr(),c=Se();function u(s){i.call(this),this.nodeType=_.DOCUMENT_FRAGMENT_NODE,this.ownerDocument=s}u.prototype=Object.create(i.prototype,{nodeName:{value:"#document-fragment"},nodeValue:{get:function(){return null},set:function(){}},textContent:Object.getOwnPropertyDescriptor(a.prototype,"textContent"),innerText:Object.getOwnPropertyDescriptor(a.prototype,"innerText"),querySelector:{value:function(s){var v=this.querySelectorAll(s);return v.length?v[0]:null}},querySelectorAll:{value:function(s){var v=Object.create(this);v.isHTML=!0,v.getElementsByTagName=a.prototype.getElementsByTagName,v.nextElement=Object.getOwnPropertyDescriptor(a.prototype,"firstElementChild").get;var p=l(s,v);return p.item?p:new f(p)}},clone:{value:function(){return new u(this.ownerDocument)}},isEqual:{value:function(v){return!0}},innerHTML:{get:function(){return this.serialize()},set:c.nyi},outerHTML:{get:function(){return this.serialize()},set:c.nyi}})}}),On=Z({"external/npm/node_modules/domino/lib/ProcessingInstruction.js"(M,O){O.exports=i;var _=xe(),f=fr();function i(l,c,u){f.call(this),this.nodeType=_.PROCESSING_INSTRUCTION_NODE,this.ownerDocument=l,this.target=c,this._data=u}var a={get:function(){return this._data},set:function(l){l==null?l="":l=String(l),this._data=l,this.rooted&&this.ownerDocument.mutateValue(this)}};i.prototype=Object.create(f.prototype,{nodeName:{get:function(){return this.target}},nodeValue:a,textContent:a,innerText:a,data:{get:a.get,set:function(l){a.set.call(this,l===null?"":String(l))}},clone:{value:function(){return new i(this.ownerDocument,this.target,this._data)}},isEqual:{value:function(c){return this.target===c.target&&this._data===c._data}}})}}),hr=Z({"external/npm/node_modules/domino/lib/NodeFilter.js"(M,O){var _={FILTER_ACCEPT:1,FILTER_REJECT:2,FILTER_SKIP:3,SHOW_ALL:4294967295,SHOW_ELEMENT:1,SHOW_ATTRIBUTE:2,SHOW_TEXT:4,SHOW_CDATA_SECTION:8,SHOW_ENTITY_REFERENCE:16,SHOW_ENTITY:32,SHOW_PROCESSING_INSTRUCTION:64,SHOW_COMMENT:128,SHOW_DOCUMENT:256,SHOW_DOCUMENT_TYPE:512,SHOW_DOCUMENT_FRAGMENT:1024,SHOW_NOTATION:2048};O.exports=_.constructor=_.prototype=_}}),In=Z({"external/npm/node_modules/domino/lib/NodeTraversal.js"(M,O){O.exports={nextSkippingChildren:_,nextAncestorSibling:f,next:i,previous:l,deepLastChild:a};function _(c,u){return c===u?null:c.nextSibling!==null?c.nextSibling:f(c,u)}function f(c,u){for(c=c.parentNode;c!==null;c=c.parentNode){if(c===u)return null;if(c.nextSibling!==null)return c.nextSibling}return null}function i(c,u){var s;return s=c.firstChild,s!==null?s:c===u?null:(s=c.nextSibling,s!==null?s:f(c,u))}function a(c){for(;c.lastChild;)c=c.lastChild;return c}function l(c,u){var s;return s=c.previousSibling,s!==null?a(s):(s=c.parentNode,s===u?null:s)}}}),ii=Z({"external/npm/node_modules/domino/lib/TreeWalker.js"(M,O){O.exports=v;var _=xe(),f=hr(),i=In(),a=Se(),l={first:"firstChild",last:"lastChild",next:"firstChild",previous:"lastChild"},c={first:"nextSibling",last:"previousSibling",next:"nextSibling",previous:"previousSibling"};function u(p,b){var D,F,A,ee,R;for(F=p._currentNode[l[b]];F!==null;){if(ee=p._internalFilter(F),ee===f.FILTER_ACCEPT)return p._currentNode=F,F;if(ee===f.FILTER_SKIP&&(D=F[l[b]],D!==null)){F=D;continue}for(;F!==null;){if(R=F[c[b]],R!==null){F=R;break}if(A=F.parentNode,A===null||A===p.root||A===p._currentNode)return null;F=A}}return null}function s(p,b){var D,F,A;if(D=p._currentNode,D===p.root)return null;for(;;){for(A=D[c[b]];A!==null;){if(D=A,F=p._internalFilter(D),F===f.FILTER_ACCEPT)return p._currentNode=D,D;A=D[l[b]],(F===f.FILTER_REJECT||A===null)&&(A=D[c[b]])}if(D=D.parentNode,D===null||D===p.root||p._internalFilter(D)===f.FILTER_ACCEPT)return null}}function v(p,b,D){(!p||!p.nodeType)&&a.NotSupportedError(),this._root=p,this._whatToShow=Number(b)||0,this._filter=D||null,this._active=!1,this._currentNode=p}Object.defineProperties(v.prototype,{root:{get:function(){return this._root}},whatToShow:{get:function(){return this._whatToShow}},filter:{get:function(){return this._filter}},currentNode:{get:function(){return this._currentNode},set:function(b){if(!(b instanceof _))throw new TypeError("Not a Node");this._currentNode=b}},_internalFilter:{value:function(b){var D,F;if(this._active&&a.InvalidStateError(),!(1<<b.nodeType-1&this._whatToShow))return f.FILTER_SKIP;if(F=this._filter,F===null)D=f.FILTER_ACCEPT;else{this._active=!0;try{typeof F=="function"?D=F(b):D=F.acceptNode(b)}finally{this._active=!1}}return+D}},parentNode:{value:function(){for(var b=this._currentNode;b!==this.root;){if(b=b.parentNode,b===null)return null;if(this._internalFilter(b)===f.FILTER_ACCEPT)return this._currentNode=b,b}return null}},firstChild:{value:function(){return u(this,"first")}},lastChild:{value:function(){return u(this,"last")}},previousSibling:{value:function(){return s(this,"previous")}},nextSibling:{value:function(){return s(this,"next")}},previousNode:{value:function(){var b,D,F,A;for(b=this._currentNode;b!==this._root;){for(F=b.previousSibling;F;F=b.previousSibling)if(b=F,D=this._internalFilter(b),D!==f.FILTER_REJECT){for(A=b.lastChild;A&&(b=A,D=this._internalFilter(b),D!==f.FILTER_REJECT);A=b.lastChild);if(D===f.FILTER_ACCEPT)return this._currentNode=b,b}if(b===this.root||b.parentNode===null)return null;if(b=b.parentNode,this._internalFilter(b)===f.FILTER_ACCEPT)return this._currentNode=b,b}return null}},nextNode:{value:function(){var b,D,F,A;b=this._currentNode,D=f.FILTER_ACCEPT;e:for(;;){for(F=b.firstChild;F;F=b.firstChild){if(b=F,D=this._internalFilter(b),D===f.FILTER_ACCEPT)return this._currentNode=b,b;if(D===f.FILTER_REJECT)break}for(A=i.nextSkippingChildren(b,this.root);A;A=i.nextSkippingChildren(b,this.root)){if(b=A,D=this._internalFilter(b),D===f.FILTER_ACCEPT)return this._currentNode=b,b;if(D===f.FILTER_SKIP)continue e}return null}}},toString:{value:function(){return"[object TreeWalker]"}}})}}),si=Z({"external/npm/node_modules/domino/lib/NodeIterator.js"(M,O){O.exports=u;var _=hr(),f=In(),i=Se();function a(s,v,p){return p?f.next(s,v):s===v?null:f.previous(s,null)}function l(s,v){for(;v;v=v.parentNode)if(s===v)return!0;return!1}function c(s,v){var p,b;for(p=s._referenceNode,b=s._pointerBeforeReferenceNode;;){if(b===v)b=!b;else if(p=a(p,s._root,v),p===null)return null;var D=s._internalFilter(p);if(D===_.FILTER_ACCEPT)break}return s._referenceNode=p,s._pointerBeforeReferenceNode=b,p}function u(s,v,p){(!s||!s.nodeType)&&i.NotSupportedError(),this._root=s,this._referenceNode=s,this._pointerBeforeReferenceNode=!0,this._whatToShow=Number(v)||0,this._filter=p||null,this._active=!1,s.doc._attachNodeIterator(this)}Object.defineProperties(u.prototype,{root:{get:function(){return this._root}},referenceNode:{get:function(){return this._referenceNode}},pointerBeforeReferenceNode:{get:function(){return this._pointerBeforeReferenceNode}},whatToShow:{get:function(){return this._whatToShow}},filter:{get:function(){return this._filter}},_internalFilter:{value:function(v){var p,b;if(this._active&&i.InvalidStateError(),!(1<<v.nodeType-1&this._whatToShow))return _.FILTER_SKIP;if(b=this._filter,b===null)p=_.FILTER_ACCEPT;else{this._active=!0;try{typeof b=="function"?p=b(v):p=b.acceptNode(v)}finally{this._active=!1}}return+p}},_preremove:{value:function(v){if(!l(v,this._root)&&l(v,this._referenceNode)){if(this._pointerBeforeReferenceNode){for(var p=v;p.lastChild;)p=p.lastChild;if(p=f.next(p,this.root),p){this._referenceNode=p;return}this._pointerBeforeReferenceNode=!1}if(v.previousSibling===null)this._referenceNode=v.parentNode;else{this._referenceNode=v.previousSibling;var b;for(b=this._referenceNode.lastChild;b;b=this._referenceNode.lastChild)this._referenceNode=b}}}},nextNode:{value:function(){return c(this,!0)}},previousNode:{value:function(){return c(this,!1)}},detach:{value:function(){}},toString:{value:function(){return"[object NodeIterator]"}}})}}),Fr=Z({"external/npm/node_modules/domino/lib/URL.js"(M,O){O.exports=_;function _(f){if(!f)return Object.create(_.prototype);this.url=f.replace(/^[ \t\n\r\f]+|[ \t\n\r\f]+$/g,"");var i=_.pattern.exec(this.url);if(i){if(i[2]&&(this.scheme=i[2]),i[4]){var a=i[4].match(_.userinfoPattern);if(a&&(this.username=a[1],this.password=a[3],i[4]=i[4].substring(a[0].length)),i[4].match(_.portPattern)){var l=i[4].lastIndexOf(":");this.host=i[4].substring(0,l),this.port=i[4].substring(l+1)}else this.host=i[4]}i[5]&&(this.path=i[5]),i[6]&&(this.query=i[7]),i[8]&&(this.fragment=i[9])}}_.pattern=/^(([^:\/?#]+):)?(\/\/([^\/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/,_.userinfoPattern=/^([^@:]*)(:([^@]*))?@/,_.portPattern=/:\d+$/,_.authorityPattern=/^[^:\/?#]+:\/\//,_.hierarchyPattern=/^[^:\/?#]+:\//,_.percentEncode=function(i){var a=i.charCodeAt(0);if(a<256)return"%"+a.toString(16);throw Error("can't percent-encode codepoints > 255 yet")},_.prototype={constructor:_,isAbsolute:function(){return!!this.scheme},isAuthorityBased:function(){return _.authorityPattern.test(this.url)},isHierarchical:function(){return _.hierarchyPattern.test(this.url)},toString:function(){var f="";return this.scheme!==void 0&&(f+=this.scheme+":"),this.isAbsolute()&&(f+="//",(this.username||this.password)&&(f+=this.username||"",this.password&&(f+=":"+this.password),f+="@"),this.host&&(f+=this.host)),this.port!==void 0&&(f+=":"+this.port),this.path!==void 0&&(f+=this.path),this.query!==void 0&&(f+="?"+this.query),this.fragment!==void 0&&(f+="#"+this.fragment),f},resolve:function(f){var i=this,a=new _(f),l=new _;return a.scheme!==void 0?(l.scheme=a.scheme,l.username=a.username,l.password=a.password,l.host=a.host,l.port=a.port,l.path=u(a.path),l.query=a.query):(l.scheme=i.scheme,a.host!==void 0?(l.username=a.username,l.password=a.password,l.host=a.host,l.port=a.port,l.path=u(a.path),l.query=a.query):(l.username=i.username,l.password=i.password,l.host=i.host,l.port=i.port,a.path?(a.path.charAt(0)==="/"?l.path=u(a.path):(l.path=c(i.path,a.path),l.path=u(l.path)),l.query=a.query):(l.path=i.path,a.query!==void 0?l.query=a.query:l.query=i.query))),l.fragment=a.fragment,l.toString();function c(s,v){if(i.host!==void 0&&!i.path)return"/"+v;var p=s.lastIndexOf("/");return p===-1?v:s.substring(0,p+1)+v}function u(s){if(!s)return s;for(var v="";s.length>0;){if(s==="."||s===".."){s="";break}var p=s.substring(0,2),b=s.substring(0,3),D=s.substring(0,4);if(b==="../")s=s.substring(3);else if(p==="./")s=s.substring(2);else if(b==="/./")s="/"+s.substring(3);else if(p==="/."&&s.length===2)s="/";else if(D==="/../"||b==="/.."&&s.length===3)s="/"+s.substring(4),v=v.replace(/\/?[^\/]*$/,"");else{var F=s.match(/(\/?([^\/]*))/)[0];v+=F,s=s.substring(F.length)}}return v}}}}}),oi=Z({"external/npm/node_modules/domino/lib/CustomEvent.js"(M,O){O.exports=f;var _=Rt();function f(i,a){_.call(this,i,a)}f.prototype=Object.create(_.prototype,{constructor:{value:f}})}}),Hn=Z({"external/npm/node_modules/domino/lib/events.js"(M,O){O.exports={Event:Rt(),UIEvent:En(),MouseEvent:yn(),CustomEvent:oi()}}}),ci=Z({"external/npm/node_modules/domino/lib/style_parser.js"(M){Object.defineProperty(M,"__esModule",{value:!0}),M.hyphenate=M.parse=void 0;function O(f){let i=[],a=0,l=0,c=0,u=0,s=0,v=null;for(;a<f.length;)switch(f.charCodeAt(a++)){case 40:l++;break;case 41:l--;break;case 39:c===0?c=39:c===39&&f.charCodeAt(a-1)!==92&&(c=0);break;case 34:c===0?c=34:c===34&&f.charCodeAt(a-1)!==92&&(c=0);break;case 58:!v&&l===0&&c===0&&(v=_(f.substring(s,a-1).trim()),u=a);break;case 59:if(v&&u>0&&l===0&&c===0){let b=f.substring(u,a-1).trim();i.push(v,b),s=a,u=0,v=null}break}if(v&&u){let p=f.slice(u).trim();i.push(v,p)}return i}M.parse=O;function _(f){return f.replace(/[a-z][A-Z]/g,i=>i.charAt(0)+"-"+i.charAt(1)).toLowerCase()}M.hyphenate=_}}),Pr=Z({"external/npm/node_modules/domino/lib/CSSStyleDeclaration.js"(M,O){var{parse:_}=ci();O.exports=function(u){let s=new i(u),v={get:function(p,b){return b in p?p[b]:p.getPropertyValue(f(b))},has:function(p,b){return!0},set:function(p,b,D){return b in p?p[b]=D:p.setProperty(f(b),D??void 0),!0}};return new Proxy(s,v)};function f(u){return u.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}function i(u){this._element=u}var a="!important";function l(u){let s={property:{},priority:{}};if(!u)return s;let v=_(u);if(v.length<2)return s;for(let p=0;p<v.length;p+=2){let b=v[p],D=v[p+1];D.endsWith(a)&&(s.priority[b]="important",D=D.slice(0,-a.length).trim()),s.property[b]=D}return s}var c={};i.prototype=Object.create(Object.prototype,{_parsed:{get:function(){if(!this._parsedStyles||this.cssText!==this._lastParsedText){var u=this.cssText;this._parsedStyles=l(u),this._lastParsedText=u,delete this._names}return this._parsedStyles}},_serialize:{value:function(){var u=this._parsed,s="";for(var v in u.property)s&&(s+=" "),s+=v+": "+u.property[v],u.priority[v]&&(s+=" !"+u.priority[v]),s+=";";this.cssText=s,this._lastParsedText=s,delete this._names}},cssText:{get:function(){return this._element.getAttribute("style")},set:function(u){this._element.setAttribute("style",u)}},length:{get:function(){return this._names||(this._names=Object.getOwnPropertyNames(this._parsed.property)),this._names.length}},item:{value:function(u){return this._names||(this._names=Object.getOwnPropertyNames(this._parsed.property)),this._names[u]}},getPropertyValue:{value:function(u){return u=u.toLowerCase(),this._parsed.property[u]||""}},getPropertyPriority:{value:function(u){return u=u.toLowerCase(),this._parsed.priority[u]||""}},setProperty:{value:function(u,s,v){if(u=u.toLowerCase(),s==null&&(s=""),v==null&&(v=""),s!==c&&(s=""+s),s=s.trim(),s===""){this.removeProperty(u);return}if(!(v!==""&&v!==c&&!/^important$/i.test(v))){var p=this._parsed;if(s===c){if(!p.property[u])return;v!==""?p.priority[u]="important":delete p.priority[u]}else{if(s.includes(";")&&!s.includes("data:"))return;var b=l(u+":"+s);if(Object.getOwnPropertyNames(b.property).length===0||Object.getOwnPropertyNames(b.priority).length!==0)return;for(var D in b.property)p.property[D]=b.property[D],v!==c&&(v!==""?p.priority[D]="important":p.priority[D]&&delete p.priority[D])}this._serialize()}}},setPropertyValue:{value:function(u,s){return this.setProperty(u,s,c)}},setPropertyPriority:{value:function(u,s){return this.setProperty(u,c,s)}},removeProperty:{value:function(u){u=u.toLowerCase();var s=this._parsed;u in s.property&&(delete s.property[u],delete s.priority[u],this._serialize())}}})}}),qn=Z({"external/npm/node_modules/domino/lib/URLUtils.js"(M,O){var _=Fr();O.exports=f;function f(){}f.prototype=Object.create(Object.prototype,{_url:{get:function(){return new _(this.href)}},protocol:{get:function(){var i=this._url;return i&&i.scheme?i.scheme+":":":"},set:function(i){var a=this.href,l=new _(a);l.isAbsolute()&&(i=i.replace(/:+$/,""),i=i.replace(/[^-+\.a-zA-Z0-9]/g,_.percentEncode),i.length>0&&(l.scheme=i,a=l.toString())),this.href=a}},host:{get:function(){var i=this._url;return i.isAbsolute()&&i.isAuthorityBased()?i.host+(i.port?":"+i.port:""):""},set:function(i){var a=this.href,l=new _(a);l.isAbsolute()&&l.isAuthorityBased()&&(i=i.replace(/[^-+\._~!$&'()*,;:=a-zA-Z0-9]/g,_.percentEncode),i.length>0&&(l.host=i,delete l.port,a=l.toString())),this.href=a}},hostname:{get:function(){var i=this._url;return i.isAbsolute()&&i.isAuthorityBased()?i.host:""},set:function(i){var a=this.href,l=new _(a);l.isAbsolute()&&l.isAuthorityBased()&&(i=i.replace(/^\/+/,""),i=i.replace(/[^-+\._~!$&'()*,;:=a-zA-Z0-9]/g,_.percentEncode),i.length>0&&(l.host=i,a=l.toString())),this.href=a}},port:{get:function(){var i=this._url;return i.isAbsolute()&&i.isAuthorityBased()&&i.port!==void 0?i.port:""},set:function(i){var a=this.href,l=new _(a);l.isAbsolute()&&l.isAuthorityBased()&&(i=""+i,i=i.replace(/[^0-9].*$/,""),i=i.replace(/^0+/,""),i.length===0&&(i="0"),parseInt(i,10)<=65535&&(l.port=i,a=l.toString())),this.href=a}},pathname:{get:function(){var i=this._url;return i.isAbsolute()&&i.isHierarchical()?i.path:""},set:function(i){var a=this.href,l=new _(a);l.isAbsolute()&&l.isHierarchical()&&(i.charAt(0)!=="/"&&(i="/"+i),i=i.replace(/[^-+\._~!$&'()*,;:=@\/a-zA-Z0-9]/g,_.percentEncode),l.path=i,a=l.toString()),this.href=a}},search:{get:function(){var i=this._url;return i.isAbsolute()&&i.isHierarchical()&&i.query!==void 0?"?"+i.query:""},set:function(i){var a=this.href,l=new _(a);l.isAbsolute()&&l.isHierarchical()&&(i.charAt(0)==="?"&&(i=i.substring(1)),i=i.replace(/[^-+\._~!$&'()*,;:=@\/?a-zA-Z0-9]/g,_.percentEncode),l.query=i,a=l.toString()),this.href=a}},hash:{get:function(){var i=this._url;return i==null||i.fragment==null||i.fragment===""?"":"#"+i.fragment},set:function(i){var a=this.href,l=new _(a);i.charAt(0)==="#"&&(i=i.substring(1)),i=i.replace(/[^-+\._~!$&'()*,;:=@\/?a-zA-Z0-9]/g,_.percentEncode),l.fragment=i,a=l.toString(),this.href=a}},username:{get:function(){var i=this._url;return i.username||""},set:function(i){var a=this.href,l=new _(a);l.isAbsolute()&&(i=i.replace(/[\x00-\x1F\x7F-\uFFFF "#<>?`\/@\\:]/g,_.percentEncode),l.username=i,a=l.toString()),this.href=a}},password:{get:function(){var i=this._url;return i.password||""},set:function(i){var a=this.href,l=new _(a);l.isAbsolute()&&(i===""?l.password=null:(i=i.replace(/[\x00-\x1F\x7F-\uFFFF "#<>?`\/@\\]/g,_.percentEncode),l.password=i),a=l.toString()),this.href=a}},origin:{get:function(){var i=this._url;if(i==null)return"";var a=function(l){var c=[i.scheme,i.host,+i.port||l];return c[0]+"://"+c[1]+(c[2]===l?"":":"+c[2])};switch(i.scheme){case"ftp":return a(21);case"gopher":return a(70);case"http":case"ws":return a(80);case"https":case"wss":return a(443);default:return i.scheme+"://"}}}}),f._inherit=function(i){Object.getOwnPropertyNames(f.prototype).forEach(function(a){if(!(a==="constructor"||a==="href")){var l=Object.getOwnPropertyDescriptor(f.prototype,a);Object.defineProperty(i,a,l)}})}}}),Rn=Z({"external/npm/node_modules/domino/lib/defineElement.js"(M,O){var _=Sn(),f=Ir().isApiWritable;O.exports=function(c,u,s,v){var p=c.ctor;if(p){var b=c.props||{};if(c.attributes)for(var D in c.attributes){var F=c.attributes[D];(typeof F!="object"||Array.isArray(F))&&(F={type:F}),F.name||(F.name=D.toLowerCase()),b[D]=_.property(F)}b.constructor={value:p,writable:f},p.prototype=Object.create((c.superclass||u).prototype,b),c.events&&l(p,c.events),s[c.name]=p}else p=u;return(c.tags||c.tag&&[c.tag]||[]).forEach(function(A){v[A]=p}),p};function i(c,u,s,v){this.body=c,this.document=u,this.form=s,this.element=v}i.prototype.build=function(){return()=>{}};function a(c,u,s,v){var p=c.ownerDocument||Object.create(null),b=c.form||Object.create(null);c[u]=new i(v,p,b,c).build()}function l(c,u){var s=c.prototype;u.forEach(function(v){Object.defineProperty(s,"on"+v,{get:function(){return this._getEventHandler(v)},set:function(p){this._setEventHandler(v,p)}}),_.registerChangeHandler(c,"on"+v,a)})}}}),Ur=Z({"external/npm/node_modules/domino/lib/htmlelts.js"(M){var O=xe(),_=Bt(),f=Pr(),i=Se(),a=qn(),l=Rn(),c=M.elements={},u=Object.create(null);M.createElement=function(y,m,T){var h=u[m]||ee;return new h(y,m,T)};function s(y){return l(y,A,c,u)}function v(y){return{get:function(){var m=this._getattr(y);if(m===null)return"";var T=this.doc._resolve(m);return T===null?m:T},set:function(m){this._setattr(y,m)}}}function p(y){return{get:function(){var m=this._getattr(y);return m===null?null:m.toLowerCase()==="use-credentials"?"use-credentials":"anonymous"},set:function(m){m==null?this.removeAttribute(y):this._setattr(y,m)}}}var b={type:["","no-referrer","no-referrer-when-downgrade","same-origin","origin","strict-origin","origin-when-cross-origin","strict-origin-when-cross-origin","unsafe-url"],missing:""},D={A:!0,LINK:!0,BUTTON:!0,INPUT:!0,SELECT:!0,TEXTAREA:!0,COMMAND:!0},F=function(y,m,T){A.call(this,y,m,T),this._form=null},A=M.HTMLElement=s({superclass:_,name:"HTMLElement",ctor:function(m,T,h){_.call(this,m,T,i.NAMESPACE.HTML,h)},props:{dangerouslySetInnerHTML:{set:function(y){this._innerHTML=y}},innerHTML:{get:function(){return this.serialize()},set:function(y){var m=this.ownerDocument.implementation.mozHTMLParser(this.ownerDocument._address,this);m.parse(y===null?"":String(y),!0);for(var T=this instanceof u.template?this.content:this;T.hasChildNodes();)T.removeChild(T.firstChild);T.appendChild(m._asDocumentFragment())}},style:{get:function(){return this._style||(this._style=new f(this)),this._style},set:function(y){y==null&&(y=""),this._setattr("style",String(y))}},blur:{value:function(){}},focus:{value:function(){}},forceSpellCheck:{value:function(){}},click:{value:function(){if(!this._click_in_progress){this._click_in_progress=!0;try{this._pre_click_activation_steps&&this._pre_click_activation_steps();var y=this.ownerDocument.createEvent("MouseEvent");y.initMouseEvent("click",!0,!0,this.ownerDocument.defaultView,1,0,0,0,0,!1,!1,!1,!1,0,null);var m=this.dispatchEvent(y);m?this._post_click_activation_steps&&this._post_click_activation_steps(y):this._cancelled_activation_steps&&this._cancelled_activation_steps()}finally{this._click_in_progress=!1}}}},submit:{value:i.nyi}},attributes:{title:String,lang:String,dir:{type:["ltr","rtl","auto"],missing:""},draggable:{type:["true","false"],treatNullAsEmptyString:!0},spellcheck:{type:["true","false"],missing:""},enterKeyHint:{type:["enter","done","go","next","previous","search","send"],missing:""},autoCapitalize:{type:["off","on","none","sentences","words","characters"],missing:""},autoFocus:Boolean,accessKey:String,nonce:String,hidden:Boolean,translate:{type:["no","yes"],missing:""},tabIndex:{type:"long",default:function(){return this.tagName in D||this.contentEditable?0:-1}}},events:["abort","canplay","canplaythrough","change","click","contextmenu","cuechange","dblclick","drag","dragend","dragenter","dragleave","dragover","dragstart","drop","durationchange","emptied","ended","input","invalid","keydown","keypress","keyup","loadeddata","loadedmetadata","loadstart","mousedown","mousemove","mouseout","mouseover","mouseup","mousewheel","pause","play","playing","progress","ratechange","readystatechange","reset","seeked","seeking","select","show","stalled","submit","suspend","timeupdate","volumechange","waiting","blur","error","focus","load","scroll"]}),ee=s({name:"HTMLUnknownElement",ctor:function(m,T,h){A.call(this,m,T,h)}}),R={form:{get:function(){return this._form}}};s({tag:"a",name:"HTMLAnchorElement",ctor:function(m,T,h){A.call(this,m,T,h)},props:{_post_click_activation_steps:{value:function(y){this.href&&(this.ownerDocument.defaultView.location=this.href)}}},attributes:{href:v,ping:String,download:String,target:String,rel:String,media:String,hreflang:String,type:String,referrerPolicy:b,coords:String,charset:String,name:String,rev:String,shape:String}}),a._inherit(u.a.prototype),s({tag:"area",name:"HTMLAreaElement",ctor:function(m,T,h){A.call(this,m,T,h)},attributes:{alt:String,target:String,download:String,rel:String,media:String,href:v,hreflang:String,type:String,shape:String,coords:String,ping:String,referrerPolicy:b,noHref:Boolean}}),a._inherit(u.area.prototype),s({tag:"br",name:"HTMLBRElement",ctor:function(m,T,h){A.call(this,m,T,h)},attributes:{clear:String}}),s({tag:"base",name:"HTMLBaseElement",ctor:function(m,T,h){A.call(this,m,T,h)},attributes:{target:String}}),s({tag:"body",name:"HTMLBodyElement",ctor:function(m,T,h){A.call(this,m,T,h)},events:["afterprint","beforeprint","beforeunload","blur","error","focus","hashchange","load","message","offline","online","pagehide","pageshow","popstate","resize","scroll","storage","unload"],attributes:{text:{type:String,treatNullAsEmptyString:!0},link:{type:String,treatNullAsEmptyString:!0},vLink:{type:String,treatNullAsEmptyString:!0},aLink:{type:String,treatNullAsEmptyString:!0},bgColor:{type:String,treatNullAsEmptyString:!0},background:String}}),s({tag:"button",name:"HTMLButtonElement",ctor:function(m,T,h){F.call(this,m,T,h)},props:R,attributes:{name:String,value:String,disabled:Boolean,autofocus:Boolean,type:{type:["submit","reset","button","menu"],missing:"submit"},formTarget:String,formAction:v,formNoValidate:Boolean,formMethod:{type:["get","post","dialog"],invalid:"get",missing:""},formEnctype:{type:["application/x-www-form-urlencoded","multipart/form-data","text/plain"],invalid:"application/x-www-form-urlencoded",missing:""}}}),s({tag:"dl",name:"HTMLDListElement",ctor:function(m,T,h){A.call(this,m,T,h)},attributes:{compact:Boolean}}),s({tag:"data",name:"HTMLDataElement",ctor:function(m,T,h){A.call(this,m,T,h)},attributes:{value:String}}),s({tag:"datalist",name:"HTMLDataListElement",ctor:function(m,T,h){A.call(this,m,T,h)}}),s({tag:"details",name:"HTMLDetailsElement",ctor:function(m,T,h){A.call(this,m,T,h)},attributes:{open:Boolean}}),s({tag:"div",name:"HTMLDivElement",ctor:function(m,T,h){A.call(this,m,T,h)},attributes:{align:String}}),s({tag:"embed",name:"HTMLEmbedElement",ctor:function(m,T,h){A.call(this,m,T,h)},attributes:{src:v,type:String,width:String,height:String,align:String,name:String}}),s({tag:"fieldset",name:"HTMLFieldSetElement",ctor:function(m,T,h){F.call(this,m,T,h)},props:R,attributes:{disabled:Boolean,name:String}}),s({tag:"form",name:"HTMLFormElement",ctor:function(m,T,h){A.call(this,m,T,h)},attributes:{action:String,autocomplete:{type:["on","off"],missing:"on"},name:String,acceptCharset:{name:"accept-charset"},target:String,noValidate:Boolean,method:{type:["get","post","dialog"],invalid:"get",missing:"get"},enctype:{type:["application/x-www-form-urlencoded","multipart/form-data","text/plain"],invalid:"application/x-www-form-urlencoded",missing:"application/x-www-form-urlencoded"},encoding:{name:"enctype",type:["application/x-www-form-urlencoded","multipart/form-data","text/plain"],invalid:"application/x-www-form-urlencoded",missing:"application/x-www-form-urlencoded"}}}),s({tag:"hr",name:"HTMLHRElement",ctor:function(m,T,h){A.call(this,m,T,h)},attributes:{align:String,color:String,noShade:Boolean,size:String,width:String}}),s({tag:"head",name:"HTMLHeadElement",ctor:function(m,T,h){A.call(this,m,T,h)}}),s({tags:["h1","h2","h3","h4","h5","h6"],name:"HTMLHeadingElement",ctor:function(m,T,h){A.call(this,m,T,h)},attributes:{align:String}}),s({tag:"html",name:"HTMLHtmlElement",ctor:function(m,T,h){A.call(this,m,T,h)},attributes:{xmlns:v,version:String}}),s({tag:"iframe",name:"HTMLIFrameElement",ctor:function(m,T,h){A.call(this,m,T,h)},attributes:{src:v,srcdoc:String,name:String,width:String,height:String,seamless:Boolean,allow:Boolean,allowFullscreen:Boolean,allowUserMedia:Boolean,allowPaymentRequest:Boolean,referrerPolicy:b,loading:{type:["eager","lazy"],treatNullAsEmptyString:!0},align:String,scrolling:String,frameBorder:String,longDesc:v,marginHeight:{type:String,treatNullAsEmptyString:!0},marginWidth:{type:String,treatNullAsEmptyString:!0}}}),s({tag:"img",name:"HTMLImageElement",ctor:function(m,T,h){A.call(this,m,T,h)},attributes:{alt:String,src:v,srcset:String,crossOrigin:p,useMap:String,isMap:Boolean,sizes:String,height:{type:"unsigned long",default:0},width:{type:"unsigned long",default:0},referrerPolicy:b,loading:{type:["eager","lazy"],missing:""},name:String,lowsrc:v,align:String,hspace:{type:"unsigned long",default:0},vspace:{type:"unsigned long",default:0},longDesc:v,border:{type:String,treatNullAsEmptyString:!0}}}),s({tag:"input",name:"HTMLInputElement",ctor:function(m,T,h){F.call(this,m,T,h)},props:{form:R.form,_post_click_activation_steps:{value:function(y){if(this.type==="checkbox")this.checked=!this.checked;else if(this.type==="radio")for(var m=this.form.getElementsByName(this.name),T=m.length-1;T>=0;T--){var h=m[T];h.checked=h===this}}}},attributes:{name:String,disabled:Boolean,autofocus:Boolean,accept:String,alt:String,max:String,min:String,pattern:String,placeholder:String,step:String,dirName:String,defaultValue:{name:"value"},multiple:Boolean,required:Boolean,readOnly:Boolean,checked:Boolean,value:String,src:v,defaultChecked:{name:"checked",type:Boolean},size:{type:"unsigned long",default:20,min:1,setmin:1},width:{type:"unsigned long",min:0,setmin:0,default:0},height:{type:"unsigned long",min:0,setmin:0,default:0},minLength:{type:"unsigned long",min:0,setmin:0,default:-1},maxLength:{type:"unsigned long",min:0,setmin:0,default:-1},autocomplete:String,type:{type:["text","hidden","search","tel","url","email","password","datetime","date","month","week","time","datetime-local","number","range","color","checkbox","radio","file","submit","image","reset","button"],missing:"text"},formTarget:String,formNoValidate:Boolean,formMethod:{type:["get","post"],invalid:"get",missing:""},formEnctype:{type:["application/x-www-form-urlencoded","multipart/form-data","text/plain"],invalid:"application/x-www-form-urlencoded",missing:""},inputMode:{type:["verbatim","latin","latin-name","latin-prose","full-width-latin","kana","kana-name","katakana","numeric","tel","email","url"],missing:""},align:String,useMap:String}}),s({tag:"keygen",name:"HTMLKeygenElement",ctor:function(m,T,h){F.call(this,m,T,h)},props:R,attributes:{name:String,disabled:Boolean,autofocus:Boolean,challenge:String,keytype:{type:["rsa"],missing:""}}}),s({tag:"li",name:"HTMLLIElement",ctor:function(m,T,h){A.call(this,m,T,h)},attributes:{value:{type:"long",default:0},type:String}}),s({tag:"label",name:"HTMLLabelElement",ctor:function(m,T,h){F.call(this,m,T,h)},props:R,attributes:{htmlFor:{name:"for",type:String}}}),s({tag:"legend",name:"HTMLLegendElement",ctor:function(m,T,h){A.call(this,m,T,h)},attributes:{align:String}}),s({tag:"link",name:"HTMLLinkElement",ctor:function(m,T,h){A.call(this,m,T,h)},attributes:{href:v,rel:String,media:String,hreflang:String,type:String,crossOrigin:p,nonce:String,integrity:String,referrerPolicy:b,imageSizes:String,imageSrcset:String,charset:String,rev:String,target:String}}),s({tag:"map",name:"HTMLMapElement",ctor:function(m,T,h){A.call(this,m,T,h)},attributes:{name:String}}),s({tag:"menu",name:"HTMLMenuElement",ctor:function(m,T,h){A.call(this,m,T,h)},attributes:{type:{type:["context","popup","toolbar"],missing:"toolbar"},label:String,compact:Boolean}}),s({tag:"meta",name:"HTMLMetaElement",ctor:function(m,T,h){A.call(this,m,T,h)},attributes:{name:String,content:String,httpEquiv:{name:"http-equiv",type:String},scheme:String}}),s({tag:"meter",name:"HTMLMeterElement",ctor:function(m,T,h){F.call(this,m,T,h)},props:R}),s({tags:["ins","del"],name:"HTMLModElement",ctor:function(m,T,h){A.call(this,m,T,h)},attributes:{cite:v,dateTime:String}}),s({tag:"ol",name:"HTMLOListElement",ctor:function(m,T,h){A.call(this,m,T,h)},props:{_numitems:{get:function(){var y=0;return this.childNodes.forEach(function(m){m.nodeType===O.ELEMENT_NODE&&m.tagName==="LI"&&y++}),y}}},attributes:{type:String,reversed:Boolean,start:{type:"long",default:function(){return this.reversed?this._numitems:1}},compact:Boolean}}),s({tag:"object",name:"HTMLObjectElement",ctor:function(m,T,h){F.call(this,m,T,h)},props:R,attributes:{data:v,type:String,name:String,useMap:String,typeMustMatch:Boolean,width:String,height:String,align:String,archive:String,code:String,declare:Boolean,hspace:{type:"unsigned long",default:0},standby:String,vspace:{type:"unsigned long",default:0},codeBase:v,codeType:String,border:{type:String,treatNullAsEmptyString:!0}}}),s({tag:"optgroup",name:"HTMLOptGroupElement",ctor:function(m,T,h){A.call(this,m,T,h)},attributes:{disabled:Boolean,label:String}}),s({tag:"option",name:"HTMLOptionElement",ctor:function(m,T,h){A.call(this,m,T,h)},props:{form:{get:function(){for(var y=this.parentNode;y&&y.nodeType===O.ELEMENT_NODE;){if(y.localName==="select")return y.form;y=y.parentNode}}},value:{get:function(){return this._getattr("value")||this.text},set:function(y){this._setattr("value",y)}},text:{get:function(){return this.textContent.replace(/[ \t\n\f\r]+/g," ").trim()},set:function(y){this.textContent=y}}},attributes:{disabled:Boolean,defaultSelected:{name:"selected",type:Boolean},label:String}}),s({tag:"output",name:"HTMLOutputElement",ctor:function(m,T,h){F.call(this,m,T,h)},props:R,attributes:{name:String}}),s({tag:"p",name:"HTMLParagraphElement",ctor:function(m,T,h){A.call(this,m,T,h)},attributes:{align:String}}),s({tag:"param",name:"HTMLParamElement",ctor:function(m,T,h){A.call(this,m,T,h)},attributes:{name:String,value:String,type:String,valueType:String}}),s({tags:["pre","listing","xmp"],name:"HTMLPreElement",ctor:function(m,T,h){A.call(this,m,T,h)},attributes:{width:{type:"long",default:0}}}),s({tag:"progress",name:"HTMLProgressElement",ctor:function(m,T,h){F.call(this,m,T,h)},props:R,attributes:{max:{type:Number,float:!0,default:1,min:0}}}),s({tags:["q","blockquote"],name:"HTMLQuoteElement",ctor:function(m,T,h){A.call(this,m,T,h)},attributes:{cite:v}}),s({tag:"script",name:"HTMLScriptElement",ctor:function(m,T,h){A.call(this,m,T,h)},props:{text:{get:function(){for(var y="",m=0,T=this.childNodes.length;m<T;m++){var h=this.childNodes[m];h.nodeType===O.TEXT_NODE&&(y+=h._data)}return y},set:function(y){this.removeChildren(),y!==null&&y!==""&&this.appendChild(this.ownerDocument.createTextNode(y))}}},attributes:{src:v,type:String,charset:String,referrerPolicy:b,defer:Boolean,async:Boolean,nomodule:Boolean,crossOrigin:p,nonce:String,integrity:String}}),s({tag:"select",name:"HTMLSelectElement",ctor:function(m,T,h){F.call(this,m,T,h)},props:{form:R.form,options:{get:function(){return this.getElementsByTagName("option")}}},attributes:{autocomplete:String,name:String,disabled:Boolean,autofocus:Boolean,multiple:Boolean,required:Boolean,size:{type:"unsigned long",default:0}}}),s({tag:"span",name:"HTMLSpanElement",ctor:function(m,T,h){A.call(this,m,T,h)}}),s({tag:"style",name:"HTMLStyleElement",ctor:function(m,T,h){A.call(this,m,T,h)},attributes:{media:String,type:String,scoped:Boolean}}),s({tag:"caption",name:"HTMLTableCaptionElement",ctor:function(m,T,h){A.call(this,m,T,h)},attributes:{align:String}}),s({name:"HTMLTableCellElement",ctor:function(m,T,h){A.call(this,m,T,h)},attributes:{colSpan:{type:"unsigned long",default:1},rowSpan:{type:"unsigned long",default:1},scope:{type:["row","col","rowgroup","colgroup"],missing:""},abbr:String,align:String,axis:String,height:String,width:String,ch:{name:"char",type:String},chOff:{name:"charoff",type:String},noWrap:Boolean,vAlign:String,bgColor:{type:String,treatNullAsEmptyString:!0}}}),s({tags:["col","colgroup"],name:"HTMLTableColElement",ctor:function(m,T,h){A.call(this,m,T,h)},attributes:{span:{type:"limited unsigned long with fallback",default:1,min:1},align:String,ch:{name:"char",type:String},chOff:{name:"charoff",type:String},vAlign:String,width:String}}),s({tag:"table",name:"HTMLTableElement",ctor:function(m,T,h){A.call(this,m,T,h)},props:{rows:{get:function(){return this.getElementsByTagName("tr")}}},attributes:{align:String,border:String,frame:String,rules:String,summary:String,width:String,bgColor:{type:String,treatNullAsEmptyString:!0},cellPadding:{type:String,treatNullAsEmptyString:!0},cellSpacing:{type:String,treatNullAsEmptyString:!0}}}),s({tag:"template",name:"HTMLTemplateElement",ctor:function(m,T,h){A.call(this,m,T,h),this._contentFragment=m._templateDoc.createDocumentFragment()},props:{content:{get:function(){return this._contentFragment}},serialize:{value:function(){return this.content.serialize()}}}}),s({tag:"tr",name:"HTMLTableRowElement",ctor:function(m,T,h){A.call(this,m,T,h)},props:{cells:{get:function(){return this.querySelectorAll("td,th")}}},attributes:{align:String,ch:{name:"char",type:String},chOff:{name:"charoff",type:String},vAlign:String,bgColor:{type:String,treatNullAsEmptyString:!0}}}),s({tags:["thead","tfoot","tbody"],name:"HTMLTableSectionElement",ctor:function(m,T,h){A.call(this,m,T,h)},props:{rows:{get:function(){return this.getElementsByTagName("tr")}}},attributes:{align:String,ch:{name:"char",type:String},chOff:{name:"charoff",type:String},vAlign:String}}),s({tag:"textarea",name:"HTMLTextAreaElement",ctor:function(m,T,h){F.call(this,m,T,h)},props:{form:R.form,type:{get:function(){return"textarea"}},defaultValue:{get:function(){return this.textContent},set:function(y){this.textContent=y}},value:{get:function(){return this.defaultValue},set:function(y){this.defaultValue=y}},textLength:{get:function(){return this.value.length}}},attributes:{autocomplete:String,name:String,disabled:Boolean,autofocus:Boolean,placeholder:String,wrap:String,dirName:String,required:Boolean,readOnly:Boolean,rows:{type:"limited unsigned long with fallback",default:2},cols:{type:"limited unsigned long with fallback",default:20},maxLength:{type:"unsigned long",min:0,setmin:0,default:-1},minLength:{type:"unsigned long",min:0,setmin:0,default:-1},inputMode:{type:["verbatim","latin","latin-name","latin-prose","full-width-latin","kana","kana-name","katakana","numeric","tel","email","url"],missing:""}}}),s({tag:"time",name:"HTMLTimeElement",ctor:function(m,T,h){A.call(this,m,T,h)},attributes:{dateTime:String,pubDate:Boolean}}),s({tag:"title",name:"HTMLTitleElement",ctor:function(m,T,h){A.call(this,m,T,h)},props:{text:{get:function(){return this.textContent}}}}),s({tag:"ul",name:"HTMLUListElement",ctor:function(m,T,h){A.call(this,m,T,h)},attributes:{type:String,compact:Boolean}}),s({name:"HTMLMediaElement",ctor:function(m,T,h){A.call(this,m,T,h)},attributes:{src:v,crossOrigin:p,preload:{type:["metadata","none","auto",{value:"",alias:"auto"}],missing:"auto"},loop:Boolean,autoplay:Boolean,mediaGroup:String,controls:Boolean,defaultMuted:{name:"muted",type:Boolean}}}),s({name:"HTMLAudioElement",tag:"audio",superclass:c.HTMLMediaElement,ctor:function(m,T,h){c.HTMLMediaElement.call(this,m,T,h)}}),s({name:"HTMLVideoElement",tag:"video",superclass:c.HTMLMediaElement,ctor:function(m,T,h){c.HTMLMediaElement.call(this,m,T,h)},attributes:{poster:v,width:{type:"unsigned long",min:0,default:0},height:{type:"unsigned long",min:0,default:0}}}),s({tag:"td",name:"HTMLTableDataCellElement",superclass:c.HTMLTableCellElement,ctor:function(m,T,h){c.HTMLTableCellElement.call(this,m,T,h)}}),s({tag:"th",name:"HTMLTableHeaderCellElement",superclass:c.HTMLTableCellElement,ctor:function(m,T,h){c.HTMLTableCellElement.call(this,m,T,h)}}),s({tag:"frameset",name:"HTMLFrameSetElement",ctor:function(m,T,h){A.call(this,m,T,h)}}),s({tag:"frame",name:"HTMLFrameElement",ctor:function(m,T,h){A.call(this,m,T,h)}}),s({tag:"canvas",name:"HTMLCanvasElement",ctor:function(m,T,h){A.call(this,m,T,h)},props:{getContext:{value:i.nyi},probablySupportsContext:{value:i.nyi},setContext:{value:i.nyi},transferControlToProxy:{value:i.nyi},toDataURL:{value:i.nyi},toBlob:{value:i.nyi}},attributes:{width:{type:"unsigned long",default:300},height:{type:"unsigned long",default:150}}}),s({tag:"dialog",name:"HTMLDialogElement",ctor:function(m,T,h){A.call(this,m,T,h)},props:{show:{value:i.nyi},showModal:{value:i.nyi},close:{value:i.nyi}},attributes:{open:Boolean,returnValue:String}}),s({tag:"menuitem",name:"HTMLMenuItemElement",ctor:function(m,T,h){A.call(this,m,T,h)},props:{_label:{get:function(){var y=this._getattr("label");return y!==null&&y!==""?y:(y=this.textContent,y.replace(/[ \t\n\f\r]+/g," ").trim())}},label:{get:function(){var y=this._getattr("label");return y!==null?y:this._label},set:function(y){this._setattr("label",y)}}},attributes:{type:{type:["command","checkbox","radio"],missing:"command"},icon:v,disabled:Boolean,checked:Boolean,radiogroup:String,default:Boolean}}),s({tag:"source",name:"HTMLSourceElement",ctor:function(m,T,h){A.call(this,m,T,h)},attributes:{srcset:String,sizes:String,media:String,src:v,type:String,width:String,height:String}}),s({tag:"track",name:"HTMLTrackElement",ctor:function(m,T,h){A.call(this,m,T,h)},attributes:{src:v,srclang:String,label:String,default:Boolean,kind:{type:["subtitles","captions","descriptions","chapters","metadata"],missing:"subtitles",invalid:"metadata"}},props:{NONE:{get:function(){return 0}},LOADING:{get:function(){return 1}},LOADED:{get:function(){return 2}},ERROR:{get:function(){return 3}},readyState:{get:i.nyi},track:{get:i.nyi}}}),s({tag:"font",name:"HTMLFontElement",ctor:function(m,T,h){A.call(this,m,T,h)},attributes:{color:{type:String,treatNullAsEmptyString:!0},face:{type:String},size:{type:String}}}),s({tag:"dir",name:"HTMLDirectoryElement",ctor:function(m,T,h){A.call(this,m,T,h)},attributes:{compact:Boolean}}),s({tags:["abbr","address","article","aside","b","bdi","bdo","cite","content","code","dd","dfn","dt","em","figcaption","figure","footer","header","hgroup","i","kbd","main","mark","nav","noscript","rb","rp","rt","rtc","ruby","s","samp","section","small","strong","sub","summary","sup","u","var","wbr","acronym","basefont","big","center","nobr","noembed","noframes","plaintext","strike","tt"]})}}),Bn=Z({"external/npm/node_modules/domino/lib/svg.js"(M){var O=Bt(),_=Rn(),f=Se(),i=Pr(),a=M.elements={},l=Object.create(null);M.createElement=function(s,v,p){var b=l[v]||u;return new b(s,v,p)};function c(s){return _(s,u,a,l)}var u=c({superclass:O,name:"SVGElement",ctor:function(v,p,b){O.call(this,v,p,f.NAMESPACE.SVG,b)},props:{style:{get:function(){return this._style||(this._style=new i(this)),this._style}}}});c({name:"SVGSVGElement",ctor:function(v,p,b){u.call(this,v,p,b)},tag:"svg",props:{createSVGRect:{value:function(){return M.createElement(this.ownerDocument,"rect",null)}}}}),c({tags:["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignObject","g","glyph","glyphRef","hkern","image","line","linearGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"]})}}),li=Z({"external/npm/node_modules/domino/lib/MutationConstants.js"(M,O){O.exports={VALUE:1,ATTR:2,REMOVE_ATTR:3,REMOVE:4,MOVE:5,INSERT:6}}}),jr=Z({"external/npm/node_modules/domino/lib/Document.js"(M,O){O.exports=j;var _=xe(),f=kt(),i=Hr(),a=Bt(),l=Dn(),c=Mn(),u=Rt(),s=An(),v=On(),p=pr(),b=ii(),D=si(),F=hr(),A=Fr(),ee=Rr(),R=Hn(),y=qr(),m=Ur(),T=Bn(),h=Se(),se=li(),le=h.NAMESPACE,ke=Ir().isApiWritable;function j(g,L){i.call(this),this.nodeType=_.DOCUMENT_NODE,this.isHTML=g,this._address=L||"about:blank",this.readyState="loading",this.implementation=new p(this),this.ownerDocument=null,this._contentType=g?"text/html":"application/xml",this.doctype=null,this.documentElement=null,this._templateDocCache=null,this._nodeIterators=null,this._nid=1,this._nextnid=2,this._nodes=[null,this],this.byId=Object.create(null),this.modclock=0}var k={event:"Event",customevent:"CustomEvent",uievent:"UIEvent",mouseevent:"MouseEvent"},I={events:"event",htmlevents:"event",mouseevents:"mouseevent",mutationevents:"mutationevent",uievents:"uievent"},Y=function(g,L,G){return{get:function(){var ue=g.call(this);return ue?ue[L]:G},set:function(ue){var Oe=g.call(this);Oe&&(Oe[L]=ue)}}};function t(g,L){var G,ue,Oe;return g===""&&(g=null),y.isValidQName(L)||h.InvalidCharacterError(),G=null,ue=L,Oe=L.indexOf(":"),Oe>=0&&(G=L.substring(0,Oe),ue=L.substring(Oe+1)),G!==null&&g===null&&h.NamespaceError(),G==="xml"&&g!==le.XML&&h.NamespaceError(),(G==="xmlns"||L==="xmlns")&&g!==le.XMLNS&&h.NamespaceError(),g===le.XMLNS&&!(G==="xmlns"||L==="xmlns")&&h.NamespaceError(),{namespace:g,prefix:G,localName:ue}}j.prototype=Object.create(i.prototype,{_setMutationHandler:{value:function(g){this.mutationHandler=g}},_dispatchRendererEvent:{value:function(g,L,G){var ue=this._nodes[g];ue&&ue._dispatchEvent(new u(L,G),!0)}},nodeName:{value:"#document"},nodeValue:{get:function(){return null},set:function(){}},documentURI:{get:function(){return this._address},set:h.nyi},compatMode:{get:function(){return this._quirks?"BackCompat":"CSS1Compat"}},createTextNode:{value:function(g){return new l(this,String(g))}},createComment:{value:function(g){return new c(this,g)}},createDocumentFragment:{value:function(){return new s(this)}},createProcessingInstruction:{value:function(g,L){return(!y.isValidName(g)||L.indexOf("?>")!==-1)&&h.InvalidCharacterError(),new v(this,g,L)}},createAttribute:{value:function(g){return g=String(g),y.isValidName(g)||h.InvalidCharacterError(),this.isHTML&&(g=h.toASCIILowerCase(g)),new a._Attr(null,g,null,null,"")}},createAttributeNS:{value:function(g,L){g=g==null||g===""?null:String(g),L=String(L);var G=t(g,L);return new a._Attr(null,G.localName,G.prefix,G.namespace,"")}},createElement:{value:function(g){return g=String(g),y.isValidName(g)||h.InvalidCharacterError(),this.isHTML?(/[A-Z]/.test(g)&&(g=h.toASCIILowerCase(g)),m.createElement(this,g,null)):this.contentType==="application/xhtml+xml"?m.createElement(this,g,null):new a(this,g,null,null)},writable:ke},createElementNS:{value:function(g,L){g=g==null||g===""?null:String(g),L=String(L);var G=t(g,L);return this._createElementNS(G.localName,G.namespace,G.prefix)},writable:ke},_createElementNS:{value:function(g,L,G){return L===le.HTML?m.createElement(this,g,G):L===le.SVG?T.createElement(this,g,G):new a(this,g,L,G)}},createEvent:{value:function(L){L=L.toLowerCase();var G=I[L]||L,ue=R[k[G]];if(ue){var Oe=new ue;return Oe._initialized=!1,Oe}else h.NotSupportedError()}},createTreeWalker:{value:function(g,L,G){if(!g)throw new TypeError("root argument is required");if(!(g instanceof _))throw new TypeError("root not a node");return L=L===void 0?F.SHOW_ALL:+L,G=G===void 0?null:G,new b(g,L,G)}},createNodeIterator:{value:function(g,L,G){if(!g)throw new TypeError("root argument is required");if(!(g instanceof _))throw new TypeError("root not a node");return L=L===void 0?F.SHOW_ALL:+L,G=G===void 0?null:G,new D(g,L,G)}},_attachNodeIterator:{value:function(g){this._nodeIterators||(this._nodeIterators=[]),this._nodeIterators.push(g)}},_detachNodeIterator:{value:function(g){var L=this._nodeIterators.indexOf(g);this._nodeIterators.splice(L,1)}},_preremoveNodeIterators:{value:function(g){this._nodeIterators&&this._nodeIterators.forEach(function(L){L._preremove(g)})}},_updateDocTypeElement:{value:function(){this.doctype=this.documentElement=null;for(var L=this.firstChild;L!==null;L=L.nextSibling)L.nodeType===_.DOCUMENT_TYPE_NODE?this.doctype=L:L.nodeType===_.ELEMENT_NODE&&(this.documentElement=L)}},insertBefore:{value:function(L,G){return _.prototype.insertBefore.call(this,L,G),this._updateDocTypeElement(),L}},replaceChild:{value:function(L,G){return _.prototype.replaceChild.call(this,L,G),this._updateDocTypeElement(),G}},removeChild:{value:function(L){return _.prototype.removeChild.call(this,L),this._updateDocTypeElement(),L}},getElementById:{value:function(g){var L=this.byId[g];return L?L instanceof oe?L.getFirst():L:null}},_hasMultipleElementsWithId:{value:function(g){return this.byId[g]instanceof oe}},getElementsByName:{value:a.prototype.getElementsByName},getElementsByTagName:{value:a.prototype.getElementsByTagName},getElementsByTagNameNS:{value:a.prototype.getElementsByTagNameNS},getElementsByClassName:{value:a.prototype.getElementsByClassName},adoptNode:{value:function(L){return L.nodeType===_.DOCUMENT_NODE&&h.NotSupportedError(),L.nodeType===_.ATTRIBUTE_NODE||(L.parentNode&&L.parentNode.removeChild(L),L.ownerDocument!==this&&ce(L,this)),L}},importNode:{value:function(L,G){return this.adoptNode(L.cloneNode(G))},writable:ke},origin:{get:function(){return null}},characterSet:{get:function(){return"UTF-8"}},contentType:{get:function(){return this._contentType}},URL:{get:function(){return this._address}},domain:{get:h.nyi,set:h.nyi},referrer:{get:h.nyi},cookie:{get:h.nyi,set:h.nyi},lastModified:{get:h.nyi},location:{get:function(){return this.defaultView?this.defaultView.location:null},set:h.nyi},_titleElement:{get:function(){return this.getElementsByTagName("title").item(0)||null}},title:{get:function(){var g=this._titleElement,L=g?g.textContent:"";return L.replace(/[ \t\n\r\f]+/g," ").replace(/(^ )|( $)/g,"")},set:function(g){var L=this._titleElement,G=this.head;!L&&!G||(L||(L=this.createElement("title"),G.appendChild(L)),L.textContent=g)}},dir:Y(function(){var g=this.documentElement;if(g&&g.tagName==="HTML")return g},"dir",""),fgColor:Y(function(){return this.body},"text",""),linkColor:Y(function(){return this.body},"link",""),vlinkColor:Y(function(){return this.body},"vLink",""),alinkColor:Y(function(){return this.body},"aLink",""),bgColor:Y(function(){return this.body},"bgColor",""),charset:{get:function(){return this.characterSet}},inputEncoding:{get:function(){return this.characterSet}},scrollingElement:{get:function(){return this._quirks?this.body:this.documentElement}},body:{get:function(){return o(this.documentElement,"body")},set:h.nyi},head:{get:function(){return o(this.documentElement,"head")}},images:{get:h.nyi},embeds:{get:h.nyi},plugins:{get:h.nyi},links:{get:h.nyi},forms:{get:h.nyi},scripts:{get:h.nyi},applets:{get:function(){return[]}},activeElement:{get:function(){return null}},innerHTML:{get:function(){return this.serialize()},set:h.nyi},outerHTML:{get:function(){return this.serialize()},set:h.nyi},write:{value:function(g){if(this.isHTML||h.InvalidStateError(),!!this._parser){var L=arguments.join("");this._parser.parse(L)}}},writeln:{value:function(L){this.write(Array.prototype.join.call(arguments,"")+`
`)}},open:{value:function(){this.documentElement=null}},close:{value:function(){this.readyState="interactive",this._dispatchEvent(new u("readystatechange"),!0),this._dispatchEvent(new u("DOMContentLoaded"),!0),this.readyState="complete",this._dispatchEvent(new u("readystatechange"),!0),this.defaultView&&this.defaultView._dispatchEvent(new u("load"),!0)}},clone:{value:function(){var L=new j(this.isHTML,this._address);return L._quirks=this._quirks,L._contentType=this._contentType,L}},cloneNode:{value:function(L){var G=_.prototype.cloneNode.call(this,!1);if(L)for(var ue=this.firstChild;ue!==null;ue=ue.nextSibling)G._appendChild(G.importNode(ue,!0));return G._updateDocTypeElement(),G}},isEqual:{value:function(L){return!0}},mutateValue:{value:function(g){this.mutationHandler&&this.mutationHandler({type:se.VALUE,target:g,data:g.data})}},mutateAttr:{value:function(g,L){this.mutationHandler&&this.mutationHandler({type:se.ATTR,target:g.ownerElement,attr:g})}},mutateRemoveAttr:{value:function(g){this.mutationHandler&&this.mutationHandler({type:se.REMOVE_ATTR,target:g.ownerElement,attr:g})}},mutateRemove:{value:function(g){this.mutationHandler&&this.mutationHandler({type:se.REMOVE,target:g.parentNode,node:g}),K(g)}},mutateInsert:{value:function(g){P(g),this.mutationHandler&&this.mutationHandler({type:se.INSERT,target:g.parentNode,node:g})}},mutateMove:{value:function(g){this.mutationHandler&&this.mutationHandler({type:se.MOVE,target:g})}},addId:{value:function(L,G){var ue=this.byId[L];ue?(ue instanceof oe||(ue=new oe(ue),this.byId[L]=ue),ue.add(G)):this.byId[L]=G}},delId:{value:function(L,G){var ue=this.byId[L];h.assert(ue),ue instanceof oe?(ue.del(G),ue.length===1&&(this.byId[L]=ue.downgrade())):this.byId[L]=void 0}},_resolve:{value:function(g){return new A(this._documentBaseURL).resolve(g)}},_documentBaseURL:{get:function(){var g=this._address;g==="about:blank"&&(g="/");var L=this.querySelector("base[href]");return L?new A(g).resolve(L.getAttribute("href")):g}},_templateDoc:{get:function(){if(!this._templateDocCache){var g=new j(this.isHTML,this._address);this._templateDocCache=g._templateDocCache=g}return this._templateDocCache}},querySelector:{value:function(g){return ee(g,this)[0]}},querySelectorAll:{value:function(g){var L=ee(g,this);return L.item?L:new f(L)}}});var r=["abort","canplay","canplaythrough","change","click","contextmenu","cuechange","dblclick","drag","dragend","dragenter","dragleave","dragover","dragstart","drop","durationchange","emptied","ended","input","invalid","keydown","keypress","keyup","loadeddata","loadedmetadata","loadstart","mousedown","mousemove","mouseout","mouseover","mouseup","mousewheel","pause","play","playing","progress","ratechange","readystatechange","reset","seeked","seeking","select","show","stalled","submit","suspend","timeupdate","volumechange","waiting","blur","error","focus","load","scroll"];r.forEach(function(g){Object.defineProperty(j.prototype,"on"+g,{get:function(){return this._getEventHandler(g)},set:function(L){this._setEventHandler(g,L)}})});function o(g,L){if(g&&g.isHTML){for(var G=g.firstChild;G!==null;G=G.nextSibling)if(G.nodeType===_.ELEMENT_NODE&&G.localName===L&&G.namespaceURI===le.HTML)return G}return null}function w(g){if(g._nid=g.ownerDocument._nextnid++,g.ownerDocument._nodes[g._nid]=g,g.nodeType===_.ELEMENT_NODE){var L=g.getAttribute("id");L&&g.ownerDocument.addId(L,g),g._roothook&&g._roothook()}}function C(g){if(g.nodeType===_.ELEMENT_NODE){var L=g.getAttribute("id");L&&g.ownerDocument.delId(L,g)}g.ownerDocument._nodes[g._nid]=void 0,g._nid=void 0}function P(g){if(w(g),g.nodeType===_.ELEMENT_NODE)for(var L=g.firstChild;L!==null;L=L.nextSibling)P(L)}function K(g){C(g);for(var L=g.firstChild;L!==null;L=L.nextSibling)K(L)}function ce(g,L){g.ownerDocument=L,g._lastModTime=void 0,Object.prototype.hasOwnProperty.call(g,"_tagName")&&(g._tagName=void 0);for(var G=g.firstChild;G!==null;G=G.nextSibling)ce(G,L)}function oe(g){this.nodes=Object.create(null),this.nodes[g._nid]=g,this.length=1,this.firstNode=void 0}oe.prototype.add=function(g){this.nodes[g._nid]||(this.nodes[g._nid]=g,this.length++,this.firstNode=void 0)},oe.prototype.del=function(g){this.nodes[g._nid]&&(delete this.nodes[g._nid],this.length--,this.firstNode=void 0)},oe.prototype.getFirst=function(){if(!this.firstNode){var g;for(g in this.nodes)(this.firstNode===void 0||this.firstNode.compareDocumentPosition(this.nodes[g])&_.DOCUMENT_POSITION_PRECEDING)&&(this.firstNode=this.nodes[g])}return this.firstNode},oe.prototype.downgrade=function(){if(this.length===1){var g;for(g in this.nodes)return this.nodes[g]}return this}}}),Vr=Z({"external/npm/node_modules/domino/lib/DocumentType.js"(M,O){O.exports=a;var _=xe(),f=xn(),i=Br();function a(l,c,u,s){f.call(this),this.nodeType=_.DOCUMENT_TYPE_NODE,this.ownerDocument=l||null,this.name=c,this.publicId=u||"",this.systemId=s||""}a.prototype=Object.create(f.prototype,{nodeName:{get:function(){return this.name}},nodeValue:{get:function(){return null},set:function(){}},clone:{value:function(){return new a(this.ownerDocument,this.name,this.publicId,this.systemId)}},isEqual:{value:function(c){return this.name===c.name&&this.publicId===c.publicId&&this.systemId===c.systemId}}}),Object.defineProperties(a.prototype,i)}}),Gr=Z({"external/npm/node_modules/domino/lib/HTMLParser.js"(M,O){O.exports=fe;var _=jr(),f=Vr(),i=xe(),a=Se().NAMESPACE,l=Ur(),c=l.elements,u=Function.prototype.apply.bind(Array.prototype.push),s=-1,v=1,p=2,b=3,D=4,F=5,A=[],ee=/^HTML$|^-\/\/W3O\/\/DTD W3 HTML Strict 3\.0\/\/EN\/\/$|^-\/W3C\/DTD HTML 4\.0 Transitional\/EN$|^\+\/\/Silmaril\/\/dtd html Pro v0r11 19970101\/\/|^-\/\/AdvaSoft Ltd\/\/DTD HTML 3\.0 asWedit \+ extensions\/\/|^-\/\/AS\/\/DTD HTML 3\.0 asWedit \+ extensions\/\/|^-\/\/IETF\/\/DTD HTML 2\.0 Level 1\/\/|^-\/\/IETF\/\/DTD HTML 2\.0 Level 2\/\/|^-\/\/IETF\/\/DTD HTML 2\.0 Strict Level 1\/\/|^-\/\/IETF\/\/DTD HTML 2\.0 Strict Level 2\/\/|^-\/\/IETF\/\/DTD HTML 2\.0 Strict\/\/|^-\/\/IETF\/\/DTD HTML 2\.0\/\/|^-\/\/IETF\/\/DTD HTML 2\.1E\/\/|^-\/\/IETF\/\/DTD HTML 3\.0\/\/|^-\/\/IETF\/\/DTD HTML 3\.2 Final\/\/|^-\/\/IETF\/\/DTD HTML 3\.2\/\/|^-\/\/IETF\/\/DTD HTML 3\/\/|^-\/\/IETF\/\/DTD HTML Level 0\/\/|^-\/\/IETF\/\/DTD HTML Level 1\/\/|^-\/\/IETF\/\/DTD HTML Level 2\/\/|^-\/\/IETF\/\/DTD HTML Level 3\/\/|^-\/\/IETF\/\/DTD HTML Strict Level 0\/\/|^-\/\/IETF\/\/DTD HTML Strict Level 1\/\/|^-\/\/IETF\/\/DTD HTML Strict Level 2\/\/|^-\/\/IETF\/\/DTD HTML Strict Level 3\/\/|^-\/\/IETF\/\/DTD HTML Strict\/\/|^-\/\/IETF\/\/DTD HTML\/\/|^-\/\/Metrius\/\/DTD Metrius Presentational\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 2\.0 HTML Strict\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 2\.0 HTML\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 2\.0 Tables\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 3\.0 HTML Strict\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 3\.0 HTML\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 3\.0 Tables\/\/|^-\/\/Netscape Comm\. Corp\.\/\/DTD HTML\/\/|^-\/\/Netscape Comm\. Corp\.\/\/DTD Strict HTML\/\/|^-\/\/O'Reilly and Associates\/\/DTD HTML 2\.0\/\/|^-\/\/O'Reilly and Associates\/\/DTD HTML Extended 1\.0\/\/|^-\/\/O'Reilly and Associates\/\/DTD HTML Extended Relaxed 1\.0\/\/|^-\/\/SoftQuad Software\/\/DTD HoTMetaL PRO 6\.0::19990601::extensions to HTML 4\.0\/\/|^-\/\/SoftQuad\/\/DTD HoTMetaL PRO 4\.0::19971010::extensions to HTML 4\.0\/\/|^-\/\/Spyglass\/\/DTD HTML 2\.0 Extended\/\/|^-\/\/SQ\/\/DTD HTML 2\.0 HoTMetaL \+ extensions\/\/|^-\/\/Sun Microsystems Corp\.\/\/DTD HotJava HTML\/\/|^-\/\/Sun Microsystems Corp\.\/\/DTD HotJava Strict HTML\/\/|^-\/\/W3C\/\/DTD HTML 3 1995-03-24\/\/|^-\/\/W3C\/\/DTD HTML 3\.2 Draft\/\/|^-\/\/W3C\/\/DTD HTML 3\.2 Final\/\/|^-\/\/W3C\/\/DTD HTML 3\.2\/\/|^-\/\/W3C\/\/DTD HTML 3\.2S Draft\/\/|^-\/\/W3C\/\/DTD HTML 4\.0 Frameset\/\/|^-\/\/W3C\/\/DTD HTML 4\.0 Transitional\/\/|^-\/\/W3C\/\/DTD HTML Experimental 19960712\/\/|^-\/\/W3C\/\/DTD HTML Experimental 970421\/\/|^-\/\/W3C\/\/DTD W3 HTML\/\/|^-\/\/W3O\/\/DTD W3 HTML 3\.0\/\/|^-\/\/WebTechs\/\/DTD Mozilla HTML 2\.0\/\/|^-\/\/WebTechs\/\/DTD Mozilla HTML\/\//i,R="http://www.ibm.com/data/dtd/v11/ibmxhtml1-transitional.dtd",y=/^-\/\/W3C\/\/DTD HTML 4\.01 Frameset\/\/|^-\/\/W3C\/\/DTD HTML 4\.01 Transitional\/\//i,m=/^-\/\/W3C\/\/DTD XHTML 1\.0 Frameset\/\/|^-\/\/W3C\/\/DTD XHTML 1\.0 Transitional\/\//i,T=Object.create(null);T[a.HTML]={__proto__:null,address:!0,applet:!0,area:!0,article:!0,aside:!0,base:!0,basefont:!0,bgsound:!0,blockquote:!0,body:!0,br:!0,button:!0,caption:!0,center:!0,col:!0,colgroup:!0,dd:!0,details:!0,dir:!0,div:!0,dl:!0,dt:!0,embed:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,frame:!0,frameset:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,head:!0,header:!0,hgroup:!0,hr:!0,html:!0,iframe:!0,img:!0,input:!0,li:!0,link:!0,listing:!0,main:!0,marquee:!0,menu:!0,meta:!0,nav:!0,noembed:!0,noframes:!0,noscript:!0,object:!0,ol:!0,p:!0,param:!0,plaintext:!0,pre:!0,script:!0,section:!0,select:!0,source:!0,style:!0,summary:!0,table:!0,tbody:!0,td:!0,template:!0,textarea:!0,tfoot:!0,th:!0,thead:!0,title:!0,tr:!0,track:!0,ul:!0,wbr:!0,xmp:!0},T[a.SVG]={__proto__:null,foreignObject:!0,desc:!0,title:!0},T[a.MATHML]={__proto__:null,mi:!0,mo:!0,mn:!0,ms:!0,mtext:!0,"annotation-xml":!0};var h=Object.create(null);h[a.HTML]={__proto__:null,address:!0,div:!0,p:!0};var se=Object.create(null);se[a.HTML]={__proto__:null,dd:!0,dt:!0};var le=Object.create(null);le[a.HTML]={__proto__:null,table:!0,thead:!0,tbody:!0,tfoot:!0,tr:!0};var ke=Object.create(null);ke[a.HTML]={__proto__:null,dd:!0,dt:!0,li:!0,menuitem:!0,optgroup:!0,option:!0,p:!0,rb:!0,rp:!0,rt:!0,rtc:!0};var j=Object.create(null);j[a.HTML]={__proto__:null,caption:!0,colgroup:!0,dd:!0,dt:!0,li:!0,optgroup:!0,option:!0,p:!0,rb:!0,rp:!0,rt:!0,rtc:!0,tbody:!0,td:!0,tfoot:!0,th:!0,thead:!0,tr:!0};var k=Object.create(null);k[a.HTML]={__proto__:null,table:!0,template:!0,html:!0};var I=Object.create(null);I[a.HTML]={__proto__:null,tbody:!0,tfoot:!0,thead:!0,template:!0,html:!0};var Y=Object.create(null);Y[a.HTML]={__proto__:null,tr:!0,template:!0,html:!0};var t=Object.create(null);t[a.HTML]={__proto__:null,button:!0,fieldset:!0,input:!0,keygen:!0,object:!0,output:!0,select:!0,textarea:!0,img:!0};var r=Object.create(null);r[a.HTML]={__proto__:null,applet:!0,caption:!0,html:!0,table:!0,td:!0,th:!0,marquee:!0,object:!0,template:!0},r[a.MATHML]={__proto__:null,mi:!0,mo:!0,mn:!0,ms:!0,mtext:!0,"annotation-xml":!0},r[a.SVG]={__proto__:null,foreignObject:!0,desc:!0,title:!0};var o=Object.create(r);o[a.HTML]=Object.create(r[a.HTML]),o[a.HTML].ol=!0,o[a.HTML].ul=!0;var w=Object.create(r);w[a.HTML]=Object.create(r[a.HTML]),w[a.HTML].button=!0;var C=Object.create(null);C[a.HTML]={__proto__:null,html:!0,table:!0,template:!0};var P=Object.create(null);P[a.HTML]={__proto__:null,optgroup:!0,option:!0};var K=Object.create(null);K[a.MATHML]={__proto__:null,mi:!0,mo:!0,mn:!0,ms:!0,mtext:!0};var ce=Object.create(null);ce[a.SVG]={__proto__:null,foreignObject:!0,desc:!0,title:!0};var oe={__proto__:null,"xlink:actuate":a.XLINK,"xlink:arcrole":a.XLINK,"xlink:href":a.XLINK,"xlink:role":a.XLINK,"xlink:show":a.XLINK,"xlink:title":a.XLINK,"xlink:type":a.XLINK,"xml:base":a.XML,"xml:lang":a.XML,"xml:space":a.XML,xmlns:a.XMLNS,"xmlns:xlink":a.XMLNS},g={__proto__:null,attributename:"attributeName",attributetype:"attributeType",basefrequency:"baseFrequency",baseprofile:"baseProfile",calcmode:"calcMode",clippathunits:"clipPathUnits",diffuseconstant:"diffuseConstant",edgemode:"edgeMode",filterunits:"filterUnits",glyphref:"glyphRef",gradienttransform:"gradientTransform",gradientunits:"gradientUnits",kernelmatrix:"kernelMatrix",kernelunitlength:"kernelUnitLength",keypoints:"keyPoints",keysplines:"keySplines",keytimes:"keyTimes",lengthadjust:"lengthAdjust",limitingconeangle:"limitingConeAngle",markerheight:"markerHeight",markerunits:"markerUnits",markerwidth:"markerWidth",maskcontentunits:"maskContentUnits",maskunits:"maskUnits",numoctaves:"numOctaves",pathlength:"pathLength",patterncontentunits:"patternContentUnits",patterntransform:"patternTransform",patternunits:"patternUnits",pointsatx:"pointsAtX",pointsaty:"pointsAtY",pointsatz:"pointsAtZ",preservealpha:"preserveAlpha",preserveaspectratio:"preserveAspectRatio",primitiveunits:"primitiveUnits",refx:"refX",refy:"refY",repeatcount:"repeatCount",repeatdur:"repeatDur",requiredextensions:"requiredExtensions",requiredfeatures:"requiredFeatures",specularconstant:"specularConstant",specularexponent:"specularExponent",spreadmethod:"spreadMethod",startoffset:"startOffset",stddeviation:"stdDeviation",stitchtiles:"stitchTiles",surfacescale:"surfaceScale",systemlanguage:"systemLanguage",tablevalues:"tableValues",targetx:"targetX",targety:"targetY",textlength:"textLength",viewbox:"viewBox",viewtarget:"viewTarget",xchannelselector:"xChannelSelector",ychannelselector:"yChannelSelector",zoomandpan:"zoomAndPan"},L={__proto__:null,altglyph:"altGlyph",altglyphdef:"altGlyphDef",altglyphitem:"altGlyphItem",animatecolor:"animateColor",animatemotion:"animateMotion",animatetransform:"animateTransform",clippath:"clipPath",feblend:"feBlend",fecolormatrix:"feColorMatrix",fecomponenttransfer:"feComponentTransfer",fecomposite:"feComposite",feconvolvematrix:"feConvolveMatrix",fediffuselighting:"feDiffuseLighting",fedisplacementmap:"feDisplacementMap",fedistantlight:"feDistantLight",feflood:"feFlood",fefunca:"feFuncA",fefuncb:"feFuncB",fefuncg:"feFuncG",fefuncr:"feFuncR",fegaussianblur:"feGaussianBlur",feimage:"feImage",femerge:"feMerge",femergenode:"feMergeNode",femorphology:"feMorphology",feoffset:"feOffset",fepointlight:"fePointLight",fespecularlighting:"feSpecularLighting",fespotlight:"feSpotLight",fetile:"feTile",feturbulence:"feTurbulence",foreignobject:"foreignObject",glyphref:"glyphRef",lineargradient:"linearGradient",radialgradient:"radialGradient",textpath:"textPath"},G={__proto__:null,0:65533,128:8364,130:8218,131:402,132:8222,133:8230,134:8224,135:8225,136:710,137:8240,138:352,139:8249,140:338,142:381,145:8216,146:8217,147:8220,148:8221,149:8226,150:8211,151:8212,152:732,153:8482,154:353,155:8250,156:339,158:382,159:376},ue={__proto__:null,AElig:198,"AElig;":198,AMP:38,"AMP;":38,Aacute:193,"Aacute;":193,"Abreve;":258,Acirc:194,"Acirc;":194,"Acy;":1040,"Afr;":[55349,56580],Agrave:192,"Agrave;":192,"Alpha;":913,"Amacr;":256,"And;":10835,"Aogon;":260,"Aopf;":[55349,56632],"ApplyFunction;":8289,Aring:197,"Aring;":197,"Ascr;":[55349,56476],"Assign;":8788,Atilde:195,"Atilde;":195,Auml:196,"Auml;":196,"Backslash;":8726,"Barv;":10983,"Barwed;":8966,"Bcy;":1041,"Because;":8757,"Bernoullis;":8492,"Beta;":914,"Bfr;":[55349,56581],"Bopf;":[55349,56633],"Breve;":728,"Bscr;":8492,"Bumpeq;":8782,"CHcy;":1063,COPY:169,"COPY;":169,"Cacute;":262,"Cap;":8914,"CapitalDifferentialD;":8517,"Cayleys;":8493,"Ccaron;":268,Ccedil:199,"Ccedil;":199,"Ccirc;":264,"Cconint;":8752,"Cdot;":266,"Cedilla;":184,"CenterDot;":183,"Cfr;":8493,"Chi;":935,"CircleDot;":8857,"CircleMinus;":8854,"CirclePlus;":8853,"CircleTimes;":8855,"ClockwiseContourIntegral;":8754,"CloseCurlyDoubleQuote;":8221,"CloseCurlyQuote;":8217,"Colon;":8759,"Colone;":10868,"Congruent;":8801,"Conint;":8751,"ContourIntegral;":8750,"Copf;":8450,"Coproduct;":8720,"CounterClockwiseContourIntegral;":8755,"Cross;":10799,"Cscr;":[55349,56478],"Cup;":8915,"CupCap;":8781,"DD;":8517,"DDotrahd;":10513,"DJcy;":1026,"DScy;":1029,"DZcy;":1039,"Dagger;":8225,"Darr;":8609,"Dashv;":10980,"Dcaron;":270,"Dcy;":1044,"Del;":8711,"Delta;":916,"Dfr;":[55349,56583],"DiacriticalAcute;":180,"DiacriticalDot;":729,"DiacriticalDoubleAcute;":733,"DiacriticalGrave;":96,"DiacriticalTilde;":732,"Diamond;":8900,"DifferentialD;":8518,"Dopf;":[55349,56635],"Dot;":168,"DotDot;":8412,"DotEqual;":8784,"DoubleContourIntegral;":8751,"DoubleDot;":168,"DoubleDownArrow;":8659,"DoubleLeftArrow;":8656,"DoubleLeftRightArrow;":8660,"DoubleLeftTee;":10980,"DoubleLongLeftArrow;":10232,"DoubleLongLeftRightArrow;":10234,"DoubleLongRightArrow;":10233,"DoubleRightArrow;":8658,"DoubleRightTee;":8872,"DoubleUpArrow;":8657,"DoubleUpDownArrow;":8661,"DoubleVerticalBar;":8741,"DownArrow;":8595,"DownArrowBar;":10515,"DownArrowUpArrow;":8693,"DownBreve;":785,"DownLeftRightVector;":10576,"DownLeftTeeVector;":10590,"DownLeftVector;":8637,"DownLeftVectorBar;":10582,"DownRightTeeVector;":10591,"DownRightVector;":8641,"DownRightVectorBar;":10583,"DownTee;":8868,"DownTeeArrow;":8615,"Downarrow;":8659,"Dscr;":[55349,56479],"Dstrok;":272,"ENG;":330,ETH:208,"ETH;":208,Eacute:201,"Eacute;":201,"Ecaron;":282,Ecirc:202,"Ecirc;":202,"Ecy;":1069,"Edot;":278,"Efr;":[55349,56584],Egrave:200,"Egrave;":200,"Element;":8712,"Emacr;":274,"EmptySmallSquare;":9723,"EmptyVerySmallSquare;":9643,"Eogon;":280,"Eopf;":[55349,56636],"Epsilon;":917,"Equal;":10869,"EqualTilde;":8770,"Equilibrium;":8652,"Escr;":8496,"Esim;":10867,"Eta;":919,Euml:203,"Euml;":203,"Exists;":8707,"ExponentialE;":8519,"Fcy;":1060,"Ffr;":[55349,56585],"FilledSmallSquare;":9724,"FilledVerySmallSquare;":9642,"Fopf;":[55349,56637],"ForAll;":8704,"Fouriertrf;":8497,"Fscr;":8497,"GJcy;":1027,GT:62,"GT;":62,"Gamma;":915,"Gammad;":988,"Gbreve;":286,"Gcedil;":290,"Gcirc;":284,"Gcy;":1043,"Gdot;":288,"Gfr;":[55349,56586],"Gg;":8921,"Gopf;":[55349,56638],"GreaterEqual;":8805,"GreaterEqualLess;":8923,"GreaterFullEqual;":8807,"GreaterGreater;":10914,"GreaterLess;":8823,"GreaterSlantEqual;":10878,"GreaterTilde;":8819,"Gscr;":[55349,56482],"Gt;":8811,"HARDcy;":1066,"Hacek;":711,"Hat;":94,"Hcirc;":292,"Hfr;":8460,"HilbertSpace;":8459,"Hopf;":8461,"HorizontalLine;":9472,"Hscr;":8459,"Hstrok;":294,"HumpDownHump;":8782,"HumpEqual;":8783,"IEcy;":1045,"IJlig;":306,"IOcy;":1025,Iacute:205,"Iacute;":205,Icirc:206,"Icirc;":206,"Icy;":1048,"Idot;":304,"Ifr;":8465,Igrave:204,"Igrave;":204,"Im;":8465,"Imacr;":298,"ImaginaryI;":8520,"Implies;":8658,"Int;":8748,"Integral;":8747,"Intersection;":8898,"InvisibleComma;":8291,"InvisibleTimes;":8290,"Iogon;":302,"Iopf;":[55349,56640],"Iota;":921,"Iscr;":8464,"Itilde;":296,"Iukcy;":1030,Iuml:207,"Iuml;":207,"Jcirc;":308,"Jcy;":1049,"Jfr;":[55349,56589],"Jopf;":[55349,56641],"Jscr;":[55349,56485],"Jsercy;":1032,"Jukcy;":1028,"KHcy;":1061,"KJcy;":1036,"Kappa;":922,"Kcedil;":310,"Kcy;":1050,"Kfr;":[55349,56590],"Kopf;":[55349,56642],"Kscr;":[55349,56486],"LJcy;":1033,LT:60,"LT;":60,"Lacute;":313,"Lambda;":923,"Lang;":10218,"Laplacetrf;":8466,"Larr;":8606,"Lcaron;":317,"Lcedil;":315,"Lcy;":1051,"LeftAngleBracket;":10216,"LeftArrow;":8592,"LeftArrowBar;":8676,"LeftArrowRightArrow;":8646,"LeftCeiling;":8968,"LeftDoubleBracket;":10214,"LeftDownTeeVector;":10593,"LeftDownVector;":8643,"LeftDownVectorBar;":10585,"LeftFloor;":8970,"LeftRightArrow;":8596,"LeftRightVector;":10574,"LeftTee;":8867,"LeftTeeArrow;":8612,"LeftTeeVector;":10586,"LeftTriangle;":8882,"LeftTriangleBar;":10703,"LeftTriangleEqual;":8884,"LeftUpDownVector;":10577,"LeftUpTeeVector;":10592,"LeftUpVector;":8639,"LeftUpVectorBar;":10584,"LeftVector;":8636,"LeftVectorBar;":10578,"Leftarrow;":8656,"Leftrightarrow;":8660,"LessEqualGreater;":8922,"LessFullEqual;":8806,"LessGreater;":8822,"LessLess;":10913,"LessSlantEqual;":10877,"LessTilde;":8818,"Lfr;":[55349,56591],"Ll;":8920,"Lleftarrow;":8666,"Lmidot;":319,"LongLeftArrow;":10229,"LongLeftRightArrow;":10231,"LongRightArrow;":10230,"Longleftarrow;":10232,"Longleftrightarrow;":10234,"Longrightarrow;":10233,"Lopf;":[55349,56643],"LowerLeftArrow;":8601,"LowerRightArrow;":8600,"Lscr;":8466,"Lsh;":8624,"Lstrok;":321,"Lt;":8810,"Map;":10501,"Mcy;":1052,"MediumSpace;":8287,"Mellintrf;":8499,"Mfr;":[55349,56592],"MinusPlus;":8723,"Mopf;":[55349,56644],"Mscr;":8499,"Mu;":924,"NJcy;":1034,"Nacute;":323,"Ncaron;":327,"Ncedil;":325,"Ncy;":1053,"NegativeMediumSpace;":8203,"NegativeThickSpace;":8203,"NegativeThinSpace;":8203,"NegativeVeryThinSpace;":8203,"NestedGreaterGreater;":8811,"NestedLessLess;":8810,"NewLine;":10,"Nfr;":[55349,56593],"NoBreak;":8288,"NonBreakingSpace;":160,"Nopf;":8469,"Not;":10988,"NotCongruent;":8802,"NotCupCap;":8813,"NotDoubleVerticalBar;":8742,"NotElement;":8713,"NotEqual;":8800,"NotEqualTilde;":[8770,824],"NotExists;":8708,"NotGreater;":8815,"NotGreaterEqual;":8817,"NotGreaterFullEqual;":[8807,824],"NotGreaterGreater;":[8811,824],"NotGreaterLess;":8825,"NotGreaterSlantEqual;":[10878,824],"NotGreaterTilde;":8821,"NotHumpDownHump;":[8782,824],"NotHumpEqual;":[8783,824],"NotLeftTriangle;":8938,"NotLeftTriangleBar;":[10703,824],"NotLeftTriangleEqual;":8940,"NotLess;":8814,"NotLessEqual;":8816,"NotLessGreater;":8824,"NotLessLess;":[8810,824],"NotLessSlantEqual;":[10877,824],"NotLessTilde;":8820,"NotNestedGreaterGreater;":[10914,824],"NotNestedLessLess;":[10913,824],"NotPrecedes;":8832,"NotPrecedesEqual;":[10927,824],"NotPrecedesSlantEqual;":8928,"NotReverseElement;":8716,"NotRightTriangle;":8939,"NotRightTriangleBar;":[10704,824],"NotRightTriangleEqual;":8941,"NotSquareSubset;":[8847,824],"NotSquareSubsetEqual;":8930,"NotSquareSuperset;":[8848,824],"NotSquareSupersetEqual;":8931,"NotSubset;":[8834,8402],"NotSubsetEqual;":8840,"NotSucceeds;":8833,"NotSucceedsEqual;":[10928,824],"NotSucceedsSlantEqual;":8929,"NotSucceedsTilde;":[8831,824],"NotSuperset;":[8835,8402],"NotSupersetEqual;":8841,"NotTilde;":8769,"NotTildeEqual;":8772,"NotTildeFullEqual;":8775,"NotTildeTilde;":8777,"NotVerticalBar;":8740,"Nscr;":[55349,56489],Ntilde:209,"Ntilde;":209,"Nu;":925,"OElig;":338,Oacute:211,"Oacute;":211,Ocirc:212,"Ocirc;":212,"Ocy;":1054,"Odblac;":336,"Ofr;":[55349,56594],Ograve:210,"Ograve;":210,"Omacr;":332,"Omega;":937,"Omicron;":927,"Oopf;":[55349,56646],"OpenCurlyDoubleQuote;":8220,"OpenCurlyQuote;":8216,"Or;":10836,"Oscr;":[55349,56490],Oslash:216,"Oslash;":216,Otilde:213,"Otilde;":213,"Otimes;":10807,Ouml:214,"Ouml;":214,"OverBar;":8254,"OverBrace;":9182,"OverBracket;":9140,"OverParenthesis;":9180,"PartialD;":8706,"Pcy;":1055,"Pfr;":[55349,56595],"Phi;":934,"Pi;":928,"PlusMinus;":177,"Poincareplane;":8460,"Popf;":8473,"Pr;":10939,"Precedes;":8826,"PrecedesEqual;":10927,"PrecedesSlantEqual;":8828,"PrecedesTilde;":8830,"Prime;":8243,"Product;":8719,"Proportion;":8759,"Proportional;":8733,"Pscr;":[55349,56491],"Psi;":936,QUOT:34,"QUOT;":34,"Qfr;":[55349,56596],"Qopf;":8474,"Qscr;":[55349,56492],"RBarr;":10512,REG:174,"REG;":174,"Racute;":340,"Rang;":10219,"Rarr;":8608,"Rarrtl;":10518,"Rcaron;":344,"Rcedil;":342,"Rcy;":1056,"Re;":8476,"ReverseElement;":8715,"ReverseEquilibrium;":8651,"ReverseUpEquilibrium;":10607,"Rfr;":8476,"Rho;":929,"RightAngleBracket;":10217,"RightArrow;":8594,"RightArrowBar;":8677,"RightArrowLeftArrow;":8644,"RightCeiling;":8969,"RightDoubleBracket;":10215,"RightDownTeeVector;":10589,"RightDownVector;":8642,"RightDownVectorBar;":10581,"RightFloor;":8971,"RightTee;":8866,"RightTeeArrow;":8614,"RightTeeVector;":10587,"RightTriangle;":8883,"RightTriangleBar;":10704,"RightTriangleEqual;":8885,"RightUpDownVector;":10575,"RightUpTeeVector;":10588,"RightUpVector;":8638,"RightUpVectorBar;":10580,"RightVector;":8640,"RightVectorBar;":10579,"Rightarrow;":8658,"Ropf;":8477,"RoundImplies;":10608,"Rrightarrow;":8667,"Rscr;":8475,"Rsh;":8625,"RuleDelayed;":10740,"SHCHcy;":1065,"SHcy;":1064,"SOFTcy;":1068,"Sacute;":346,"Sc;":10940,"Scaron;":352,"Scedil;":350,"Scirc;":348,"Scy;":1057,"Sfr;":[55349,56598],"ShortDownArrow;":8595,"ShortLeftArrow;":8592,"ShortRightArrow;":8594,"ShortUpArrow;":8593,"Sigma;":931,"SmallCircle;":8728,"Sopf;":[55349,56650],"Sqrt;":8730,"Square;":9633,"SquareIntersection;":8851,"SquareSubset;":8847,"SquareSubsetEqual;":8849,"SquareSuperset;":8848,"SquareSupersetEqual;":8850,"SquareUnion;":8852,"Sscr;":[55349,56494],"Star;":8902,"Sub;":8912,"Subset;":8912,"SubsetEqual;":8838,"Succeeds;":8827,"SucceedsEqual;":10928,"SucceedsSlantEqual;":8829,"SucceedsTilde;":8831,"SuchThat;":8715,"Sum;":8721,"Sup;":8913,"Superset;":8835,"SupersetEqual;":8839,"Supset;":8913,THORN:222,"THORN;":222,"TRADE;":8482,"TSHcy;":1035,"TScy;":1062,"Tab;":9,"Tau;":932,"Tcaron;":356,"Tcedil;":354,"Tcy;":1058,"Tfr;":[55349,56599],"Therefore;":8756,"Theta;":920,"ThickSpace;":[8287,8202],"ThinSpace;":8201,"Tilde;":8764,"TildeEqual;":8771,"TildeFullEqual;":8773,"TildeTilde;":8776,"Topf;":[55349,56651],"TripleDot;":8411,"Tscr;":[55349,56495],"Tstrok;":358,Uacute:218,"Uacute;":218,"Uarr;":8607,"Uarrocir;":10569,"Ubrcy;":1038,"Ubreve;":364,Ucirc:219,"Ucirc;":219,"Ucy;":1059,"Udblac;":368,"Ufr;":[55349,56600],Ugrave:217,"Ugrave;":217,"Umacr;":362,"UnderBar;":95,"UnderBrace;":9183,"UnderBracket;":9141,"UnderParenthesis;":9181,"Union;":8899,"UnionPlus;":8846,"Uogon;":370,"Uopf;":[55349,56652],"UpArrow;":8593,"UpArrowBar;":10514,"UpArrowDownArrow;":8645,"UpDownArrow;":8597,"UpEquilibrium;":10606,"UpTee;":8869,"UpTeeArrow;":8613,"Uparrow;":8657,"Updownarrow;":8661,"UpperLeftArrow;":8598,"UpperRightArrow;":8599,"Upsi;":978,"Upsilon;":933,"Uring;":366,"Uscr;":[55349,56496],"Utilde;":360,Uuml:220,"Uuml;":220,"VDash;":8875,"Vbar;":10987,"Vcy;":1042,"Vdash;":8873,"Vdashl;":10982,"Vee;":8897,"Verbar;":8214,"Vert;":8214,"VerticalBar;":8739,"VerticalLine;":124,"VerticalSeparator;":10072,"VerticalTilde;":8768,"VeryThinSpace;":8202,"Vfr;":[55349,56601],"Vopf;":[55349,56653],"Vscr;":[55349,56497],"Vvdash;":8874,"Wcirc;":372,"Wedge;":8896,"Wfr;":[55349,56602],"Wopf;":[55349,56654],"Wscr;":[55349,56498],"Xfr;":[55349,56603],"Xi;":926,"Xopf;":[55349,56655],"Xscr;":[55349,56499],"YAcy;":1071,"YIcy;":1031,"YUcy;":1070,Yacute:221,"Yacute;":221,"Ycirc;":374,"Ycy;":1067,"Yfr;":[55349,56604],"Yopf;":[55349,56656],"Yscr;":[55349,56500],"Yuml;":376,"ZHcy;":1046,"Zacute;":377,"Zcaron;":381,"Zcy;":1047,"Zdot;":379,"ZeroWidthSpace;":8203,"Zeta;":918,"Zfr;":8488,"Zopf;":8484,"Zscr;":[55349,56501],aacute:225,"aacute;":225,"abreve;":259,"ac;":8766,"acE;":[8766,819],"acd;":8767,acirc:226,"acirc;":226,acute:180,"acute;":180,"acy;":1072,aelig:230,"aelig;":230,"af;":8289,"afr;":[55349,56606],agrave:224,"agrave;":224,"alefsym;":8501,"aleph;":8501,"alpha;":945,"amacr;":257,"amalg;":10815,amp:38,"amp;":38,"and;":8743,"andand;":10837,"andd;":10844,"andslope;":10840,"andv;":10842,"ang;":8736,"ange;":10660,"angle;":8736,"angmsd;":8737,"angmsdaa;":10664,"angmsdab;":10665,"angmsdac;":10666,"angmsdad;":10667,"angmsdae;":10668,"angmsdaf;":10669,"angmsdag;":10670,"angmsdah;":10671,"angrt;":8735,"angrtvb;":8894,"angrtvbd;":10653,"angsph;":8738,"angst;":197,"angzarr;":9084,"aogon;":261,"aopf;":[55349,56658],"ap;":8776,"apE;":10864,"apacir;":10863,"ape;":8778,"apid;":8779,"apos;":39,"approx;":8776,"approxeq;":8778,aring:229,"aring;":229,"ascr;":[55349,56502],"ast;":42,"asymp;":8776,"asympeq;":8781,atilde:227,"atilde;":227,auml:228,"auml;":228,"awconint;":8755,"awint;":10769,"bNot;":10989,"backcong;":8780,"backepsilon;":1014,"backprime;":8245,"backsim;":8765,"backsimeq;":8909,"barvee;":8893,"barwed;":8965,"barwedge;":8965,"bbrk;":9141,"bbrktbrk;":9142,"bcong;":8780,"bcy;":1073,"bdquo;":8222,"becaus;":8757,"because;":8757,"bemptyv;":10672,"bepsi;":1014,"bernou;":8492,"beta;":946,"beth;":8502,"between;":8812,"bfr;":[55349,56607],"bigcap;":8898,"bigcirc;":9711,"bigcup;":8899,"bigodot;":10752,"bigoplus;":10753,"bigotimes;":10754,"bigsqcup;":10758,"bigstar;":9733,"bigtriangledown;":9661,"bigtriangleup;":9651,"biguplus;":10756,"bigvee;":8897,"bigwedge;":8896,"bkarow;":10509,"blacklozenge;":10731,"blacksquare;":9642,"blacktriangle;":9652,"blacktriangledown;":9662,"blacktriangleleft;":9666,"blacktriangleright;":9656,"blank;":9251,"blk12;":9618,"blk14;":9617,"blk34;":9619,"block;":9608,"bne;":[61,8421],"bnequiv;":[8801,8421],"bnot;":8976,"bopf;":[55349,56659],"bot;":8869,"bottom;":8869,"bowtie;":8904,"boxDL;":9559,"boxDR;":9556,"boxDl;":9558,"boxDr;":9555,"boxH;":9552,"boxHD;":9574,"boxHU;":9577,"boxHd;":9572,"boxHu;":9575,"boxUL;":9565,"boxUR;":9562,"boxUl;":9564,"boxUr;":9561,"boxV;":9553,"boxVH;":9580,"boxVL;":9571,"boxVR;":9568,"boxVh;":9579,"boxVl;":9570,"boxVr;":9567,"boxbox;":10697,"boxdL;":9557,"boxdR;":9554,"boxdl;":9488,"boxdr;":9484,"boxh;":9472,"boxhD;":9573,"boxhU;":9576,"boxhd;":9516,"boxhu;":9524,"boxminus;":8863,"boxplus;":8862,"boxtimes;":8864,"boxuL;":9563,"boxuR;":9560,"boxul;":9496,"boxur;":9492,"boxv;":9474,"boxvH;":9578,"boxvL;":9569,"boxvR;":9566,"boxvh;":9532,"boxvl;":9508,"boxvr;":9500,"bprime;":8245,"breve;":728,brvbar:166,"brvbar;":166,"bscr;":[55349,56503],"bsemi;":8271,"bsim;":8765,"bsime;":8909,"bsol;":92,"bsolb;":10693,"bsolhsub;":10184,"bull;":8226,"bullet;":8226,"bump;":8782,"bumpE;":10926,"bumpe;":8783,"bumpeq;":8783,"cacute;":263,"cap;":8745,"capand;":10820,"capbrcup;":10825,"capcap;":10827,"capcup;":10823,"capdot;":10816,"caps;":[8745,65024],"caret;":8257,"caron;":711,"ccaps;":10829,"ccaron;":269,ccedil:231,"ccedil;":231,"ccirc;":265,"ccups;":10828,"ccupssm;":10832,"cdot;":267,cedil:184,"cedil;":184,"cemptyv;":10674,cent:162,"cent;":162,"centerdot;":183,"cfr;":[55349,56608],"chcy;":1095,"check;":10003,"checkmark;":10003,"chi;":967,"cir;":9675,"cirE;":10691,"circ;":710,"circeq;":8791,"circlearrowleft;":8634,"circlearrowright;":8635,"circledR;":174,"circledS;":9416,"circledast;":8859,"circledcirc;":8858,"circleddash;":8861,"cire;":8791,"cirfnint;":10768,"cirmid;":10991,"cirscir;":10690,"clubs;":9827,"clubsuit;":9827,"colon;":58,"colone;":8788,"coloneq;":8788,"comma;":44,"commat;":64,"comp;":8705,"compfn;":8728,"complement;":8705,"complexes;":8450,"cong;":8773,"congdot;":10861,"conint;":8750,"copf;":[55349,56660],"coprod;":8720,copy:169,"copy;":169,"copysr;":8471,"crarr;":8629,"cross;":10007,"cscr;":[55349,56504],"csub;":10959,"csube;":10961,"csup;":10960,"csupe;":10962,"ctdot;":8943,"cudarrl;":10552,"cudarrr;":10549,"cuepr;":8926,"cuesc;":8927,"cularr;":8630,"cularrp;":10557,"cup;":8746,"cupbrcap;":10824,"cupcap;":10822,"cupcup;":10826,"cupdot;":8845,"cupor;":10821,"cups;":[8746,65024],"curarr;":8631,"curarrm;":10556,"curlyeqprec;":8926,"curlyeqsucc;":8927,"curlyvee;":8910,"curlywedge;":8911,curren:164,"curren;":164,"curvearrowleft;":8630,"curvearrowright;":8631,"cuvee;":8910,"cuwed;":8911,"cwconint;":8754,"cwint;":8753,"cylcty;":9005,"dArr;":8659,"dHar;":10597,"dagger;":8224,"daleth;":8504,"darr;":8595,"dash;":8208,"dashv;":8867,"dbkarow;":10511,"dblac;":733,"dcaron;":271,"dcy;":1076,"dd;":8518,"ddagger;":8225,"ddarr;":8650,"ddotseq;":10871,deg:176,"deg;":176,"delta;":948,"demptyv;":10673,"dfisht;":10623,"dfr;":[55349,56609],"dharl;":8643,"dharr;":8642,"diam;":8900,"diamond;":8900,"diamondsuit;":9830,"diams;":9830,"die;":168,"digamma;":989,"disin;":8946,"div;":247,divide:247,"divide;":247,"divideontimes;":8903,"divonx;":8903,"djcy;":1106,"dlcorn;":8990,"dlcrop;":8973,"dollar;":36,"dopf;":[55349,56661],"dot;":729,"doteq;":8784,"doteqdot;":8785,"dotminus;":8760,"dotplus;":8724,"dotsquare;":8865,"doublebarwedge;":8966,"downarrow;":8595,"downdownarrows;":8650,"downharpoonleft;":8643,"downharpoonright;":8642,"drbkarow;":10512,"drcorn;":8991,"drcrop;":8972,"dscr;":[55349,56505],"dscy;":1109,"dsol;":10742,"dstrok;":273,"dtdot;":8945,"dtri;":9663,"dtrif;":9662,"duarr;":8693,"duhar;":10607,"dwangle;":10662,"dzcy;":1119,"dzigrarr;":10239,"eDDot;":10871,"eDot;":8785,eacute:233,"eacute;":233,"easter;":10862,"ecaron;":283,"ecir;":8790,ecirc:234,"ecirc;":234,"ecolon;":8789,"ecy;":1101,"edot;":279,"ee;":8519,"efDot;":8786,"efr;":[55349,56610],"eg;":10906,egrave:232,"egrave;":232,"egs;":10902,"egsdot;":10904,"el;":10905,"elinters;":9191,"ell;":8467,"els;":10901,"elsdot;":10903,"emacr;":275,"empty;":8709,"emptyset;":8709,"emptyv;":8709,"emsp13;":8196,"emsp14;":8197,"emsp;":8195,"eng;":331,"ensp;":8194,"eogon;":281,"eopf;":[55349,56662],"epar;":8917,"eparsl;":10723,"eplus;":10865,"epsi;":949,"epsilon;":949,"epsiv;":1013,"eqcirc;":8790,"eqcolon;":8789,"eqsim;":8770,"eqslantgtr;":10902,"eqslantless;":10901,"equals;":61,"equest;":8799,"equiv;":8801,"equivDD;":10872,"eqvparsl;":10725,"erDot;":8787,"erarr;":10609,"escr;":8495,"esdot;":8784,"esim;":8770,"eta;":951,eth:240,"eth;":240,euml:235,"euml;":235,"euro;":8364,"excl;":33,"exist;":8707,"expectation;":8496,"exponentiale;":8519,"fallingdotseq;":8786,"fcy;":1092,"female;":9792,"ffilig;":64259,"fflig;":64256,"ffllig;":64260,"ffr;":[55349,56611],"filig;":64257,"fjlig;":[102,106],"flat;":9837,"fllig;":64258,"fltns;":9649,"fnof;":402,"fopf;":[55349,56663],"forall;":8704,"fork;":8916,"forkv;":10969,"fpartint;":10765,frac12:189,"frac12;":189,"frac13;":8531,frac14:188,"frac14;":188,"frac15;":8533,"frac16;":8537,"frac18;":8539,"frac23;":8532,"frac25;":8534,frac34:190,"frac34;":190,"frac35;":8535,"frac38;":8540,"frac45;":8536,"frac56;":8538,"frac58;":8541,"frac78;":8542,"frasl;":8260,"frown;":8994,"fscr;":[55349,56507],"gE;":8807,"gEl;":10892,"gacute;":501,"gamma;":947,"gammad;":989,"gap;":10886,"gbreve;":287,"gcirc;":285,"gcy;":1075,"gdot;":289,"ge;":8805,"gel;":8923,"geq;":8805,"geqq;":8807,"geqslant;":10878,"ges;":10878,"gescc;":10921,"gesdot;":10880,"gesdoto;":10882,"gesdotol;":10884,"gesl;":[8923,65024],"gesles;":10900,"gfr;":[55349,56612],"gg;":8811,"ggg;":8921,"gimel;":8503,"gjcy;":1107,"gl;":8823,"glE;":10898,"gla;":10917,"glj;":10916,"gnE;":8809,"gnap;":10890,"gnapprox;":10890,"gne;":10888,"gneq;":10888,"gneqq;":8809,"gnsim;":8935,"gopf;":[55349,56664],"grave;":96,"gscr;":8458,"gsim;":8819,"gsime;":10894,"gsiml;":10896,gt:62,"gt;":62,"gtcc;":10919,"gtcir;":10874,"gtdot;":8919,"gtlPar;":10645,"gtquest;":10876,"gtrapprox;":10886,"gtrarr;":10616,"gtrdot;":8919,"gtreqless;":8923,"gtreqqless;":10892,"gtrless;":8823,"gtrsim;":8819,"gvertneqq;":[8809,65024],"gvnE;":[8809,65024],"hArr;":8660,"hairsp;":8202,"half;":189,"hamilt;":8459,"hardcy;":1098,"harr;":8596,"harrcir;":10568,"harrw;":8621,"hbar;":8463,"hcirc;":293,"hearts;":9829,"heartsuit;":9829,"hellip;":8230,"hercon;":8889,"hfr;":[55349,56613],"hksearow;":10533,"hkswarow;":10534,"hoarr;":8703,"homtht;":8763,"hookleftarrow;":8617,"hookrightarrow;":8618,"hopf;":[55349,56665],"horbar;":8213,"hscr;":[55349,56509],"hslash;":8463,"hstrok;":295,"hybull;":8259,"hyphen;":8208,iacute:237,"iacute;":237,"ic;":8291,icirc:238,"icirc;":238,"icy;":1080,"iecy;":1077,iexcl:161,"iexcl;":161,"iff;":8660,"ifr;":[55349,56614],igrave:236,"igrave;":236,"ii;":8520,"iiiint;":10764,"iiint;":8749,"iinfin;":10716,"iiota;":8489,"ijlig;":307,"imacr;":299,"image;":8465,"imagline;":8464,"imagpart;":8465,"imath;":305,"imof;":8887,"imped;":437,"in;":8712,"incare;":8453,"infin;":8734,"infintie;":10717,"inodot;":305,"int;":8747,"intcal;":8890,"integers;":8484,"intercal;":8890,"intlarhk;":10775,"intprod;":10812,"iocy;":1105,"iogon;":303,"iopf;":[55349,56666],"iota;":953,"iprod;":10812,iquest:191,"iquest;":191,"iscr;":[55349,56510],"isin;":8712,"isinE;":8953,"isindot;":8949,"isins;":8948,"isinsv;":8947,"isinv;":8712,"it;":8290,"itilde;":297,"iukcy;":1110,iuml:239,"iuml;":239,"jcirc;":309,"jcy;":1081,"jfr;":[55349,56615],"jmath;":567,"jopf;":[55349,56667],"jscr;":[55349,56511],"jsercy;":1112,"jukcy;":1108,"kappa;":954,"kappav;":1008,"kcedil;":311,"kcy;":1082,"kfr;":[55349,56616],"kgreen;":312,"khcy;":1093,"kjcy;":1116,"kopf;":[55349,56668],"kscr;":[55349,56512],"lAarr;":8666,"lArr;":8656,"lAtail;":10523,"lBarr;":10510,"lE;":8806,"lEg;":10891,"lHar;":10594,"lacute;":314,"laemptyv;":10676,"lagran;":8466,"lambda;":955,"lang;":10216,"langd;":10641,"langle;":10216,"lap;":10885,laquo:171,"laquo;":171,"larr;":8592,"larrb;":8676,"larrbfs;":10527,"larrfs;":10525,"larrhk;":8617,"larrlp;":8619,"larrpl;":10553,"larrsim;":10611,"larrtl;":8610,"lat;":10923,"latail;":10521,"late;":10925,"lates;":[10925,65024],"lbarr;":10508,"lbbrk;":10098,"lbrace;":123,"lbrack;":91,"lbrke;":10635,"lbrksld;":10639,"lbrkslu;":10637,"lcaron;":318,"lcedil;":316,"lceil;":8968,"lcub;":123,"lcy;":1083,"ldca;":10550,"ldquo;":8220,"ldquor;":8222,"ldrdhar;":10599,"ldrushar;":10571,"ldsh;":8626,"le;":8804,"leftarrow;":8592,"leftarrowtail;":8610,"leftharpoondown;":8637,"leftharpoonup;":8636,"leftleftarrows;":8647,"leftrightarrow;":8596,"leftrightarrows;":8646,"leftrightharpoons;":8651,"leftrightsquigarrow;":8621,"leftthreetimes;":8907,"leg;":8922,"leq;":8804,"leqq;":8806,"leqslant;":10877,"les;":10877,"lescc;":10920,"lesdot;":10879,"lesdoto;":10881,"lesdotor;":10883,"lesg;":[8922,65024],"lesges;":10899,"lessapprox;":10885,"lessdot;":8918,"lesseqgtr;":8922,"lesseqqgtr;":10891,"lessgtr;":8822,"lesssim;":8818,"lfisht;":10620,"lfloor;":8970,"lfr;":[55349,56617],"lg;":8822,"lgE;":10897,"lhard;":8637,"lharu;":8636,"lharul;":10602,"lhblk;":9604,"ljcy;":1113,"ll;":8810,"llarr;":8647,"llcorner;":8990,"llhard;":10603,"lltri;":9722,"lmidot;":320,"lmoust;":9136,"lmoustache;":9136,"lnE;":8808,"lnap;":10889,"lnapprox;":10889,"lne;":10887,"lneq;":10887,"lneqq;":8808,"lnsim;":8934,"loang;":10220,"loarr;":8701,"lobrk;":10214,"longleftarrow;":10229,"longleftrightarrow;":10231,"longmapsto;":10236,"longrightarrow;":10230,"looparrowleft;":8619,"looparrowright;":8620,"lopar;":10629,"lopf;":[55349,56669],"loplus;":10797,"lotimes;":10804,"lowast;":8727,"lowbar;":95,"loz;":9674,"lozenge;":9674,"lozf;":10731,"lpar;":40,"lparlt;":10643,"lrarr;":8646,"lrcorner;":8991,"lrhar;":8651,"lrhard;":10605,"lrm;":8206,"lrtri;":8895,"lsaquo;":8249,"lscr;":[55349,56513],"lsh;":8624,"lsim;":8818,"lsime;":10893,"lsimg;":10895,"lsqb;":91,"lsquo;":8216,"lsquor;":8218,"lstrok;":322,lt:60,"lt;":60,"ltcc;":10918,"ltcir;":10873,"ltdot;":8918,"lthree;":8907,"ltimes;":8905,"ltlarr;":10614,"ltquest;":10875,"ltrPar;":10646,"ltri;":9667,"ltrie;":8884,"ltrif;":9666,"lurdshar;":10570,"luruhar;":10598,"lvertneqq;":[8808,65024],"lvnE;":[8808,65024],"mDDot;":8762,macr:175,"macr;":175,"male;":9794,"malt;":10016,"maltese;":10016,"map;":8614,"mapsto;":8614,"mapstodown;":8615,"mapstoleft;":8612,"mapstoup;":8613,"marker;":9646,"mcomma;":10793,"mcy;":1084,"mdash;":8212,"measuredangle;":8737,"mfr;":[55349,56618],"mho;":8487,micro:181,"micro;":181,"mid;":8739,"midast;":42,"midcir;":10992,middot:183,"middot;":183,"minus;":8722,"minusb;":8863,"minusd;":8760,"minusdu;":10794,"mlcp;":10971,"mldr;":8230,"mnplus;":8723,"models;":8871,"mopf;":[55349,56670],"mp;":8723,"mscr;":[55349,56514],"mstpos;":8766,"mu;":956,"multimap;":8888,"mumap;":8888,"nGg;":[8921,824],"nGt;":[8811,8402],"nGtv;":[8811,824],"nLeftarrow;":8653,"nLeftrightarrow;":8654,"nLl;":[8920,824],"nLt;":[8810,8402],"nLtv;":[8810,824],"nRightarrow;":8655,"nVDash;":8879,"nVdash;":8878,"nabla;":8711,"nacute;":324,"nang;":[8736,8402],"nap;":8777,"napE;":[10864,824],"napid;":[8779,824],"napos;":329,"napprox;":8777,"natur;":9838,"natural;":9838,"naturals;":8469,nbsp:160,"nbsp;":160,"nbump;":[8782,824],"nbumpe;":[8783,824],"ncap;":10819,"ncaron;":328,"ncedil;":326,"ncong;":8775,"ncongdot;":[10861,824],"ncup;":10818,"ncy;":1085,"ndash;":8211,"ne;":8800,"neArr;":8663,"nearhk;":10532,"nearr;":8599,"nearrow;":8599,"nedot;":[8784,824],"nequiv;":8802,"nesear;":10536,"nesim;":[8770,824],"nexist;":8708,"nexists;":8708,"nfr;":[55349,56619],"ngE;":[8807,824],"nge;":8817,"ngeq;":8817,"ngeqq;":[8807,824],"ngeqslant;":[10878,824],"nges;":[10878,824],"ngsim;":8821,"ngt;":8815,"ngtr;":8815,"nhArr;":8654,"nharr;":8622,"nhpar;":10994,"ni;":8715,"nis;":8956,"nisd;":8954,"niv;":8715,"njcy;":1114,"nlArr;":8653,"nlE;":[8806,824],"nlarr;":8602,"nldr;":8229,"nle;":8816,"nleftarrow;":8602,"nleftrightarrow;":8622,"nleq;":8816,"nleqq;":[8806,824],"nleqslant;":[10877,824],"nles;":[10877,824],"nless;":8814,"nlsim;":8820,"nlt;":8814,"nltri;":8938,"nltrie;":8940,"nmid;":8740,"nopf;":[55349,56671],not:172,"not;":172,"notin;":8713,"notinE;":[8953,824],"notindot;":[8949,824],"notinva;":8713,"notinvb;":8951,"notinvc;":8950,"notni;":8716,"notniva;":8716,"notnivb;":8958,"notnivc;":8957,"npar;":8742,"nparallel;":8742,"nparsl;":[11005,8421],"npart;":[8706,824],"npolint;":10772,"npr;":8832,"nprcue;":8928,"npre;":[10927,824],"nprec;":8832,"npreceq;":[10927,824],"nrArr;":8655,"nrarr;":8603,"nrarrc;":[10547,824],"nrarrw;":[8605,824],"nrightarrow;":8603,"nrtri;":8939,"nrtrie;":8941,"nsc;":8833,"nsccue;":8929,"nsce;":[10928,824],"nscr;":[55349,56515],"nshortmid;":8740,"nshortparallel;":8742,"nsim;":8769,"nsime;":8772,"nsimeq;":8772,"nsmid;":8740,"nspar;":8742,"nsqsube;":8930,"nsqsupe;":8931,"nsub;":8836,"nsubE;":[10949,824],"nsube;":8840,"nsubset;":[8834,8402],"nsubseteq;":8840,"nsubseteqq;":[10949,824],"nsucc;":8833,"nsucceq;":[10928,824],"nsup;":8837,"nsupE;":[10950,824],"nsupe;":8841,"nsupset;":[8835,8402],"nsupseteq;":8841,"nsupseteqq;":[10950,824],"ntgl;":8825,ntilde:241,"ntilde;":241,"ntlg;":8824,"ntriangleleft;":8938,"ntrianglelefteq;":8940,"ntriangleright;":8939,"ntrianglerighteq;":8941,"nu;":957,"num;":35,"numero;":8470,"numsp;":8199,"nvDash;":8877,"nvHarr;":10500,"nvap;":[8781,8402],"nvdash;":8876,"nvge;":[8805,8402],"nvgt;":[62,8402],"nvinfin;":10718,"nvlArr;":10498,"nvle;":[8804,8402],"nvlt;":[60,8402],"nvltrie;":[8884,8402],"nvrArr;":10499,"nvrtrie;":[8885,8402],"nvsim;":[8764,8402],"nwArr;":8662,"nwarhk;":10531,"nwarr;":8598,"nwarrow;":8598,"nwnear;":10535,"oS;":9416,oacute:243,"oacute;":243,"oast;":8859,"ocir;":8858,ocirc:244,"ocirc;":244,"ocy;":1086,"odash;":8861,"odblac;":337,"odiv;":10808,"odot;":8857,"odsold;":10684,"oelig;":339,"ofcir;":10687,"ofr;":[55349,56620],"ogon;":731,ograve:242,"ograve;":242,"ogt;":10689,"ohbar;":10677,"ohm;":937,"oint;":8750,"olarr;":8634,"olcir;":10686,"olcross;":10683,"oline;":8254,"olt;":10688,"omacr;":333,"omega;":969,"omicron;":959,"omid;":10678,"ominus;":8854,"oopf;":[55349,56672],"opar;":10679,"operp;":10681,"oplus;":8853,"or;":8744,"orarr;":8635,"ord;":10845,"order;":8500,"orderof;":8500,ordf:170,"ordf;":170,ordm:186,"ordm;":186,"origof;":8886,"oror;":10838,"orslope;":10839,"orv;":10843,"oscr;":8500,oslash:248,"oslash;":248,"osol;":8856,otilde:245,"otilde;":245,"otimes;":8855,"otimesas;":10806,ouml:246,"ouml;":246,"ovbar;":9021,"par;":8741,para:182,"para;":182,"parallel;":8741,"parsim;":10995,"parsl;":11005,"part;":8706,"pcy;":1087,"percnt;":37,"period;":46,"permil;":8240,"perp;":8869,"pertenk;":8241,"pfr;":[55349,56621],"phi;":966,"phiv;":981,"phmmat;":8499,"phone;":9742,"pi;":960,"pitchfork;":8916,"piv;":982,"planck;":8463,"planckh;":8462,"plankv;":8463,"plus;":43,"plusacir;":10787,"plusb;":8862,"pluscir;":10786,"plusdo;":8724,"plusdu;":10789,"pluse;":10866,plusmn:177,"plusmn;":177,"plussim;":10790,"plustwo;":10791,"pm;":177,"pointint;":10773,"popf;":[55349,56673],pound:163,"pound;":163,"pr;":8826,"prE;":10931,"prap;":10935,"prcue;":8828,"pre;":10927,"prec;":8826,"precapprox;":10935,"preccurlyeq;":8828,"preceq;":10927,"precnapprox;":10937,"precneqq;":10933,"precnsim;":8936,"precsim;":8830,"prime;":8242,"primes;":8473,"prnE;":10933,"prnap;":10937,"prnsim;":8936,"prod;":8719,"profalar;":9006,"profline;":8978,"profsurf;":8979,"prop;":8733,"propto;":8733,"prsim;":8830,"prurel;":8880,"pscr;":[55349,56517],"psi;":968,"puncsp;":8200,"qfr;":[55349,56622],"qint;":10764,"qopf;":[55349,56674],"qprime;":8279,"qscr;":[55349,56518],"quaternions;":8461,"quatint;":10774,"quest;":63,"questeq;":8799,quot:34,"quot;":34,"rAarr;":8667,"rArr;":8658,"rAtail;":10524,"rBarr;":10511,"rHar;":10596,"race;":[8765,817],"racute;":341,"radic;":8730,"raemptyv;":10675,"rang;":10217,"rangd;":10642,"range;":10661,"rangle;":10217,raquo:187,"raquo;":187,"rarr;":8594,"rarrap;":10613,"rarrb;":8677,"rarrbfs;":10528,"rarrc;":10547,"rarrfs;":10526,"rarrhk;":8618,"rarrlp;":8620,"rarrpl;":10565,"rarrsim;":10612,"rarrtl;":8611,"rarrw;":8605,"ratail;":10522,"ratio;":8758,"rationals;":8474,"rbarr;":10509,"rbbrk;":10099,"rbrace;":125,"rbrack;":93,"rbrke;":10636,"rbrksld;":10638,"rbrkslu;":10640,"rcaron;":345,"rcedil;":343,"rceil;":8969,"rcub;":125,"rcy;":1088,"rdca;":10551,"rdldhar;":10601,"rdquo;":8221,"rdquor;":8221,"rdsh;":8627,"real;":8476,"realine;":8475,"realpart;":8476,"reals;":8477,"rect;":9645,reg:174,"reg;":174,"rfisht;":10621,"rfloor;":8971,"rfr;":[55349,56623],"rhard;":8641,"rharu;":8640,"rharul;":10604,"rho;":961,"rhov;":1009,"rightarrow;":8594,"rightarrowtail;":8611,"rightharpoondown;":8641,"rightharpoonup;":8640,"rightleftarrows;":8644,"rightleftharpoons;":8652,"rightrightarrows;":8649,"rightsquigarrow;":8605,"rightthreetimes;":8908,"ring;":730,"risingdotseq;":8787,"rlarr;":8644,"rlhar;":8652,"rlm;":8207,"rmoust;":9137,"rmoustache;":9137,"rnmid;":10990,"roang;":10221,"roarr;":8702,"robrk;":10215,"ropar;":10630,"ropf;":[55349,56675],"roplus;":10798,"rotimes;":10805,"rpar;":41,"rpargt;":10644,"rppolint;":10770,"rrarr;":8649,"rsaquo;":8250,"rscr;":[55349,56519],"rsh;":8625,"rsqb;":93,"rsquo;":8217,"rsquor;":8217,"rthree;":8908,"rtimes;":8906,"rtri;":9657,"rtrie;":8885,"rtrif;":9656,"rtriltri;":10702,"ruluhar;":10600,"rx;":8478,"sacute;":347,"sbquo;":8218,"sc;":8827,"scE;":10932,"scap;":10936,"scaron;":353,"sccue;":8829,"sce;":10928,"scedil;":351,"scirc;":349,"scnE;":10934,"scnap;":10938,"scnsim;":8937,"scpolint;":10771,"scsim;":8831,"scy;":1089,"sdot;":8901,"sdotb;":8865,"sdote;":10854,"seArr;":8664,"searhk;":10533,"searr;":8600,"searrow;":8600,sect:167,"sect;":167,"semi;":59,"seswar;":10537,"setminus;":8726,"setmn;":8726,"sext;":10038,"sfr;":[55349,56624],"sfrown;":8994,"sharp;":9839,"shchcy;":1097,"shcy;":1096,"shortmid;":8739,"shortparallel;":8741,shy:173,"shy;":173,"sigma;":963,"sigmaf;":962,"sigmav;":962,"sim;":8764,"simdot;":10858,"sime;":8771,"simeq;":8771,"simg;":10910,"simgE;":10912,"siml;":10909,"simlE;":10911,"simne;":8774,"simplus;":10788,"simrarr;":10610,"slarr;":8592,"smallsetminus;":8726,"smashp;":10803,"smeparsl;":10724,"smid;":8739,"smile;":8995,"smt;":10922,"smte;":10924,"smtes;":[10924,65024],"softcy;":1100,"sol;":47,"solb;":10692,"solbar;":9023,"sopf;":[55349,56676],"spades;":9824,"spadesuit;":9824,"spar;":8741,"sqcap;":8851,"sqcaps;":[8851,65024],"sqcup;":8852,"sqcups;":[8852,65024],"sqsub;":8847,"sqsube;":8849,"sqsubset;":8847,"sqsubseteq;":8849,"sqsup;":8848,"sqsupe;":8850,"sqsupset;":8848,"sqsupseteq;":8850,"squ;":9633,"square;":9633,"squarf;":9642,"squf;":9642,"srarr;":8594,"sscr;":[55349,56520],"ssetmn;":8726,"ssmile;":8995,"sstarf;":8902,"star;":9734,"starf;":9733,"straightepsilon;":1013,"straightphi;":981,"strns;":175,"sub;":8834,"subE;":10949,"subdot;":10941,"sube;":8838,"subedot;":10947,"submult;":10945,"subnE;":10955,"subne;":8842,"subplus;":10943,"subrarr;":10617,"subset;":8834,"subseteq;":8838,"subseteqq;":10949,"subsetneq;":8842,"subsetneqq;":10955,"subsim;":10951,"subsub;":10965,"subsup;":10963,"succ;":8827,"succapprox;":10936,"succcurlyeq;":8829,"succeq;":10928,"succnapprox;":10938,"succneqq;":10934,"succnsim;":8937,"succsim;":8831,"sum;":8721,"sung;":9834,sup1:185,"sup1;":185,sup2:178,"sup2;":178,sup3:179,"sup3;":179,"sup;":8835,"supE;":10950,"supdot;":10942,"supdsub;":10968,"supe;":8839,"supedot;":10948,"suphsol;":10185,"suphsub;":10967,"suplarr;":10619,"supmult;":10946,"supnE;":10956,"supne;":8843,"supplus;":10944,"supset;":8835,"supseteq;":8839,"supseteqq;":10950,"supsetneq;":8843,"supsetneqq;":10956,"supsim;":10952,"supsub;":10964,"supsup;":10966,"swArr;":8665,"swarhk;":10534,"swarr;":8601,"swarrow;":8601,"swnwar;":10538,szlig:223,"szlig;":223,"target;":8982,"tau;":964,"tbrk;":9140,"tcaron;":357,"tcedil;":355,"tcy;":1090,"tdot;":8411,"telrec;":8981,"tfr;":[55349,56625],"there4;":8756,"therefore;":8756,"theta;":952,"thetasym;":977,"thetav;":977,"thickapprox;":8776,"thicksim;":8764,"thinsp;":8201,"thkap;":8776,"thksim;":8764,thorn:254,"thorn;":254,"tilde;":732,times:215,"times;":215,"timesb;":8864,"timesbar;":10801,"timesd;":10800,"tint;":8749,"toea;":10536,"top;":8868,"topbot;":9014,"topcir;":10993,"topf;":[55349,56677],"topfork;":10970,"tosa;":10537,"tprime;":8244,"trade;":8482,"triangle;":9653,"triangledown;":9663,"triangleleft;":9667,"trianglelefteq;":8884,"triangleq;":8796,"triangleright;":9657,"trianglerighteq;":8885,"tridot;":9708,"trie;":8796,"triminus;":10810,"triplus;":10809,"trisb;":10701,"tritime;":10811,"trpezium;":9186,"tscr;":[55349,56521],"tscy;":1094,"tshcy;":1115,"tstrok;":359,"twixt;":8812,"twoheadleftarrow;":8606,"twoheadrightarrow;":8608,"uArr;":8657,"uHar;":10595,uacute:250,"uacute;":250,"uarr;":8593,"ubrcy;":1118,"ubreve;":365,ucirc:251,"ucirc;":251,"ucy;":1091,"udarr;":8645,"udblac;":369,"udhar;":10606,"ufisht;":10622,"ufr;":[55349,56626],ugrave:249,"ugrave;":249,"uharl;":8639,"uharr;":8638,"uhblk;":9600,"ulcorn;":8988,"ulcorner;":8988,"ulcrop;":8975,"ultri;":9720,"umacr;":363,uml:168,"uml;":168,"uogon;":371,"uopf;":[55349,56678],"uparrow;":8593,"updownarrow;":8597,"upharpoonleft;":8639,"upharpoonright;":8638,"uplus;":8846,"upsi;":965,"upsih;":978,"upsilon;":965,"upuparrows;":8648,"urcorn;":8989,"urcorner;":8989,"urcrop;":8974,"uring;":367,"urtri;":9721,"uscr;":[55349,56522],"utdot;":8944,"utilde;":361,"utri;":9653,"utrif;":9652,"uuarr;":8648,uuml:252,"uuml;":252,"uwangle;":10663,"vArr;":8661,"vBar;":10984,"vBarv;":10985,"vDash;":8872,"vangrt;":10652,"varepsilon;":1013,"varkappa;":1008,"varnothing;":8709,"varphi;":981,"varpi;":982,"varpropto;":8733,"varr;":8597,"varrho;":1009,"varsigma;":962,"varsubsetneq;":[8842,65024],"varsubsetneqq;":[10955,65024],"varsupsetneq;":[8843,65024],"varsupsetneqq;":[10956,65024],"vartheta;":977,"vartriangleleft;":8882,"vartriangleright;":8883,"vcy;":1074,"vdash;":8866,"vee;":8744,"veebar;":8891,"veeeq;":8794,"vellip;":8942,"verbar;":124,"vert;":124,"vfr;":[55349,56627],"vltri;":8882,"vnsub;":[8834,8402],"vnsup;":[8835,8402],"vopf;":[55349,56679],"vprop;":8733,"vrtri;":8883,"vscr;":[55349,56523],"vsubnE;":[10955,65024],"vsubne;":[8842,65024],"vsupnE;":[10956,65024],"vsupne;":[8843,65024],"vzigzag;":10650,"wcirc;":373,"wedbar;":10847,"wedge;":8743,"wedgeq;":8793,"weierp;":8472,"wfr;":[55349,56628],"wopf;":[55349,56680],"wp;":8472,"wr;":8768,"wreath;":8768,"wscr;":[55349,56524],"xcap;":8898,"xcirc;":9711,"xcup;":8899,"xdtri;":9661,"xfr;":[55349,56629],"xhArr;":10234,"xharr;":10231,"xi;":958,"xlArr;":10232,"xlarr;":10229,"xmap;":10236,"xnis;":8955,"xodot;":10752,"xopf;":[55349,56681],"xoplus;":10753,"xotime;":10754,"xrArr;":10233,"xrarr;":10230,"xscr;":[55349,56525],"xsqcup;":10758,"xuplus;":10756,"xutri;":9651,"xvee;":8897,"xwedge;":8896,yacute:253,"yacute;":253,"yacy;":1103,"ycirc;":375,"ycy;":1099,yen:165,"yen;":165,"yfr;":[55349,56630],"yicy;":1111,"yopf;":[55349,56682],"yscr;":[55349,56526],"yucy;":1102,yuml:255,"yuml;":255,"zacute;":378,"zcaron;":382,"zcy;":1079,"zdot;":380,"zeetrf;":8488,"zeta;":950,"zfr;":[55349,56631],"zhcy;":1078,"zigrarr;":8669,"zopf;":[55349,56683],"zscr;":[55349,56527],"zwj;":8205,"zwnj;":8204},Oe=/(A(?:Elig;?|MP;?|acute;?|breve;|c(?:irc;?|y;)|fr;|grave;?|lpha;|macr;|nd;|o(?:gon;|pf;)|pplyFunction;|ring;?|s(?:cr;|sign;)|tilde;?|uml;?)|B(?:a(?:ckslash;|r(?:v;|wed;))|cy;|e(?:cause;|rnoullis;|ta;)|fr;|opf;|reve;|scr;|umpeq;)|C(?:Hcy;|OPY;?|a(?:cute;|p(?:;|italDifferentialD;)|yleys;)|c(?:aron;|edil;?|irc;|onint;)|dot;|e(?:dilla;|nterDot;)|fr;|hi;|ircle(?:Dot;|Minus;|Plus;|Times;)|lo(?:ckwiseContourIntegral;|seCurly(?:DoubleQuote;|Quote;))|o(?:lon(?:;|e;)|n(?:gruent;|int;|tourIntegral;)|p(?:f;|roduct;)|unterClockwiseContourIntegral;)|ross;|scr;|up(?:;|Cap;))|D(?:D(?:;|otrahd;)|Jcy;|Scy;|Zcy;|a(?:gger;|rr;|shv;)|c(?:aron;|y;)|el(?:;|ta;)|fr;|i(?:a(?:critical(?:Acute;|Do(?:t;|ubleAcute;)|Grave;|Tilde;)|mond;)|fferentialD;)|o(?:pf;|t(?:;|Dot;|Equal;)|uble(?:ContourIntegral;|Do(?:t;|wnArrow;)|L(?:eft(?:Arrow;|RightArrow;|Tee;)|ong(?:Left(?:Arrow;|RightArrow;)|RightArrow;))|Right(?:Arrow;|Tee;)|Up(?:Arrow;|DownArrow;)|VerticalBar;)|wn(?:Arrow(?:;|Bar;|UpArrow;)|Breve;|Left(?:RightVector;|TeeVector;|Vector(?:;|Bar;))|Right(?:TeeVector;|Vector(?:;|Bar;))|Tee(?:;|Arrow;)|arrow;))|s(?:cr;|trok;))|E(?:NG;|TH;?|acute;?|c(?:aron;|irc;?|y;)|dot;|fr;|grave;?|lement;|m(?:acr;|pty(?:SmallSquare;|VerySmallSquare;))|o(?:gon;|pf;)|psilon;|qu(?:al(?:;|Tilde;)|ilibrium;)|s(?:cr;|im;)|ta;|uml;?|x(?:ists;|ponentialE;))|F(?:cy;|fr;|illed(?:SmallSquare;|VerySmallSquare;)|o(?:pf;|rAll;|uriertrf;)|scr;)|G(?:Jcy;|T;?|amma(?:;|d;)|breve;|c(?:edil;|irc;|y;)|dot;|fr;|g;|opf;|reater(?:Equal(?:;|Less;)|FullEqual;|Greater;|Less;|SlantEqual;|Tilde;)|scr;|t;)|H(?:ARDcy;|a(?:cek;|t;)|circ;|fr;|ilbertSpace;|o(?:pf;|rizontalLine;)|s(?:cr;|trok;)|ump(?:DownHump;|Equal;))|I(?:Ecy;|Jlig;|Ocy;|acute;?|c(?:irc;?|y;)|dot;|fr;|grave;?|m(?:;|a(?:cr;|ginaryI;)|plies;)|n(?:t(?:;|e(?:gral;|rsection;))|visible(?:Comma;|Times;))|o(?:gon;|pf;|ta;)|scr;|tilde;|u(?:kcy;|ml;?))|J(?:c(?:irc;|y;)|fr;|opf;|s(?:cr;|ercy;)|ukcy;)|K(?:Hcy;|Jcy;|appa;|c(?:edil;|y;)|fr;|opf;|scr;)|L(?:Jcy;|T;?|a(?:cute;|mbda;|ng;|placetrf;|rr;)|c(?:aron;|edil;|y;)|e(?:ft(?:A(?:ngleBracket;|rrow(?:;|Bar;|RightArrow;))|Ceiling;|Do(?:ubleBracket;|wn(?:TeeVector;|Vector(?:;|Bar;)))|Floor;|Right(?:Arrow;|Vector;)|T(?:ee(?:;|Arrow;|Vector;)|riangle(?:;|Bar;|Equal;))|Up(?:DownVector;|TeeVector;|Vector(?:;|Bar;))|Vector(?:;|Bar;)|arrow;|rightarrow;)|ss(?:EqualGreater;|FullEqual;|Greater;|Less;|SlantEqual;|Tilde;))|fr;|l(?:;|eftarrow;)|midot;|o(?:ng(?:Left(?:Arrow;|RightArrow;)|RightArrow;|left(?:arrow;|rightarrow;)|rightarrow;)|pf;|wer(?:LeftArrow;|RightArrow;))|s(?:cr;|h;|trok;)|t;)|M(?:ap;|cy;|e(?:diumSpace;|llintrf;)|fr;|inusPlus;|opf;|scr;|u;)|N(?:Jcy;|acute;|c(?:aron;|edil;|y;)|e(?:gative(?:MediumSpace;|Thi(?:ckSpace;|nSpace;)|VeryThinSpace;)|sted(?:GreaterGreater;|LessLess;)|wLine;)|fr;|o(?:Break;|nBreakingSpace;|pf;|t(?:;|C(?:ongruent;|upCap;)|DoubleVerticalBar;|E(?:lement;|qual(?:;|Tilde;)|xists;)|Greater(?:;|Equal;|FullEqual;|Greater;|Less;|SlantEqual;|Tilde;)|Hump(?:DownHump;|Equal;)|Le(?:ftTriangle(?:;|Bar;|Equal;)|ss(?:;|Equal;|Greater;|Less;|SlantEqual;|Tilde;))|Nested(?:GreaterGreater;|LessLess;)|Precedes(?:;|Equal;|SlantEqual;)|R(?:everseElement;|ightTriangle(?:;|Bar;|Equal;))|S(?:quareSu(?:bset(?:;|Equal;)|perset(?:;|Equal;))|u(?:bset(?:;|Equal;)|cceeds(?:;|Equal;|SlantEqual;|Tilde;)|perset(?:;|Equal;)))|Tilde(?:;|Equal;|FullEqual;|Tilde;)|VerticalBar;))|scr;|tilde;?|u;)|O(?:Elig;|acute;?|c(?:irc;?|y;)|dblac;|fr;|grave;?|m(?:acr;|ega;|icron;)|opf;|penCurly(?:DoubleQuote;|Quote;)|r;|s(?:cr;|lash;?)|ti(?:lde;?|mes;)|uml;?|ver(?:B(?:ar;|rac(?:e;|ket;))|Parenthesis;))|P(?:artialD;|cy;|fr;|hi;|i;|lusMinus;|o(?:incareplane;|pf;)|r(?:;|ecedes(?:;|Equal;|SlantEqual;|Tilde;)|ime;|o(?:duct;|portion(?:;|al;)))|s(?:cr;|i;))|Q(?:UOT;?|fr;|opf;|scr;)|R(?:Barr;|EG;?|a(?:cute;|ng;|rr(?:;|tl;))|c(?:aron;|edil;|y;)|e(?:;|verse(?:E(?:lement;|quilibrium;)|UpEquilibrium;))|fr;|ho;|ight(?:A(?:ngleBracket;|rrow(?:;|Bar;|LeftArrow;))|Ceiling;|Do(?:ubleBracket;|wn(?:TeeVector;|Vector(?:;|Bar;)))|Floor;|T(?:ee(?:;|Arrow;|Vector;)|riangle(?:;|Bar;|Equal;))|Up(?:DownVector;|TeeVector;|Vector(?:;|Bar;))|Vector(?:;|Bar;)|arrow;)|o(?:pf;|undImplies;)|rightarrow;|s(?:cr;|h;)|uleDelayed;)|S(?:H(?:CHcy;|cy;)|OFTcy;|acute;|c(?:;|aron;|edil;|irc;|y;)|fr;|hort(?:DownArrow;|LeftArrow;|RightArrow;|UpArrow;)|igma;|mallCircle;|opf;|q(?:rt;|uare(?:;|Intersection;|Su(?:bset(?:;|Equal;)|perset(?:;|Equal;))|Union;))|scr;|tar;|u(?:b(?:;|set(?:;|Equal;))|c(?:ceeds(?:;|Equal;|SlantEqual;|Tilde;)|hThat;)|m;|p(?:;|erset(?:;|Equal;)|set;)))|T(?:HORN;?|RADE;|S(?:Hcy;|cy;)|a(?:b;|u;)|c(?:aron;|edil;|y;)|fr;|h(?:e(?:refore;|ta;)|i(?:ckSpace;|nSpace;))|ilde(?:;|Equal;|FullEqual;|Tilde;)|opf;|ripleDot;|s(?:cr;|trok;))|U(?:a(?:cute;?|rr(?:;|ocir;))|br(?:cy;|eve;)|c(?:irc;?|y;)|dblac;|fr;|grave;?|macr;|n(?:der(?:B(?:ar;|rac(?:e;|ket;))|Parenthesis;)|ion(?:;|Plus;))|o(?:gon;|pf;)|p(?:Arrow(?:;|Bar;|DownArrow;)|DownArrow;|Equilibrium;|Tee(?:;|Arrow;)|arrow;|downarrow;|per(?:LeftArrow;|RightArrow;)|si(?:;|lon;))|ring;|scr;|tilde;|uml;?)|V(?:Dash;|bar;|cy;|dash(?:;|l;)|e(?:e;|r(?:bar;|t(?:;|ical(?:Bar;|Line;|Separator;|Tilde;))|yThinSpace;))|fr;|opf;|scr;|vdash;)|W(?:circ;|edge;|fr;|opf;|scr;)|X(?:fr;|i;|opf;|scr;)|Y(?:Acy;|Icy;|Ucy;|acute;?|c(?:irc;|y;)|fr;|opf;|scr;|uml;)|Z(?:Hcy;|acute;|c(?:aron;|y;)|dot;|e(?:roWidthSpace;|ta;)|fr;|opf;|scr;)|a(?:acute;?|breve;|c(?:;|E;|d;|irc;?|ute;?|y;)|elig;?|f(?:;|r;)|grave;?|l(?:e(?:fsym;|ph;)|pha;)|m(?:a(?:cr;|lg;)|p;?)|n(?:d(?:;|and;|d;|slope;|v;)|g(?:;|e;|le;|msd(?:;|a(?:a;|b;|c;|d;|e;|f;|g;|h;))|rt(?:;|vb(?:;|d;))|s(?:ph;|t;)|zarr;))|o(?:gon;|pf;)|p(?:;|E;|acir;|e;|id;|os;|prox(?:;|eq;))|ring;?|s(?:cr;|t;|ymp(?:;|eq;))|tilde;?|uml;?|w(?:conint;|int;))|b(?:Not;|a(?:ck(?:cong;|epsilon;|prime;|sim(?:;|eq;))|r(?:vee;|wed(?:;|ge;)))|brk(?:;|tbrk;)|c(?:ong;|y;)|dquo;|e(?:caus(?:;|e;)|mptyv;|psi;|rnou;|t(?:a;|h;|ween;))|fr;|ig(?:c(?:ap;|irc;|up;)|o(?:dot;|plus;|times;)|s(?:qcup;|tar;)|triangle(?:down;|up;)|uplus;|vee;|wedge;)|karow;|l(?:a(?:ck(?:lozenge;|square;|triangle(?:;|down;|left;|right;))|nk;)|k(?:1(?:2;|4;)|34;)|ock;)|n(?:e(?:;|quiv;)|ot;)|o(?:pf;|t(?:;|tom;)|wtie;|x(?:D(?:L;|R;|l;|r;)|H(?:;|D;|U;|d;|u;)|U(?:L;|R;|l;|r;)|V(?:;|H;|L;|R;|h;|l;|r;)|box;|d(?:L;|R;|l;|r;)|h(?:;|D;|U;|d;|u;)|minus;|plus;|times;|u(?:L;|R;|l;|r;)|v(?:;|H;|L;|R;|h;|l;|r;)))|prime;|r(?:eve;|vbar;?)|s(?:cr;|emi;|im(?:;|e;)|ol(?:;|b;|hsub;))|u(?:ll(?:;|et;)|mp(?:;|E;|e(?:;|q;))))|c(?:a(?:cute;|p(?:;|and;|brcup;|c(?:ap;|up;)|dot;|s;)|r(?:et;|on;))|c(?:a(?:ps;|ron;)|edil;?|irc;|ups(?:;|sm;))|dot;|e(?:dil;?|mptyv;|nt(?:;|erdot;|))|fr;|h(?:cy;|eck(?:;|mark;)|i;)|ir(?:;|E;|c(?:;|eq;|le(?:arrow(?:left;|right;)|d(?:R;|S;|ast;|circ;|dash;)))|e;|fnint;|mid;|scir;)|lubs(?:;|uit;)|o(?:lon(?:;|e(?:;|q;))|m(?:ma(?:;|t;)|p(?:;|fn;|le(?:ment;|xes;)))|n(?:g(?:;|dot;)|int;)|p(?:f;|rod;|y(?:;|sr;|)))|r(?:arr;|oss;)|s(?:cr;|u(?:b(?:;|e;)|p(?:;|e;)))|tdot;|u(?:darr(?:l;|r;)|e(?:pr;|sc;)|larr(?:;|p;)|p(?:;|brcap;|c(?:ap;|up;)|dot;|or;|s;)|r(?:arr(?:;|m;)|ly(?:eq(?:prec;|succ;)|vee;|wedge;)|ren;?|vearrow(?:left;|right;))|vee;|wed;)|w(?:conint;|int;)|ylcty;)|d(?:Arr;|Har;|a(?:gger;|leth;|rr;|sh(?:;|v;))|b(?:karow;|lac;)|c(?:aron;|y;)|d(?:;|a(?:gger;|rr;)|otseq;)|e(?:g;?|lta;|mptyv;)|f(?:isht;|r;)|har(?:l;|r;)|i(?:am(?:;|ond(?:;|suit;)|s;)|e;|gamma;|sin;|v(?:;|ide(?:;|ontimes;|)|onx;))|jcy;|lc(?:orn;|rop;)|o(?:llar;|pf;|t(?:;|eq(?:;|dot;)|minus;|plus;|square;)|ublebarwedge;|wn(?:arrow;|downarrows;|harpoon(?:left;|right;)))|r(?:bkarow;|c(?:orn;|rop;))|s(?:c(?:r;|y;)|ol;|trok;)|t(?:dot;|ri(?:;|f;))|u(?:arr;|har;)|wangle;|z(?:cy;|igrarr;))|e(?:D(?:Dot;|ot;)|a(?:cute;?|ster;)|c(?:aron;|ir(?:;|c;?)|olon;|y;)|dot;|e;|f(?:Dot;|r;)|g(?:;|rave;?|s(?:;|dot;))|l(?:;|inters;|l;|s(?:;|dot;))|m(?:acr;|pty(?:;|set;|v;)|sp(?:1(?:3;|4;)|;))|n(?:g;|sp;)|o(?:gon;|pf;)|p(?:ar(?:;|sl;)|lus;|si(?:;|lon;|v;))|q(?:c(?:irc;|olon;)|s(?:im;|lant(?:gtr;|less;))|u(?:als;|est;|iv(?:;|DD;))|vparsl;)|r(?:Dot;|arr;)|s(?:cr;|dot;|im;)|t(?:a;|h;?)|u(?:ml;?|ro;)|x(?:cl;|ist;|p(?:ectation;|onentiale;)))|f(?:allingdotseq;|cy;|emale;|f(?:ilig;|l(?:ig;|lig;)|r;)|ilig;|jlig;|l(?:at;|lig;|tns;)|nof;|o(?:pf;|r(?:all;|k(?:;|v;)))|partint;|r(?:a(?:c(?:1(?:2;?|3;|4;?|5;|6;|8;)|2(?:3;|5;)|3(?:4;?|5;|8;)|45;|5(?:6;|8;)|78;)|sl;)|own;)|scr;)|g(?:E(?:;|l;)|a(?:cute;|mma(?:;|d;)|p;)|breve;|c(?:irc;|y;)|dot;|e(?:;|l;|q(?:;|q;|slant;)|s(?:;|cc;|dot(?:;|o(?:;|l;))|l(?:;|es;)))|fr;|g(?:;|g;)|imel;|jcy;|l(?:;|E;|a;|j;)|n(?:E;|ap(?:;|prox;)|e(?:;|q(?:;|q;))|sim;)|opf;|rave;|s(?:cr;|im(?:;|e;|l;))|t(?:;|c(?:c;|ir;)|dot;|lPar;|quest;|r(?:a(?:pprox;|rr;)|dot;|eq(?:less;|qless;)|less;|sim;)|)|v(?:ertneqq;|nE;))|h(?:Arr;|a(?:irsp;|lf;|milt;|r(?:dcy;|r(?:;|cir;|w;)))|bar;|circ;|e(?:arts(?:;|uit;)|llip;|rcon;)|fr;|ks(?:earow;|warow;)|o(?:arr;|mtht;|ok(?:leftarrow;|rightarrow;)|pf;|rbar;)|s(?:cr;|lash;|trok;)|y(?:bull;|phen;))|i(?:acute;?|c(?:;|irc;?|y;)|e(?:cy;|xcl;?)|f(?:f;|r;)|grave;?|i(?:;|i(?:int;|nt;)|nfin;|ota;)|jlig;|m(?:a(?:cr;|g(?:e;|line;|part;)|th;)|of;|ped;)|n(?:;|care;|fin(?:;|tie;)|odot;|t(?:;|cal;|e(?:gers;|rcal;)|larhk;|prod;))|o(?:cy;|gon;|pf;|ta;)|prod;|quest;?|s(?:cr;|in(?:;|E;|dot;|s(?:;|v;)|v;))|t(?:;|ilde;)|u(?:kcy;|ml;?))|j(?:c(?:irc;|y;)|fr;|math;|opf;|s(?:cr;|ercy;)|ukcy;)|k(?:appa(?:;|v;)|c(?:edil;|y;)|fr;|green;|hcy;|jcy;|opf;|scr;)|l(?:A(?:arr;|rr;|tail;)|Barr;|E(?:;|g;)|Har;|a(?:cute;|emptyv;|gran;|mbda;|ng(?:;|d;|le;)|p;|quo;?|rr(?:;|b(?:;|fs;)|fs;|hk;|lp;|pl;|sim;|tl;)|t(?:;|ail;|e(?:;|s;)))|b(?:arr;|brk;|r(?:ac(?:e;|k;)|k(?:e;|sl(?:d;|u;))))|c(?:aron;|e(?:dil;|il;)|ub;|y;)|d(?:ca;|quo(?:;|r;)|r(?:dhar;|ushar;)|sh;)|e(?:;|ft(?:arrow(?:;|tail;)|harpoon(?:down;|up;)|leftarrows;|right(?:arrow(?:;|s;)|harpoons;|squigarrow;)|threetimes;)|g;|q(?:;|q;|slant;)|s(?:;|cc;|dot(?:;|o(?:;|r;))|g(?:;|es;)|s(?:approx;|dot;|eq(?:gtr;|qgtr;)|gtr;|sim;)))|f(?:isht;|loor;|r;)|g(?:;|E;)|h(?:ar(?:d;|u(?:;|l;))|blk;)|jcy;|l(?:;|arr;|corner;|hard;|tri;)|m(?:idot;|oust(?:;|ache;))|n(?:E;|ap(?:;|prox;)|e(?:;|q(?:;|q;))|sim;)|o(?:a(?:ng;|rr;)|brk;|ng(?:left(?:arrow;|rightarrow;)|mapsto;|rightarrow;)|oparrow(?:left;|right;)|p(?:ar;|f;|lus;)|times;|w(?:ast;|bar;)|z(?:;|enge;|f;))|par(?:;|lt;)|r(?:arr;|corner;|har(?:;|d;)|m;|tri;)|s(?:aquo;|cr;|h;|im(?:;|e;|g;)|q(?:b;|uo(?:;|r;))|trok;)|t(?:;|c(?:c;|ir;)|dot;|hree;|imes;|larr;|quest;|r(?:Par;|i(?:;|e;|f;))|)|ur(?:dshar;|uhar;)|v(?:ertneqq;|nE;))|m(?:DDot;|a(?:cr;?|l(?:e;|t(?:;|ese;))|p(?:;|sto(?:;|down;|left;|up;))|rker;)|c(?:omma;|y;)|dash;|easuredangle;|fr;|ho;|i(?:cro;?|d(?:;|ast;|cir;|dot;?)|nus(?:;|b;|d(?:;|u;)))|l(?:cp;|dr;)|nplus;|o(?:dels;|pf;)|p;|s(?:cr;|tpos;)|u(?:;|ltimap;|map;))|n(?:G(?:g;|t(?:;|v;))|L(?:eft(?:arrow;|rightarrow;)|l;|t(?:;|v;))|Rightarrow;|V(?:Dash;|dash;)|a(?:bla;|cute;|ng;|p(?:;|E;|id;|os;|prox;)|tur(?:;|al(?:;|s;)))|b(?:sp;?|ump(?:;|e;))|c(?:a(?:p;|ron;)|edil;|ong(?:;|dot;)|up;|y;)|dash;|e(?:;|Arr;|ar(?:hk;|r(?:;|ow;))|dot;|quiv;|s(?:ear;|im;)|xist(?:;|s;))|fr;|g(?:E;|e(?:;|q(?:;|q;|slant;)|s;)|sim;|t(?:;|r;))|h(?:Arr;|arr;|par;)|i(?:;|s(?:;|d;)|v;)|jcy;|l(?:Arr;|E;|arr;|dr;|e(?:;|ft(?:arrow;|rightarrow;)|q(?:;|q;|slant;)|s(?:;|s;))|sim;|t(?:;|ri(?:;|e;)))|mid;|o(?:pf;|t(?:;|in(?:;|E;|dot;|v(?:a;|b;|c;))|ni(?:;|v(?:a;|b;|c;))|))|p(?:ar(?:;|allel;|sl;|t;)|olint;|r(?:;|cue;|e(?:;|c(?:;|eq;))))|r(?:Arr;|arr(?:;|c;|w;)|ightarrow;|tri(?:;|e;))|s(?:c(?:;|cue;|e;|r;)|hort(?:mid;|parallel;)|im(?:;|e(?:;|q;))|mid;|par;|qsu(?:be;|pe;)|u(?:b(?:;|E;|e;|set(?:;|eq(?:;|q;)))|cc(?:;|eq;)|p(?:;|E;|e;|set(?:;|eq(?:;|q;)))))|t(?:gl;|ilde;?|lg;|riangle(?:left(?:;|eq;)|right(?:;|eq;)))|u(?:;|m(?:;|ero;|sp;))|v(?:Dash;|Harr;|ap;|dash;|g(?:e;|t;)|infin;|l(?:Arr;|e;|t(?:;|rie;))|r(?:Arr;|trie;)|sim;)|w(?:Arr;|ar(?:hk;|r(?:;|ow;))|near;))|o(?:S;|a(?:cute;?|st;)|c(?:ir(?:;|c;?)|y;)|d(?:ash;|blac;|iv;|ot;|sold;)|elig;|f(?:cir;|r;)|g(?:on;|rave;?|t;)|h(?:bar;|m;)|int;|l(?:arr;|c(?:ir;|ross;)|ine;|t;)|m(?:acr;|ega;|i(?:cron;|d;|nus;))|opf;|p(?:ar;|erp;|lus;)|r(?:;|arr;|d(?:;|er(?:;|of;)|f;?|m;?)|igof;|or;|slope;|v;)|s(?:cr;|lash;?|ol;)|ti(?:lde;?|mes(?:;|as;))|uml;?|vbar;)|p(?:ar(?:;|a(?:;|llel;|)|s(?:im;|l;)|t;)|cy;|er(?:cnt;|iod;|mil;|p;|tenk;)|fr;|h(?:i(?:;|v;)|mmat;|one;)|i(?:;|tchfork;|v;)|l(?:an(?:ck(?:;|h;)|kv;)|us(?:;|acir;|b;|cir;|d(?:o;|u;)|e;|mn;?|sim;|two;))|m;|o(?:intint;|pf;|und;?)|r(?:;|E;|ap;|cue;|e(?:;|c(?:;|approx;|curlyeq;|eq;|n(?:approx;|eqq;|sim;)|sim;))|ime(?:;|s;)|n(?:E;|ap;|sim;)|o(?:d;|f(?:alar;|line;|surf;)|p(?:;|to;))|sim;|urel;)|s(?:cr;|i;)|uncsp;)|q(?:fr;|int;|opf;|prime;|scr;|u(?:at(?:ernions;|int;)|est(?:;|eq;)|ot;?))|r(?:A(?:arr;|rr;|tail;)|Barr;|Har;|a(?:c(?:e;|ute;)|dic;|emptyv;|ng(?:;|d;|e;|le;)|quo;?|rr(?:;|ap;|b(?:;|fs;)|c;|fs;|hk;|lp;|pl;|sim;|tl;|w;)|t(?:ail;|io(?:;|nals;)))|b(?:arr;|brk;|r(?:ac(?:e;|k;)|k(?:e;|sl(?:d;|u;))))|c(?:aron;|e(?:dil;|il;)|ub;|y;)|d(?:ca;|ldhar;|quo(?:;|r;)|sh;)|e(?:al(?:;|ine;|part;|s;)|ct;|g;?)|f(?:isht;|loor;|r;)|h(?:ar(?:d;|u(?:;|l;))|o(?:;|v;))|i(?:ght(?:arrow(?:;|tail;)|harpoon(?:down;|up;)|left(?:arrows;|harpoons;)|rightarrows;|squigarrow;|threetimes;)|ng;|singdotseq;)|l(?:arr;|har;|m;)|moust(?:;|ache;)|nmid;|o(?:a(?:ng;|rr;)|brk;|p(?:ar;|f;|lus;)|times;)|p(?:ar(?:;|gt;)|polint;)|rarr;|s(?:aquo;|cr;|h;|q(?:b;|uo(?:;|r;)))|t(?:hree;|imes;|ri(?:;|e;|f;|ltri;))|uluhar;|x;)|s(?:acute;|bquo;|c(?:;|E;|a(?:p;|ron;)|cue;|e(?:;|dil;)|irc;|n(?:E;|ap;|sim;)|polint;|sim;|y;)|dot(?:;|b;|e;)|e(?:Arr;|ar(?:hk;|r(?:;|ow;))|ct;?|mi;|swar;|tm(?:inus;|n;)|xt;)|fr(?:;|own;)|h(?:arp;|c(?:hcy;|y;)|ort(?:mid;|parallel;)|y;?)|i(?:gma(?:;|f;|v;)|m(?:;|dot;|e(?:;|q;)|g(?:;|E;)|l(?:;|E;)|ne;|plus;|rarr;))|larr;|m(?:a(?:llsetminus;|shp;)|eparsl;|i(?:d;|le;)|t(?:;|e(?:;|s;)))|o(?:ftcy;|l(?:;|b(?:;|ar;))|pf;)|pa(?:des(?:;|uit;)|r;)|q(?:c(?:ap(?:;|s;)|up(?:;|s;))|su(?:b(?:;|e;|set(?:;|eq;))|p(?:;|e;|set(?:;|eq;)))|u(?:;|ar(?:e;|f;)|f;))|rarr;|s(?:cr;|etmn;|mile;|tarf;)|t(?:ar(?:;|f;)|r(?:aight(?:epsilon;|phi;)|ns;))|u(?:b(?:;|E;|dot;|e(?:;|dot;)|mult;|n(?:E;|e;)|plus;|rarr;|s(?:et(?:;|eq(?:;|q;)|neq(?:;|q;))|im;|u(?:b;|p;)))|cc(?:;|approx;|curlyeq;|eq;|n(?:approx;|eqq;|sim;)|sim;)|m;|ng;|p(?:1;?|2;?|3;?|;|E;|d(?:ot;|sub;)|e(?:;|dot;)|hs(?:ol;|ub;)|larr;|mult;|n(?:E;|e;)|plus;|s(?:et(?:;|eq(?:;|q;)|neq(?:;|q;))|im;|u(?:b;|p;))))|w(?:Arr;|ar(?:hk;|r(?:;|ow;))|nwar;)|zlig;?)|t(?:a(?:rget;|u;)|brk;|c(?:aron;|edil;|y;)|dot;|elrec;|fr;|h(?:e(?:re(?:4;|fore;)|ta(?:;|sym;|v;))|i(?:ck(?:approx;|sim;)|nsp;)|k(?:ap;|sim;)|orn;?)|i(?:lde;|mes(?:;|b(?:;|ar;)|d;|)|nt;)|o(?:ea;|p(?:;|bot;|cir;|f(?:;|ork;))|sa;)|prime;|r(?:ade;|i(?:angle(?:;|down;|left(?:;|eq;)|q;|right(?:;|eq;))|dot;|e;|minus;|plus;|sb;|time;)|pezium;)|s(?:c(?:r;|y;)|hcy;|trok;)|w(?:ixt;|ohead(?:leftarrow;|rightarrow;)))|u(?:Arr;|Har;|a(?:cute;?|rr;)|br(?:cy;|eve;)|c(?:irc;?|y;)|d(?:arr;|blac;|har;)|f(?:isht;|r;)|grave;?|h(?:ar(?:l;|r;)|blk;)|l(?:c(?:orn(?:;|er;)|rop;)|tri;)|m(?:acr;|l;?)|o(?:gon;|pf;)|p(?:arrow;|downarrow;|harpoon(?:left;|right;)|lus;|si(?:;|h;|lon;)|uparrows;)|r(?:c(?:orn(?:;|er;)|rop;)|ing;|tri;)|scr;|t(?:dot;|ilde;|ri(?:;|f;))|u(?:arr;|ml;?)|wangle;)|v(?:Arr;|Bar(?:;|v;)|Dash;|a(?:ngrt;|r(?:epsilon;|kappa;|nothing;|p(?:hi;|i;|ropto;)|r(?:;|ho;)|s(?:igma;|u(?:bsetneq(?:;|q;)|psetneq(?:;|q;)))|t(?:heta;|riangle(?:left;|right;))))|cy;|dash;|e(?:e(?:;|bar;|eq;)|llip;|r(?:bar;|t;))|fr;|ltri;|nsu(?:b;|p;)|opf;|prop;|rtri;|s(?:cr;|u(?:bn(?:E;|e;)|pn(?:E;|e;)))|zigzag;)|w(?:circ;|e(?:d(?:bar;|ge(?:;|q;))|ierp;)|fr;|opf;|p;|r(?:;|eath;)|scr;)|x(?:c(?:ap;|irc;|up;)|dtri;|fr;|h(?:Arr;|arr;)|i;|l(?:Arr;|arr;)|map;|nis;|o(?:dot;|p(?:f;|lus;)|time;)|r(?:Arr;|arr;)|s(?:cr;|qcup;)|u(?:plus;|tri;)|vee;|wedge;)|y(?:ac(?:ute;?|y;)|c(?:irc;|y;)|en;?|fr;|icy;|opf;|scr;|u(?:cy;|ml;?))|z(?:acute;|c(?:aron;|y;)|dot;|e(?:etrf;|ta;)|fr;|hcy;|igrarr;|opf;|scr;|w(?:j;|nj;)))|[\s\S]/g,Un=32,jn=/[^\r"&\u0000]+/g,Vn=/[^\r'&\u0000]+/g,Gn=/[^\r\t\n\f &>\u0000]+/g,zn=/[^\r\t\n\f \/>A-Z\u0000]+/g,Wn=/[^\r\t\n\f \/=>A-Z\u0000]+/g,Kn=/[^\]\r\u0000\uffff]*/g,Xn=/[^&<\r\u0000\uffff]*/g,zr=/[^<\r\u0000\uffff]*/g,Qn=/[^\r\u0000\uffff]*/g,Wr=/(?:(\/)?([a-z]+)>)|[\s\S]/g,Kr=/(?:([-a-z]+)[ \t\n\f]*=[ \t\n\f]*('[^'&\r\u0000]*'|"[^"&\r\u0000]*"|[^\t\n\r\f "&'\u0000>][^&> \t\n\r\f\u0000]*[ \t\n\f]))|[\s\S]/g,Ft=/[^\x09\x0A\x0C\x0D\x20]/,dr=/[^\x09\x0A\x0C\x0D\x20]/g,Yn=/[^\x00\x09\x0A\x0C\x0D\x20]/,ht=/^[\x09\x0A\x0C\x0D\x20]+/,Pt=/\x00/g;function Ce(H){var B=16384;if(H.length<B)return String.fromCharCode.apply(String,H);for(var Q="",z=0;z<H.length;z+=B)Q+=String.fromCharCode.apply(String,H.slice(z,z+B));return Q}function Zn(H){for(var B=[],Q=0;Q<H.length;Q++)B[Q]=H.charCodeAt(Q);return B}function me(H,B){if(typeof B=="string")return H.namespaceURI===a.HTML&&H.localName===B;var Q=B[H.namespaceURI];return Q&&Q[H.localName]}function Xr(H){return me(H,K)}function Qr(H){if(me(H,ce))return!0;if(H.namespaceURI===a.MATHML&&H.localName==="annotation-xml"){var B=H.getAttribute("encoding");if(B&&(B=B.toLowerCase()),B==="text/html"||B==="application/xhtml+xml")return!0}return!1}function $n(H){return H in L?L[H]:H}function Yr(H){for(var B=0,Q=H.length;B<Q;B++)H[B][0]in g&&(H[B][0]=g[H[B][0]])}function Zr(H){for(var B=0,Q=H.length;B<Q;B++)if(H[B][0]==="definitionurl"){H[B][0]="definitionURL";break}}function mr(H){for(var B=0,Q=H.length;B<Q;B++)H[B][0]in oe&&H[B].push(oe[H[B][0]])}function $r(H,B){for(var Q=0,z=H.length;Q<z;Q++){var _e=H[Q][0],W=H[Q][1];B.hasAttribute(_e)||B._setAttribute(_e,W)}}fe.ElementStack=function(){this.elements=[],this.top=null},fe.ElementStack.prototype.push=function(H){this.elements.push(H),this.top=H},fe.ElementStack.prototype.pop=function(H){this.elements.pop(),this.top=this.elements[this.elements.length-1]},fe.ElementStack.prototype.popTag=function(H){for(var B=this.elements.length-1;B>0;B--){var Q=this.elements[B];if(me(Q,H))break}this.elements.length=B,this.top=this.elements[B-1]},fe.ElementStack.prototype.popElementType=function(H){for(var B=this.elements.length-1;B>0&&!(this.elements[B]instanceof H);B--);this.elements.length=B,this.top=this.elements[B-1]},fe.ElementStack.prototype.popElement=function(H){for(var B=this.elements.length-1;B>0&&this.elements[B]!==H;B--);this.elements.length=B,this.top=this.elements[B-1]},fe.ElementStack.prototype.removeElement=function(H){if(this.top===H)this.pop();else{var B=this.elements.lastIndexOf(H);B!==-1&&this.elements.splice(B,1)}},fe.ElementStack.prototype.clearToContext=function(H){for(var B=this.elements.length-1;B>0&&!me(this.elements[B],H);B--);this.elements.length=B+1,this.top=this.elements[B]},fe.ElementStack.prototype.contains=function(H){return this.inSpecificScope(H,Object.create(null))},fe.ElementStack.prototype.inSpecificScope=function(H,B){for(var Q=this.elements.length-1;Q>=0;Q--){var z=this.elements[Q];if(me(z,H))return!0;if(me(z,B))return!1}return!1},fe.ElementStack.prototype.elementInSpecificScope=function(H,B){for(var Q=this.elements.length-1;Q>=0;Q--){var z=this.elements[Q];if(z===H)return!0;if(me(z,B))return!1}return!1},fe.ElementStack.prototype.elementTypeInSpecificScope=function(H,B){for(var Q=this.elements.length-1;Q>=0;Q--){var z=this.elements[Q];if(z instanceof H)return!0;if(me(z,B))return!1}return!1},fe.ElementStack.prototype.inScope=function(H){return this.inSpecificScope(H,r)},fe.ElementStack.prototype.elementInScope=function(H){return this.elementInSpecificScope(H,r)},fe.ElementStack.prototype.elementTypeInScope=function(H){return this.elementTypeInSpecificScope(H,r)},fe.ElementStack.prototype.inButtonScope=function(H){return this.inSpecificScope(H,w)},fe.ElementStack.prototype.inListItemScope=function(H){return this.inSpecificScope(H,o)},fe.ElementStack.prototype.inTableScope=function(H){return this.inSpecificScope(H,C)},fe.ElementStack.prototype.inSelectScope=function(H){for(var B=this.elements.length-1;B>=0;B--){var Q=this.elements[B];if(Q.namespaceURI!==a.HTML)return!1;var z=Q.localName;if(z===H)return!0;if(z!=="optgroup"&&z!=="option")return!1}return!1},fe.ElementStack.prototype.generateImpliedEndTags=function(H,B){for(var Q=B?j:ke,z=this.elements.length-1;z>=0;z--){var _e=this.elements[z];if(H&&me(_e,H)||!me(this.elements[z],Q))break}this.elements.length=z+1,this.top=this.elements[z]},fe.ActiveFormattingElements=function(){this.list=[],this.attrs=[]},fe.ActiveFormattingElements.prototype.MARKER={localName:"|"},fe.ActiveFormattingElements.prototype.insertMarker=function(){this.list.push(this.MARKER),this.attrs.push(this.MARKER)},fe.ActiveFormattingElements.prototype.push=function(H,B){for(var Q=0,z=this.list.length-1;z>=0&&this.list[z]!==this.MARKER;z--)if(pt(H,this.list[z],this.attrs[z])&&(Q++,Q===3)){this.list.splice(z,1),this.attrs.splice(z,1);break}this.list.push(H);for(var _e=[],W=0;W<B.length;W++)_e[W]=B[W];this.attrs.push(_e);function pt(Ze,dt,Ge){if(Ze.localName!==dt.localName||Ze._numattrs!==Ge.length)return!1;for(var Me=0,Ut=Ge.length;Me<Ut;Me++){var mt=Ge[Me][0],N=Ge[Me][1];if(!Ze.hasAttribute(mt)||Ze.getAttribute(mt)!==N)return!1}return!0}},fe.ActiveFormattingElements.prototype.clearToMarker=function(){for(var H=this.list.length-1;H>=0&&this.list[H]!==this.MARKER;H--);H<0&&(H=0),this.list.length=H,this.attrs.length=H},fe.ActiveFormattingElements.prototype.findElementByTag=function(H){for(var B=this.list.length-1;B>=0;B--){var Q=this.list[B];if(Q===this.MARKER)break;if(Q.localName===H)return Q}return null},fe.ActiveFormattingElements.prototype.indexOf=function(H){return this.list.lastIndexOf(H)},fe.ActiveFormattingElements.prototype.remove=function(H){var B=this.list.lastIndexOf(H);B!==-1&&(this.list.splice(B,1),this.attrs.splice(B,1))},fe.ActiveFormattingElements.prototype.replace=function(H,B,Q){var z=this.list.lastIndexOf(H);z!==-1&&(this.list[z]=B,this.attrs[z]=Q)},fe.ActiveFormattingElements.prototype.insertAfter=function(H,B){var Q=this.list.lastIndexOf(H);Q!==-1&&(this.list.splice(Q,0,B),this.attrs.splice(Q,0,B))};function fe(H,B,Q){var z=null,_e=0,W=0,pt=!1,Ze=!1,dt=0,Ge=[],Me="",Ut=!0,mt=0,N=ae,$e,ye,ge="",jt="",be=[],Ie="",Ae="",ve=[],Je=[],et=[],tt=[],Be=[],Vt=!1,q=Qa,ze=null,We=[],E=new fe.ElementStack,ne=new fe.ActiveFormattingElements,gt=B!==void 0,Gt=null,Ke=null,zt=!0;B&&(zt=B.ownerDocument._scripting_enabled),Q&&Q.scripting_enabled===!1&&(zt=!1);var Te=!0,gr=!1,Wt,br,U=[],rt=!1,bt=!1,Kt={document:function(){return he},_asDocumentFragment:function(){for(var e=he.createDocumentFragment(),n=he.firstChild;n.hasChildNodes();)e.appendChild(n.firstChild);return e},pause:function(){mt++},resume:function(){mt--,this.parse("")},parse:function(e,n,d){var S;return mt>0?(Me+=e,!0):(dt===0?(Me&&(e=Me+e,Me=""),n&&(e+="\uFFFF",pt=!0),z=e,_e=e.length,W=0,Ut&&(Ut=!1,z.charCodeAt(0)===65279&&(W=1)),dt++,S=en(d),Me=z.substring(W,_e),dt--):(dt++,Ge.push(z,_e,W),z=e,_e=e.length,W=0,en(),S=!1,Me=z.substring(W,_e),W=Ge.pop(),_e=Ge.pop(),z=Ge.pop(),Me&&(z=Me+z.substring(W),_e=z.length,W=0,Me=""),dt--),S)}},he=new _(!0,H);if(he._parser=Kt,he._scripting_enabled=zt,B){if(B.ownerDocument._quirks&&(he._quirks=!0),B.ownerDocument._limitedQuirks&&(he._limitedQuirks=!0),B.namespaceURI===a.HTML)switch(B.localName){case"title":case"textarea":N=st;break;case"style":case"xmp":case"iframe":case"noembed":case"noframes":case"script":case"plaintext":N=Tr;break}var Jr=he.createElement("html");he._appendChild(Jr),E.push(Jr),B instanceof c.HTMLTemplateElement&&We.push(Ar),At();for(var Lt=B;Lt!==null;Lt=Lt.parentElement)if(Lt instanceof c.HTMLFormElement){Ke=Lt;break}}function en(e){for(var n,d,S,x;W<_e;){if(mt>0||e&&e())return!0;switch(typeof N.lookahead){case"undefined":if(n=z.charCodeAt(W++),Ze&&(Ze=!1,n===10)){W++;continue}switch(n){case 13:W<_e?z.charCodeAt(W)===10&&W++:Ze=!0,N(10);break;case 65535:if(pt&&W===_e){N(s);break}default:N(n);break}break;case"number":n=z.charCodeAt(W);var V=N.lookahead,J=!0;if(V<0&&(J=!1,V=-V),V<_e-W)d=J?z.substring(W,W+V):null,x=!1;else if(pt)d=J?z.substring(W,_e):null,x=!0,n===65535&&W===_e-1&&(n=s);else return!0;N(n,d,x);break;case"string":n=z.charCodeAt(W),S=N.lookahead;var ie=z.indexOf(S,W);if(ie!==-1)d=z.substring(W,ie+S.length),x=!1;else{if(!pt)return!0;d=z.substring(W,_e),n===65535&&W===_e-1&&(n=s),x=!0}N(n,d,x);break}}return!1}function nt(e,n){for(var d=0;d<Be.length;d++)if(Be[d][0]===e)return;n!==void 0?Be.push([e,n]):Be.push([e])}function Jn(){Kr.lastIndex=W-1;var e=Kr.exec(z);if(!e)throw new Error("should never happen");var n=e[1];if(!n)return!1;var d=e[2],S=d.length;switch(d[0]){case'"':case"'":d=d.substring(1,S-1),W+=e[0].length-1,N=kr;break;default:N=Ve,W+=e[0].length-1,d=d.substring(0,S-1);break}for(var x=0;x<Be.length;x++)if(Be[x][0]===n)return!0;return Be.push([n,d]),!0}function ea(){Vt=!1,ge="",Be.length=0}function Ct(){Vt=!0,ge="",Be.length=0}function Xe(){be.length=0}function vr(){Ie=""}function _r(){Ae=""}function tn(){ve.length=0}function wt(){Je.length=0,et=null,tt=null}function Xt(){et=[]}function at(){tt=[]}function pe(){gr=!0}function ta(){return E.top&&E.top.namespaceURI!=="http://www.w3.org/1999/xhtml"}function He(e){return jt===e}function Nt(){if(U.length>0){var e=Ce(U);if(U.length=0,bt&&(bt=!1,e[0]===`
`&&(e=e.substring(1)),e.length===0))return;Ne(v,e),rt=!1}bt=!1}function xt(e){e.lastIndex=W-1;var n=e.exec(z);if(n&&n.index===W-1)return n=n[0],W+=n.length-1,pt&&W===_e&&(n=n.slice(0,-1),W--),n;throw new Error("should never happen")}function Dt(e){e.lastIndex=W-1;var n=e.exec(z)[0];return n?(ra(n),W+=n.length-1,!0):!1}function ra(e){U.length>0&&Nt(),!(bt&&(bt=!1,e[0]===`
`&&(e=e.substring(1)),e.length===0))&&Ne(v,e)}function Qe(){if(Vt)Ne(b,ge);else{var e=ge;ge="",jt=e,Ne(p,e,Be)}}function na(){if(W===_e)return!1;Wr.lastIndex=W;var e=Wr.exec(z);if(!e)throw new Error("should never happen");var n=e[2];if(!n)return!1;var d=e[1];return d?(W+=n.length+2,Ne(b,n)):(W+=n.length+1,jt=n,Ne(p,n,A)),!0}function aa(){Vt?Ne(b,ge,null,!0):Ne(p,ge,Be,!0)}function de(){Ne(F,Ce(Je),et?Ce(et):void 0,tt?Ce(tt):void 0)}function re(){Nt(),q(s),he.modclock=1}var Ne=Kt.insertToken=function(n,d,S,x){Nt();var V=E.top;!V||V.namespaceURI===a.HTML?q(n,d,S,x):n!==p&&n!==v?vn(n,d,S,x):Xr(V)&&(n===v||n===p&&d!=="mglyph"&&d!=="malignmark")||n===p&&d==="svg"&&V.namespaceURI===a.MATHML&&V.localName==="annotation-xml"||Qr(V)?(br=!0,q(n,d,S,x),br=!1):vn(n,d,S,x)};function Pe(e){var n=E.top;it&&me(n,le)?Yt(function(d){return d.createComment(e)}):(n instanceof c.HTMLTemplateElement&&(n=n.content),n._appendChild(n.ownerDocument.createComment(e)))}function Ue(e){var n=E.top;if(it&&me(n,le))Yt(function(S){return S.createTextNode(e)});else{n instanceof c.HTMLTemplateElement&&(n=n.content);var d=n.lastChild;d&&d.nodeType===i.TEXT_NODE?d.appendData(e):n._appendChild(n.ownerDocument.createTextNode(e))}}function Mt(e,n,d){var S=l.createElement(e,n,null);if(d)for(var x=0,V=d.length;x<V;x++)S._setAttribute(d[x][0],d[x][1]);return S}var it=!1;function te(e,n){var d=Qt(function(S){return Mt(S,e,n)});return me(d,t)&&(d._form=Ke),d}function Qt(e){var n;return it&&me(E.top,le)?n=Yt(e):E.top instanceof c.HTMLTemplateElement?(n=e(E.top.content.ownerDocument),E.top.content._appendChild(n)):(n=e(E.top.ownerDocument),E.top._appendChild(n)),E.push(n),n}function Er(e,n,d){return Qt(function(S){var x=S._createElementNS(e,d,null);if(n)for(var V=0,J=n.length;V<J;V++){var ie=n[V];ie.length===2?x._setAttribute(ie[0],ie[1]):x._setAttributeNS(ie[2],ie[0],ie[1])}return x})}function rn(e){for(var n=E.elements.length-1;n>=0;n--)if(E.elements[n]instanceof e)return n;return-1}function Yt(e){var n,d,S=-1,x=-1,V;if(S=rn(c.HTMLTableElement),x=rn(c.HTMLTemplateElement),x>=0&&(S<0||x>S)?n=E.elements[x]:S>=0&&(n=E.elements[S].parentNode,n?d=E.elements[S]:n=E.elements[S-1]),n||(n=E.elements[0]),n instanceof c.HTMLTemplateElement&&(n=n.content),V=e(n.ownerDocument),V.nodeType===i.TEXT_NODE){var J;if(d?J=d.previousSibling:J=n.lastChild,J&&J.nodeType===i.TEXT_NODE)return J.appendData(V.data),V}return d?n.insertBefore(V,d):n._appendChild(V),V}function At(){for(var e=!1,n=E.elements.length-1;n>=0;n--){var d=E.elements[n];if(n===0&&(e=!0,gt&&(d=B)),d.namespaceURI===a.HTML){var S=d.localName;switch(S){case"select":for(var x=n;x>0;){var V=E.elements[--x];if(V instanceof c.HTMLTemplateElement)break;if(V instanceof c.HTMLTableElement){q=ur;return}}q=Ye;return;case"tr":q=Ht;return;case"tbody":case"tfoot":case"thead":q=yt;return;case"caption":q=Mr;return;case"colgroup":q=lr;return;case"table":q=qe;return;case"template":q=We[We.length-1];return;case"body":q=$;return;case"frameset":q=Or;return;case"html":Gt===null?q=or:q=Dr;return;default:if(!e){if(S==="head"){q=we;return}if(S==="td"||S==="th"){q=St;return}}}}if(e){q=$;return}}}function Zt(e,n){te(e,n),N=Ot,ze=q,q=cr}function ia(e,n){te(e,n),N=st,ze=q,q=cr}function yr(e,n){return{elt:Mt(e,ne.list[n].localName,ne.attrs[n]),attrs:ne.attrs[n]}}function De(){if(ne.list.length!==0){var e=ne.list[ne.list.length-1];if(e!==ne.MARKER&&E.elements.lastIndexOf(e)===-1){for(var n=ne.list.length-2;n>=0&&(e=ne.list[n],!(e===ne.MARKER||E.elements.lastIndexOf(e)!==-1));n--);for(n=n+1;n<ne.list.length;n++){var d=Qt(function(S){return yr(S,n).elt});ne.list[n]=d}}}}var $t={localName:"BM"};function sa(e){if(me(E.top,e)&&ne.indexOf(E.top)===-1)return E.pop(),!0;for(var n=0;n<8;){n++;var d=ne.findElementByTag(e);if(!d)return!1;var S=E.elements.lastIndexOf(d);if(S===-1)return ne.remove(d),!0;if(!E.elementInScope(d))return!0;for(var x=null,V,J=S+1;J<E.elements.length;J++)if(me(E.elements[J],T)){x=E.elements[J],V=J;break}if(x){var ie=E.elements[S-1];ne.insertAfter(d,$t);for(var Ee=x,Le=x,Re=V,Fe,Tt=0;Tt++,Ee=E.elements[--Re],Ee!==d;){if(Fe=ne.indexOf(Ee),Tt>3&&Fe!==-1&&(ne.remove(Ee),Fe=-1),Fe===-1){E.removeElement(Ee);continue}var ft=yr(ie.ownerDocument,Fe);ne.replace(Ee,ft.elt,ft.attrs),E.elements[Re]=ft.elt,Ee=ft.elt,Le===x&&(ne.remove($t),ne.insertAfter(ft.elt,$t)),Ee._appendChild(Le),Le=Ee}it&&me(ie,le)?Yt(function(){return Le}):ie instanceof c.HTMLTemplateElement?ie.content._appendChild(Le):ie._appendChild(Le);for(var qt=yr(x.ownerDocument,ne.indexOf(d));x.hasChildNodes();)qt.elt._appendChild(x.firstChild);x._appendChild(qt.elt),ne.remove(d),ne.replace($t,qt.elt,qt.attrs),E.removeElement(d);var ei=E.elements.lastIndexOf(x);E.elements.splice(ei+1,0,qt.elt)}else return E.popElement(d),ne.remove(d),!0}return!0}function oa(){E.pop(),q=ze}function vt(){delete he._parser,E.elements.length=0,he.defaultView&&he.defaultView.dispatchEvent(new c.Event("load",{}))}function X(e,n){N=n,W--}function ae(e){switch(e){case 38:$e=ae,N=It;break;case 60:if(na())break;N=ca;break;case 0:U.push(e),rt=!0;break;case-1:re();break;default:Dt(Xn)||U.push(e);break}}function st(e){switch(e){case 38:$e=st,N=It;break;case 60:N=ua;break;case 0:U.push(65533),rt=!0;break;case-1:re();break;default:U.push(e);break}}function Ot(e){switch(e){case 60:N=pa;break;case 0:U.push(65533);break;case-1:re();break;default:Dt(zr)||U.push(e);break}}function ot(e){switch(e){case 60:N=ga;break;case 0:U.push(65533);break;case-1:re();break;default:Dt(zr)||U.push(e);break}}function Tr(e){switch(e){case 0:U.push(65533);break;case-1:re();break;default:Dt(Qn)||U.push(e);break}}function ca(e){switch(e){case 33:N=on;break;case 47:N=la;break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:ea(),X(e,nn);break;case 63:X(e,rr);break;default:U.push(60),X(e,ae);break}}function la(e){switch(e){case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:Ct(),X(e,nn);break;case 62:N=ae;break;case-1:U.push(60),U.push(47),re();break;default:X(e,rr);break}}function nn(e){switch(e){case 9:case 10:case 12:case 32:N=Ve;break;case 47:N=lt;break;case 62:N=ae,Qe();break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:ge+=String.fromCharCode(e+32);break;case 0:ge+="\uFFFD";break;case-1:re();break;default:ge+=xt(zn);break}}function ua(e){e===47?(Xe(),N=fa):(U.push(60),X(e,st))}function fa(e){switch(e){case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:Ct(),X(e,ha);break;default:U.push(60),U.push(47),X(e,st);break}}function ha(e){switch(e){case 9:case 10:case 12:case 32:if(He(ge)){N=Ve;return}break;case 47:if(He(ge)){N=lt;return}break;case 62:if(He(ge)){N=ae,Qe();return}break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:ge+=String.fromCharCode(e+32),be.push(e);return;case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:ge+=String.fromCharCode(e),be.push(e);return}U.push(60),U.push(47),u(U,be),X(e,st)}function pa(e){e===47?(Xe(),N=da):(U.push(60),X(e,Ot))}function da(e){switch(e){case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:Ct(),X(e,ma);break;default:U.push(60),U.push(47),X(e,Ot);break}}function ma(e){switch(e){case 9:case 10:case 12:case 32:if(He(ge)){N=Ve;return}break;case 47:if(He(ge)){N=lt;return}break;case 62:if(He(ge)){N=ae,Qe();return}break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:ge+=String.fromCharCode(e+32),be.push(e);return;case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:ge+=String.fromCharCode(e),be.push(e);return}U.push(60),U.push(47),u(U,be),X(e,Ot)}function ga(e){switch(e){case 47:Xe(),N=ba;break;case 33:N=_a,U.push(60),U.push(33);break;default:U.push(60),X(e,ot);break}}function ba(e){switch(e){case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:Ct(),X(e,va);break;default:U.push(60),U.push(47),X(e,ot);break}}function va(e){switch(e){case 9:case 10:case 12:case 32:if(He(ge)){N=Ve;return}break;case 47:if(He(ge)){N=lt;return}break;case 62:if(He(ge)){N=ae,Qe();return}break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:ge+=String.fromCharCode(e+32),be.push(e);return;case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:ge+=String.fromCharCode(e),be.push(e);return}U.push(60),U.push(47),u(U,be),X(e,ot)}function _a(e){e===45?(N=Ea,U.push(45)):X(e,ot)}function Ea(e){e===45?(N=an,U.push(45)):X(e,ot)}function je(e){switch(e){case 45:N=ya,U.push(45);break;case 60:N=wr;break;case 0:U.push(65533);break;case-1:re();break;default:U.push(e);break}}function ya(e){switch(e){case 45:N=an,U.push(45);break;case 60:N=wr;break;case 0:N=je,U.push(65533);break;case-1:re();break;default:N=je,U.push(e);break}}function an(e){switch(e){case 45:U.push(45);break;case 60:N=wr;break;case 62:N=ot,U.push(62);break;case 0:N=je,U.push(65533);break;case-1:re();break;default:N=je,U.push(e);break}}function wr(e){switch(e){case 47:Xe(),N=Ta;break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:Xe(),U.push(60),X(e,Na);break;default:U.push(60),X(e,je);break}}function Ta(e){switch(e){case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:Ct(),X(e,wa);break;default:U.push(60),U.push(47),X(e,je);break}}function wa(e){switch(e){case 9:case 10:case 12:case 32:if(He(ge)){N=Ve;return}break;case 47:if(He(ge)){N=lt;return}break;case 62:if(He(ge)){N=ae,Qe();return}break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:ge+=String.fromCharCode(e+32),be.push(e);return;case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:ge+=String.fromCharCode(e),be.push(e);return}U.push(60),U.push(47),u(U,be),X(e,je)}function Na(e){switch(e){case 9:case 10:case 12:case 32:case 47:case 62:Ce(be)==="script"?N=ct:N=je,U.push(e);break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:be.push(e+32),U.push(e);break;case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:be.push(e),U.push(e);break;default:X(e,je);break}}function ct(e){switch(e){case 45:N=Sa,U.push(45);break;case 60:N=Nr,U.push(60);break;case 0:U.push(65533);break;case-1:re();break;default:U.push(e);break}}function Sa(e){switch(e){case 45:N=ka,U.push(45);break;case 60:N=Nr,U.push(60);break;case 0:N=ct,U.push(65533);break;case-1:re();break;default:N=ct,U.push(e);break}}function ka(e){switch(e){case 45:U.push(45);break;case 60:N=Nr,U.push(60);break;case 62:N=ot,U.push(62);break;case 0:N=ct,U.push(65533);break;case-1:re();break;default:N=ct,U.push(e);break}}function Nr(e){e===47?(Xe(),N=La,U.push(47)):X(e,ct)}function La(e){switch(e){case 9:case 10:case 12:case 32:case 47:case 62:Ce(be)==="script"?N=je:N=ct,U.push(e);break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:be.push(e+32),U.push(e);break;case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:be.push(e),U.push(e);break;default:X(e,ct);break}}function Ve(e){switch(e){case 9:case 10:case 12:case 32:break;case 47:N=lt;break;case 62:N=ae,Qe();break;case-1:re();break;case 61:vr(),Ie+=String.fromCharCode(e),N=Sr;break;default:if(Jn())break;vr(),X(e,Sr);break}}function Sr(e){switch(e){case 9:case 10:case 12:case 32:case 47:case 62:case-1:X(e,Ca);break;case 61:N=sn;break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:Ie+=String.fromCharCode(e+32);break;case 0:Ie+="\uFFFD";break;case 34:case 39:case 60:default:Ie+=xt(Wn);break}}function Ca(e){switch(e){case 9:case 10:case 12:case 32:break;case 47:nt(Ie),N=lt;break;case 61:N=sn;break;case 62:N=ae,nt(Ie),Qe();break;case-1:nt(Ie),re();break;default:nt(Ie),vr(),X(e,Sr);break}}function sn(e){switch(e){case 9:case 10:case 12:case 32:break;case 34:_r(),N=Jt;break;case 39:_r(),N=er;break;case 62:default:_r(),X(e,tr);break}}function Jt(e){switch(e){case 34:nt(Ie,Ae),N=kr;break;case 38:$e=Jt,N=It;break;case 0:Ae+="\uFFFD";break;case-1:re();break;case 10:Ae+=String.fromCharCode(e);break;default:Ae+=xt(jn);break}}function er(e){switch(e){case 39:nt(Ie,Ae),N=kr;break;case 38:$e=er,N=It;break;case 0:Ae+="\uFFFD";break;case-1:re();break;case 10:Ae+=String.fromCharCode(e);break;default:Ae+=xt(Vn);break}}function tr(e){switch(e){case 9:case 10:case 12:case 32:nt(Ie,Ae),N=Ve;break;case 38:$e=tr,N=It;break;case 62:nt(Ie,Ae),N=ae,Qe();break;case 0:Ae+="\uFFFD";break;case-1:W--,N=ae;break;case 34:case 39:case 60:case 61:case 96:default:Ae+=xt(Gn);break}}function kr(e){switch(e){case 9:case 10:case 12:case 32:N=Ve;break;case 47:N=lt;break;case 62:N=ae,Qe();break;case-1:re();break;default:X(e,Ve);break}}function lt(e){switch(e){case 62:N=ae,aa();break;case-1:re();break;default:X(e,Ve);break}}function rr(e,n,d){var S=n.length;d?W+=S-1:W+=S;var x=n.substring(0,S-1);x=x.replace(/\u0000/g,"\uFFFD"),x=x.replace(/\u000D\u000A/g,`
`),x=x.replace(/\u000D/g,`
`),Ne(D,x),N=ae}rr.lookahead=">";function on(e,n,d){if(n[0]==="-"&&n[1]==="-"){W+=2,tn(),N=xa;return}n.toUpperCase()==="DOCTYPE"?(W+=7,N=qa):n==="[CDATA["&&ta()?(W+=7,N=xr):N=rr}on.lookahead=7;function xa(e){switch(tn(),e){case 45:N=Da;break;case 62:N=ae,Ne(D,Ce(ve));break;default:X(e,_t);break}}function Da(e){switch(e){case 45:N=nr;break;case 62:N=ae,Ne(D,Ce(ve));break;case-1:Ne(D,Ce(ve)),re();break;default:ve.push(45),X(e,_t);break}}function _t(e){switch(e){case 60:ve.push(e),N=Ma;break;case 45:N=Lr;break;case 0:ve.push(65533);break;case-1:Ne(D,Ce(ve)),re();break;default:ve.push(e);break}}function Ma(e){switch(e){case 33:ve.push(e),N=Aa;break;case 60:ve.push(e);break;default:X(e,_t);break}}function Aa(e){switch(e){case 45:N=Oa;break;default:X(e,_t);break}}function Oa(e){switch(e){case 45:N=Ia;break;default:X(e,Lr);break}}function Ia(e){switch(e){case 62:case-1:X(e,nr);break;default:X(e,nr);break}}function Lr(e){switch(e){case 45:N=nr;break;case-1:Ne(D,Ce(ve)),re();break;default:ve.push(45),X(e,_t);break}}function nr(e){switch(e){case 62:N=ae,Ne(D,Ce(ve));break;case 33:N=Ha;break;case 45:ve.push(45);break;case-1:Ne(D,Ce(ve)),re();break;default:ve.push(45),ve.push(45),X(e,_t);break}}function Ha(e){switch(e){case 45:ve.push(45),ve.push(45),ve.push(33),N=Lr;break;case 62:N=ae,Ne(D,Ce(ve));break;case-1:Ne(D,Ce(ve)),re();break;default:ve.push(45),ve.push(45),ve.push(33),X(e,_t);break}}function qa(e){switch(e){case 9:case 10:case 12:case 32:N=cn;break;case-1:wt(),pe(),de(),re();break;default:X(e,cn);break}}function cn(e){switch(e){case 9:case 10:case 12:case 32:break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:wt(),Je.push(e+32),N=Cr;break;case 0:wt(),Je.push(65533),N=Cr;break;case 62:wt(),pe(),N=ae,de();break;case-1:wt(),pe(),de(),re();break;default:wt(),Je.push(e),N=Cr;break}}function Cr(e){switch(e){case 9:case 10:case 12:case 32:N=ln;break;case 62:N=ae,de();break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:Je.push(e+32);break;case 0:Je.push(65533);break;case-1:pe(),de(),re();break;default:Je.push(e);break}}function ln(e,n,d){switch(e){case 9:case 10:case 12:case 32:W+=1;break;case 62:N=ae,W+=1,de();break;case-1:pe(),de(),re();break;default:n=n.toUpperCase(),n==="PUBLIC"?(W+=6,N=Ra):n==="SYSTEM"?(W+=6,N=Pa):(pe(),N=ut);break}}ln.lookahead=6;function Ra(e){switch(e){case 9:case 10:case 12:case 32:N=Ba;break;case 34:Xt(),N=un;break;case 39:Xt(),N=fn;break;case 62:pe(),N=ae,de();break;case-1:pe(),de(),re();break;default:pe(),N=ut;break}}function Ba(e){switch(e){case 9:case 10:case 12:case 32:break;case 34:Xt(),N=un;break;case 39:Xt(),N=fn;break;case 62:pe(),N=ae,de();break;case-1:pe(),de(),re();break;default:pe(),N=ut;break}}function un(e){switch(e){case 34:N=hn;break;case 0:et.push(65533);break;case 62:pe(),N=ae,de();break;case-1:pe(),de(),re();break;default:et.push(e);break}}function fn(e){switch(e){case 39:N=hn;break;case 0:et.push(65533);break;case 62:pe(),N=ae,de();break;case-1:pe(),de(),re();break;default:et.push(e);break}}function hn(e){switch(e){case 9:case 10:case 12:case 32:N=Fa;break;case 62:N=ae,de();break;case 34:at(),N=ar;break;case 39:at(),N=ir;break;case-1:pe(),de(),re();break;default:pe(),N=ut;break}}function Fa(e){switch(e){case 9:case 10:case 12:case 32:break;case 62:N=ae,de();break;case 34:at(),N=ar;break;case 39:at(),N=ir;break;case-1:pe(),de(),re();break;default:pe(),N=ut;break}}function Pa(e){switch(e){case 9:case 10:case 12:case 32:N=Ua;break;case 34:at(),N=ar;break;case 39:at(),N=ir;break;case 62:pe(),N=ae,de();break;case-1:pe(),de(),re();break;default:pe(),N=ut;break}}function Ua(e){switch(e){case 9:case 10:case 12:case 32:break;case 34:at(),N=ar;break;case 39:at(),N=ir;break;case 62:pe(),N=ae,de();break;case-1:pe(),de(),re();break;default:pe(),N=ut;break}}function ar(e){switch(e){case 34:N=pn;break;case 0:tt.push(65533);break;case 62:pe(),N=ae,de();break;case-1:pe(),de(),re();break;default:tt.push(e);break}}function ir(e){switch(e){case 39:N=pn;break;case 0:tt.push(65533);break;case 62:pe(),N=ae,de();break;case-1:pe(),de(),re();break;default:tt.push(e);break}}function pn(e){switch(e){case 9:case 10:case 12:case 32:break;case 62:N=ae,de();break;case-1:pe(),de(),re();break;default:N=ut;break}}function ut(e){switch(e){case 62:N=ae,de();break;case-1:de(),re();break}}function xr(e){switch(e){case 93:N=ja;break;case-1:re();break;case 0:rt=!0;default:Dt(Kn)||U.push(e);break}}function ja(e){switch(e){case 93:N=Va;break;default:U.push(93),X(e,xr);break}}function Va(e){switch(e){case 93:U.push(93);break;case 62:Nt(),N=ae;break;default:U.push(93),U.push(93),X(e,xr);break}}function It(e){switch(Xe(),be.push(38),e){case 9:case 10:case 12:case 32:case 60:case 38:case-1:X(e,Et);break;case 35:be.push(e),N=Ga;break;default:X(e,dn);break}}function dn(e){Oe.lastIndex=W;var n=Oe.exec(z);if(!n)throw new Error("should never happen");var d=n[1];if(!d){N=Et;return}switch(W+=d.length,u(be,Zn(d)),$e){case Jt:case er:case tr:if(d[d.length-1]!==";"&&/[=A-Za-z0-9]/.test(z[W])){N=Et;return}break}Xe();var S=ue[d];typeof S=="number"?be.push(S):u(be,S),N=Et}dn.lookahead=-Un;function Ga(e){switch(ye=0,e){case 120:case 88:be.push(e),N=za;break;default:X(e,Wa);break}}function za(e){switch(e){case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:case 65:case 66:case 67:case 68:case 69:case 70:case 97:case 98:case 99:case 100:case 101:case 102:X(e,Ka);break;default:X(e,Et);break}}function Wa(e){switch(e){case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:X(e,Xa);break;default:X(e,Et);break}}function Ka(e){switch(e){case 65:case 66:case 67:case 68:case 69:case 70:ye*=16,ye+=e-55;break;case 97:case 98:case 99:case 100:case 101:case 102:ye*=16,ye+=e-87;break;case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:ye*=16,ye+=e-48;break;case 59:N=sr;break;default:X(e,sr);break}}function Xa(e){switch(e){case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:ye*=10,ye+=e-48;break;case 59:N=sr;break;default:X(e,sr);break}}function sr(e){ye in G?ye=G[ye]:(ye>1114111||ye>=55296&&ye<57344)&&(ye=65533),Xe(),ye<=65535?be.push(ye):(ye=ye-65536,be.push(55296+(ye>>10)),be.push(56320+(ye&1023))),X(e,Et)}function Et(e){switch($e){case Jt:case er:case tr:Ae+=Ce(be);break;default:u(U,be);break}X(e,$e)}function Qa(e,n,d,S){switch(e){case 1:if(n=n.replace(ht,""),n.length===0)return;break;case 4:he._appendChild(he.createComment(n));return;case 5:var x=n,V=d,J=S;he.appendChild(new f(he,x,V,J)),gr||x.toLowerCase()!=="html"||ee.test(V)||J&&J.toLowerCase()===R||J===void 0&&y.test(V)?he._quirks=!0:(m.test(V)||J!==void 0&&y.test(V))&&(he._limitedQuirks=!0),q=mn;return}he._quirks=!0,q=mn,q(e,n,d,S)}function mn(e,n,d,S){var x;switch(e){case 1:if(n=n.replace(ht,""),n.length===0)return;break;case 5:return;case 4:he._appendChild(he.createComment(n));return;case 2:if(n==="html"){x=Mt(he,n,d),E.push(x),he.appendChild(x),q=or;return}break;case 3:switch(n){case"html":case"head":case"body":case"br":break;default:return}}x=Mt(he,"html",null),E.push(x),he.appendChild(x),q=or,q(e,n,d,S)}function or(e,n,d,S){switch(e){case 1:if(n=n.replace(ht,""),n.length===0)return;break;case 5:return;case 4:Pe(n);return;case 2:switch(n){case"html":$(e,n,d,S);return;case"head":var x=te(n,d);Gt=x,q=we;return}break;case 3:switch(n){case"html":case"head":case"body":case"br":break;default:return}}or(p,"head",null),q(e,n,d,S)}function we(e,n,d,S){switch(e){case 1:var x=n.match(ht);if(x&&(Ue(x[0]),n=n.substring(x[0].length)),n.length===0)return;break;case 4:Pe(n);return;case 5:return;case 2:switch(n){case"html":$(e,n,d,S);return;case"meta":case"base":case"basefont":case"bgsound":case"link":te(n,d),E.pop();return;case"title":ia(n,d);return;case"noscript":if(!zt){te(n,d),q=gn;return}case"noframes":case"style":Zt(n,d);return;case"script":Qt(function(V){var J=Mt(V,n,d);return J._parser_inserted=!0,J._force_async=!1,gt&&(J._already_started=!0),Nt(),J}),N=ot,ze=q,q=cr;return;case"template":te(n,d),ne.insertMarker(),Te=!1,q=Ar,We.push(q);return;case"head":return}break;case 3:switch(n){case"head":E.pop(),q=Dr;return;case"body":case"html":case"br":break;case"template":if(!E.contains("template"))return;E.generateImpliedEndTags(null,"thorough"),E.popTag("template"),ne.clearToMarker(),We.pop(),At();return;default:return}break}we(b,"head",null),q(e,n,d,S)}function gn(e,n,d,S){switch(e){case 5:return;case 4:we(e,n);return;case 1:var x=n.match(ht);if(x&&(we(e,x[0]),n=n.substring(x[0].length)),n.length===0)return;break;case 2:switch(n){case"html":$(e,n,d,S);return;case"basefont":case"bgsound":case"link":case"meta":case"noframes":case"style":we(e,n,d);return;case"head":case"noscript":return}break;case 3:switch(n){case"noscript":E.pop(),q=we;return;case"br":break;default:return}break}gn(b,"noscript",null),q(e,n,d,S)}function Dr(e,n,d,S){switch(e){case 1:var x=n.match(ht);if(x&&(Ue(x[0]),n=n.substring(x[0].length)),n.length===0)return;break;case 4:Pe(n);return;case 5:return;case 2:switch(n){case"html":$(e,n,d,S);return;case"body":te(n,d),Te=!1,q=$;return;case"frameset":te(n,d),q=Or;return;case"base":case"basefont":case"bgsound":case"link":case"meta":case"noframes":case"script":case"style":case"template":case"title":E.push(Gt),we(p,n,d),E.removeElement(Gt);return;case"head":return}break;case 3:switch(n){case"template":return we(e,n,d,S);case"body":case"html":case"br":break;default:return}break}Dr(p,"body",null),Te=!0,q(e,n,d,S)}function $(e,n,d,S){var x,V,J,ie;switch(e){case 1:if(rt&&(n=n.replace(Pt,""),n.length===0))return;Te&&Ft.test(n)&&(Te=!1),De(),Ue(n);return;case 5:return;case 4:Pe(n);return;case-1:if(We.length)return Ar(e);vt();return;case 2:switch(n){case"html":if(E.contains("template"))return;$r(d,E.elements[0]);return;case"base":case"basefont":case"bgsound":case"link":case"meta":case"noframes":case"script":case"style":case"template":case"title":we(p,n,d);return;case"body":if(x=E.elements[1],!x||!(x instanceof c.HTMLBodyElement)||E.contains("template"))return;Te=!1,$r(d,x);return;case"frameset":if(!Te||(x=E.elements[1],!x||!(x instanceof c.HTMLBodyElement)))return;for(x.parentNode&&x.parentNode.removeChild(x);!(E.top instanceof c.HTMLHtmlElement);)E.pop();te(n,d),q=Or;return;case"address":case"article":case"aside":case"blockquote":case"center":case"details":case"dialog":case"dir":case"div":case"dl":case"fieldset":case"figcaption":case"figure":case"footer":case"header":case"hgroup":case"main":case"nav":case"ol":case"p":case"section":case"summary":case"ul":E.inButtonScope("p")&&$(b,"p"),te(n,d);return;case"menu":E.inButtonScope("p")&&$(b,"p"),me(E.top,"menuitem")&&E.pop(),te(n,d);return;case"h1":case"h2":case"h3":case"h4":case"h5":case"h6":E.inButtonScope("p")&&$(b,"p"),E.top instanceof c.HTMLHeadingElement&&E.pop(),te(n,d);return;case"pre":case"listing":E.inButtonScope("p")&&$(b,"p"),te(n,d),bt=!0,Te=!1;return;case"form":if(Ke&&!E.contains("template"))return;E.inButtonScope("p")&&$(b,"p"),ie=te(n,d),E.contains("template")||(Ke=ie);return;case"li":for(Te=!1,V=E.elements.length-1;V>=0;V--){if(J=E.elements[V],J instanceof c.HTMLLIElement){$(b,"li");break}if(me(J,T)&&!me(J,h))break}E.inButtonScope("p")&&$(b,"p"),te(n,d);return;case"dd":case"dt":for(Te=!1,V=E.elements.length-1;V>=0;V--){if(J=E.elements[V],me(J,se)){$(b,J.localName);break}if(me(J,T)&&!me(J,h))break}E.inButtonScope("p")&&$(b,"p"),te(n,d);return;case"plaintext":E.inButtonScope("p")&&$(b,"p"),te(n,d),N=Tr;return;case"button":E.inScope("button")?($(b,"button"),q(e,n,d,S)):(De(),te(n,d),Te=!1);return;case"a":var Ee=ne.findElementByTag("a");Ee&&($(b,n),ne.remove(Ee),E.removeElement(Ee));case"b":case"big":case"code":case"em":case"font":case"i":case"s":case"small":case"strike":case"strong":case"tt":case"u":De(),ne.push(te(n,d),d);return;case"nobr":De(),E.inScope(n)&&($(b,n),De()),ne.push(te(n,d),d);return;case"applet":case"marquee":case"object":De(),te(n,d),ne.insertMarker(),Te=!1;return;case"table":!he._quirks&&E.inButtonScope("p")&&$(b,"p"),te(n,d),Te=!1,q=qe;return;case"area":case"br":case"embed":case"img":case"keygen":case"wbr":De(),te(n,d),E.pop(),Te=!1;return;case"input":De(),ie=te(n,d),E.pop();var Le=ie.getAttribute("type");(!Le||Le.toLowerCase()!=="hidden")&&(Te=!1);return;case"param":case"source":case"track":te(n,d),E.pop();return;case"hr":E.inButtonScope("p")&&$(b,"p"),me(E.top,"menuitem")&&E.pop(),te(n,d),E.pop(),Te=!1;return;case"image":$(p,"img",d,S);return;case"textarea":te(n,d),bt=!0,Te=!1,N=st,ze=q,q=cr;return;case"xmp":E.inButtonScope("p")&&$(b,"p"),De(),Te=!1,Zt(n,d);return;case"iframe":Te=!1,Zt(n,d);return;case"noembed":Zt(n,d);return;case"select":De(),te(n,d),Te=!1,q===qe||q===Mr||q===yt||q===Ht||q===St?q=ur:q=Ye;return;case"optgroup":case"option":E.top instanceof c.HTMLOptionElement&&$(b,"option"),De(),te(n,d);return;case"menuitem":me(E.top,"menuitem")&&E.pop(),De(),te(n,d);return;case"rb":case"rtc":E.inScope("ruby")&&E.generateImpliedEndTags(),te(n,d);return;case"rp":case"rt":E.inScope("ruby")&&E.generateImpliedEndTags("rtc"),te(n,d);return;case"math":De(),Zr(d),mr(d),Er(n,d,a.MATHML),S&&E.pop();return;case"svg":De(),Yr(d),mr(d),Er(n,d,a.SVG),S&&E.pop();return;case"caption":case"col":case"colgroup":case"frame":case"head":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":return}De(),te(n,d);return;case 3:switch(n){case"template":we(b,n,d);return;case"body":if(!E.inScope("body"))return;q=bn;return;case"html":if(!E.inScope("body"))return;q=bn,q(e,n,d);return;case"address":case"article":case"aside":case"blockquote":case"button":case"center":case"details":case"dialog":case"dir":case"div":case"dl":case"fieldset":case"figcaption":case"figure":case"footer":case"header":case"hgroup":case"listing":case"main":case"menu":case"nav":case"ol":case"pre":case"section":case"summary":case"ul":if(!E.inScope(n))return;E.generateImpliedEndTags(),E.popTag(n);return;case"form":if(E.contains("template")){if(!E.inScope("form"))return;E.generateImpliedEndTags(),E.popTag("form")}else{var Re=Ke;if(Ke=null,!Re||!E.elementInScope(Re))return;E.generateImpliedEndTags(),E.removeElement(Re)}return;case"p":E.inButtonScope(n)?(E.generateImpliedEndTags(n),E.popTag(n)):($(p,n,null),q(e,n,d,S));return;case"li":if(!E.inListItemScope(n))return;E.generateImpliedEndTags(n),E.popTag(n);return;case"dd":case"dt":if(!E.inScope(n))return;E.generateImpliedEndTags(n),E.popTag(n);return;case"h1":case"h2":case"h3":case"h4":case"h5":case"h6":if(!E.elementTypeInScope(c.HTMLHeadingElement))return;E.generateImpliedEndTags(),E.popElementType(c.HTMLHeadingElement);return;case"sarcasm":break;case"a":case"b":case"big":case"code":case"em":case"font":case"i":case"nobr":case"s":case"small":case"strike":case"strong":case"tt":case"u":var Fe=sa(n);if(Fe)return;break;case"applet":case"marquee":case"object":if(!E.inScope(n))return;E.generateImpliedEndTags(),E.popTag(n),ne.clearToMarker();return;case"br":$(p,n,null);return}for(V=E.elements.length-1;V>=0;V--)if(J=E.elements[V],me(J,n)){E.generateImpliedEndTags(n),E.popElement(J);break}else if(me(J,T))return;return}}function cr(e,n,d,S){switch(e){case 1:Ue(n);return;case-1:E.top instanceof c.HTMLScriptElement&&(E.top._already_started=!0),E.pop(),q=ze,q(e);return;case 3:n==="script"?oa():(E.pop(),q=ze);return;default:return}}function qe(e,n,d,S){function x(J){for(var ie=0,Ee=J.length;ie<Ee;ie++)if(J[ie][0]==="type")return J[ie][1].toLowerCase();return null}switch(e){case 1:if(br){$(e,n,d,S);return}else if(me(E.top,le)){Wt=[],ze=q,q=Ya,q(e,n,d,S);return}break;case 4:Pe(n);return;case 5:return;case 2:switch(n){case"caption":E.clearToContext(k),ne.insertMarker(),te(n,d),q=Mr;return;case"colgroup":E.clearToContext(k),te(n,d),q=lr;return;case"col":qe(p,"colgroup",null),q(e,n,d,S);return;case"tbody":case"tfoot":case"thead":E.clearToContext(k),te(n,d),q=yt;return;case"td":case"th":case"tr":qe(p,"tbody",null),q(e,n,d,S);return;case"table":if(!E.inTableScope(n))return;qe(b,n),q(e,n,d,S);return;case"style":case"script":case"template":we(e,n,d,S);return;case"input":var V=x(d);if(V!=="hidden")break;te(n,d),E.pop();return;case"form":if(Ke||E.contains("template"))return;Ke=te(n,d),E.popElement(Ke);return}break;case 3:switch(n){case"table":if(!E.inTableScope(n))return;E.popTag(n),At();return;case"body":case"caption":case"col":case"colgroup":case"html":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":return;case"template":we(e,n,d,S);return}break;case-1:$(e,n,d,S);return}it=!0,$(e,n,d,S),it=!1}function Ya(e,n,d,S){if(e===v){if(rt&&(n=n.replace(Pt,""),n.length===0))return;Wt.push(n)}else{var x=Wt.join("");Wt.length=0,Ft.test(x)?(it=!0,$(v,x),it=!1):Ue(x),q=ze,q(e,n,d,S)}}function Mr(e,n,d,S){function x(){return E.inTableScope("caption")?(E.generateImpliedEndTags(),E.popTag("caption"),ne.clearToMarker(),q=qe,!0):!1}switch(e){case 2:switch(n){case"caption":case"col":case"colgroup":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":x()&&q(e,n,d,S);return}break;case 3:switch(n){case"caption":x();return;case"table":x()&&q(e,n,d,S);return;case"body":case"col":case"colgroup":case"html":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":return}break}$(e,n,d,S)}function lr(e,n,d,S){switch(e){case 1:var x=n.match(ht);if(x&&(Ue(x[0]),n=n.substring(x[0].length)),n.length===0)return;break;case 4:Pe(n);return;case 5:return;case 2:switch(n){case"html":$(e,n,d,S);return;case"col":te(n,d),E.pop();return;case"template":we(e,n,d,S);return}break;case 3:switch(n){case"colgroup":if(!me(E.top,"colgroup"))return;E.pop(),q=qe;return;case"col":return;case"template":we(e,n,d,S);return}break;case-1:$(e,n,d,S);return}me(E.top,"colgroup")&&(lr(b,"colgroup"),q(e,n,d,S))}function yt(e,n,d,S){function x(){!E.inTableScope("tbody")&&!E.inTableScope("thead")&&!E.inTableScope("tfoot")||(E.clearToContext(I),yt(b,E.top.localName,null),q(e,n,d,S))}switch(e){case 2:switch(n){case"tr":E.clearToContext(I),te(n,d),q=Ht;return;case"th":case"td":yt(p,"tr",null),q(e,n,d,S);return;case"caption":case"col":case"colgroup":case"tbody":case"tfoot":case"thead":x();return}break;case 3:switch(n){case"table":x();return;case"tbody":case"tfoot":case"thead":E.inTableScope(n)&&(E.clearToContext(I),E.pop(),q=qe);return;case"body":case"caption":case"col":case"colgroup":case"html":case"td":case"th":case"tr":return}break}qe(e,n,d,S)}function Ht(e,n,d,S){function x(){return E.inTableScope("tr")?(E.clearToContext(Y),E.pop(),q=yt,!0):!1}switch(e){case 2:switch(n){case"th":case"td":E.clearToContext(Y),te(n,d),q=St,ne.insertMarker();return;case"caption":case"col":case"colgroup":case"tbody":case"tfoot":case"thead":case"tr":x()&&q(e,n,d,S);return}break;case 3:switch(n){case"tr":x();return;case"table":x()&&q(e,n,d,S);return;case"tbody":case"tfoot":case"thead":E.inTableScope(n)&&x()&&q(e,n,d,S);return;case"body":case"caption":case"col":case"colgroup":case"html":case"td":case"th":return}break}qe(e,n,d,S)}function St(e,n,d,S){switch(e){case 2:switch(n){case"caption":case"col":case"colgroup":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":E.inTableScope("td")?(St(b,"td"),q(e,n,d,S)):E.inTableScope("th")&&(St(b,"th"),q(e,n,d,S));return}break;case 3:switch(n){case"td":case"th":if(!E.inTableScope(n))return;E.generateImpliedEndTags(),E.popTag(n),ne.clearToMarker(),q=Ht;return;case"body":case"caption":case"col":case"colgroup":case"html":return;case"table":case"tbody":case"tfoot":case"thead":case"tr":if(!E.inTableScope(n))return;St(b,E.inTableScope("td")?"td":"th"),q(e,n,d,S);return}break}$(e,n,d,S)}function Ye(e,n,d,S){switch(e){case 1:if(rt&&(n=n.replace(Pt,""),n.length===0))return;Ue(n);return;case 4:Pe(n);return;case 5:return;case-1:$(e,n,d,S);return;case 2:switch(n){case"html":$(e,n,d,S);return;case"option":E.top instanceof c.HTMLOptionElement&&Ye(b,n),te(n,d);return;case"optgroup":E.top instanceof c.HTMLOptionElement&&Ye(b,"option"),E.top instanceof c.HTMLOptGroupElement&&Ye(b,n),te(n,d);return;case"select":Ye(b,n);return;case"input":case"keygen":case"textarea":if(!E.inSelectScope("select"))return;Ye(b,"select"),q(e,n,d,S);return;case"script":case"template":we(e,n,d,S);return}break;case 3:switch(n){case"optgroup":E.top instanceof c.HTMLOptionElement&&E.elements[E.elements.length-2]instanceof c.HTMLOptGroupElement&&Ye(b,"option"),E.top instanceof c.HTMLOptGroupElement&&E.pop();return;case"option":E.top instanceof c.HTMLOptionElement&&E.pop();return;case"select":if(!E.inSelectScope(n))return;E.popTag(n),At();return;case"template":we(e,n,d,S);return}break}}function ur(e,n,d,S){switch(n){case"caption":case"table":case"tbody":case"tfoot":case"thead":case"tr":case"td":case"th":switch(e){case 2:ur(b,"select"),q(e,n,d,S);return;case 3:E.inTableScope(n)&&(ur(b,"select"),q(e,n,d,S));return}}Ye(e,n,d,S)}function Ar(e,n,d,S){function x(V){q=V,We[We.length-1]=q,q(e,n,d,S)}switch(e){case 1:case 4:case 5:$(e,n,d,S);return;case-1:E.contains("template")?(E.popTag("template"),ne.clearToMarker(),We.pop(),At(),q(e,n,d,S)):vt();return;case 2:switch(n){case"base":case"basefont":case"bgsound":case"link":case"meta":case"noframes":case"script":case"style":case"template":case"title":we(e,n,d,S);return;case"caption":case"colgroup":case"tbody":case"tfoot":case"thead":x(qe);return;case"col":x(lr);return;case"tr":x(yt);return;case"td":case"th":x(Ht);return}x($);return;case 3:switch(n){case"template":we(e,n,d,S);return;default:return}}}function bn(e,n,d,S){switch(e){case 1:if(Ft.test(n))break;$(e,n);return;case 4:E.elements[0]._appendChild(he.createComment(n));return;case 5:return;case-1:vt();return;case 2:if(n==="html"){$(e,n,d,S);return}break;case 3:if(n==="html"){if(gt)return;q=$a;return}break}q=$,q(e,n,d,S)}function Or(e,n,d,S){switch(e){case 1:n=n.replace(dr,""),n.length>0&&Ue(n);return;case 4:Pe(n);return;case 5:return;case-1:vt();return;case 2:switch(n){case"html":$(e,n,d,S);return;case"frameset":te(n,d);return;case"frame":te(n,d),E.pop();return;case"noframes":we(e,n,d,S);return}break;case 3:if(n==="frameset"){if(gt&&E.top instanceof c.HTMLHtmlElement)return;E.pop(),!gt&&!(E.top instanceof c.HTMLFrameSetElement)&&(q=Za);return}break}}function Za(e,n,d,S){switch(e){case 1:n=n.replace(dr,""),n.length>0&&Ue(n);return;case 4:Pe(n);return;case 5:return;case-1:vt();return;case 2:switch(n){case"html":$(e,n,d,S);return;case"noframes":we(e,n,d,S);return}break;case 3:if(n==="html"){q=Ja;return}break}}function $a(e,n,d,S){switch(e){case 1:if(Ft.test(n))break;$(e,n,d,S);return;case 4:he._appendChild(he.createComment(n));return;case 5:$(e,n,d,S);return;case-1:vt();return;case 2:if(n==="html"){$(e,n,d,S);return}break}q=$,q(e,n,d,S)}function Ja(e,n,d,S){switch(e){case 1:n=n.replace(dr,""),n.length>0&&$(e,n,d,S);return;case 4:he._appendChild(he.createComment(n));return;case 5:$(e,n,d,S);return;case-1:vt();return;case 2:switch(n){case"html":$(e,n,d,S);return;case"noframes":we(e,n,d,S);return}break}}function vn(e,n,d,S){function x(Ee){for(var Le=0,Re=Ee.length;Le<Re;Le++)switch(Ee[Le][0]){case"color":case"face":case"size":return!0}return!1}var V;switch(e){case 1:Te&&Yn.test(n)&&(Te=!1),rt&&(n=n.replace(Pt,"\uFFFD")),Ue(n);return;case 4:Pe(n);return;case 5:return;case 2:switch(n){case"font":if(!x(d))break;case"b":case"big":case"blockquote":case"body":case"br":case"center":case"code":case"dd":case"div":case"dl":case"dt":case"em":case"embed":case"h1":case"h2":case"h3":case"h4":case"h5":case"h6":case"head":case"hr":case"i":case"img":case"li":case"listing":case"menu":case"meta":case"nobr":case"ol":case"p":case"pre":case"ruby":case"s":case"small":case"span":case"strong":case"strike":case"sub":case"sup":case"table":case"tt":case"u":case"ul":case"var":if(gt)break;do E.pop(),V=E.top;while(V.namespaceURI!==a.HTML&&!Xr(V)&&!Qr(V));Ne(e,n,d,S);return}V=E.elements.length===1&&gt?B:E.top,V.namespaceURI===a.MATHML?Zr(d):V.namespaceURI===a.SVG&&(n=$n(n),Yr(d)),mr(d),Er(n,d,V.namespaceURI),S&&E.pop();return;case 3:if(V=E.top,n==="script"&&V.namespaceURI===a.SVG&&V.localName==="script")E.pop();else for(var J=E.elements.length-1,ie=E.elements[J];;){if(ie.localName.toLowerCase()===n){E.popElement(ie);break}if(ie=E.elements[--J],ie.namespaceURI===a.HTML){q(e,n,d,S);break}}return}}return Kt.testTokenizer=function(e,n,d,S){var x=[];switch(n){case"PCDATA state":N=ae;break;case"RCDATA state":N=st;break;case"RAWTEXT state":N=Ot;break;case"PLAINTEXT state":N=Tr;break}if(d&&(jt=d),Ne=function(J,ie,Ee,Le){switch(Nt(),J){case 1:x.length>0&&x[x.length-1][0]==="Character"?x[x.length-1][1]+=ie:x.push(["Character",ie]);break;case 4:x.push(["Comment",ie]);break;case 5:x.push(["DOCTYPE",ie,Ee===void 0?null:Ee,Le===void 0?null:Le,!gr]);break;case 2:for(var Re=Object.create(null),Fe=0;Fe<Ee.length;Fe++){var Tt=Ee[Fe];Tt.length===1?Re[Tt[0]]="":Re[Tt[0]]=Tt[1]}var ft=["StartTag",ie,Re];Le&&ft.push(!0),x.push(ft);break;case 3:x.push(["EndTag",ie]);break}},!S)this.parse(e,!0);else{for(var V=0;V<e.length;V++)this.parse(e[V]);this.parse("",!0)}return x},Kt}}}),pr=Z({"external/npm/node_modules/domino/lib/DOMImplementation.js"(M,O){O.exports=c;var _=jr(),f=Vr(),i=Gr(),a=Se(),l=qr();function c(s){this.contextObject=s}var u={xml:{"":!0,"1.0":!0,"2.0":!0},core:{"":!0,"2.0":!0},html:{"":!0,"1.0":!0,"2.0":!0},xhtml:{"":!0,"1.0":!0,"2.0":!0}};c.prototype={hasFeature:function(v,p){var b=u[(v||"").toLowerCase()];return b&&b[p||""]||!1},createDocumentType:function(v,p,b){return l.isValidQName(v)||a.InvalidCharacterError(),new f(this.contextObject,v,p,b)},createDocument:function(v,p,b){var D=new _(!1,null),F;return p?F=D.createElementNS(v,p):F=null,b&&D.appendChild(b),F&&D.appendChild(F),v===a.NAMESPACE.HTML?D._contentType="application/xhtml+xml":v===a.NAMESPACE.SVG?D._contentType="image/svg+xml":D._contentType="application/xml",D},createHTMLDocument:function(v){var p=new _(!0,null);p.appendChild(new f(p,"html"));var b=p.createElement("html");p.appendChild(b);var D=p.createElement("head");if(b.appendChild(D),v!==void 0){var F=p.createElement("title");D.appendChild(F),F.appendChild(p.createTextNode(v))}return b.appendChild(p.createElement("body")),p.modclock=1,p},mozSetOutputMutationHandler:function(s,v){s.mutationHandler=v},mozGetInputMutationHandler:function(s){a.nyi()},mozHTMLParser:i}}}),ui=Z({"external/npm/node_modules/domino/lib/Location.js"(M,O){var _=Fr(),f=qn();O.exports=i;function i(a,l){this._window=a,this._href=l}i.prototype=Object.create(f.prototype,{constructor:{value:i},href:{get:function(){return this._href},set:function(a){this.assign(a)}},assign:{value:function(a){var l=new _(this._href),c=l.resolve(a);this._href=c}},replace:{value:function(a){this.assign(a)}},reload:{value:function(){this.assign(this.href)}},toString:{value:function(){return this.href}}})}}),fi=Z({"external/npm/node_modules/domino/lib/NavigatorID.js"(M,O){var _=Object.create(null,{appCodeName:{value:"Mozilla"},appName:{value:"Netscape"},appVersion:{value:"4.0"},platform:{value:""},product:{value:"Gecko"},productSub:{value:"20100101"},userAgent:{value:""},vendor:{value:""},vendorSub:{value:""},taintEnabled:{value:function(){return!1}}});O.exports=_}}),hi=Z({"external/npm/node_modules/domino/lib/WindowTimers.js"(M,O){var _={setTimeout,clearTimeout,setInterval,clearInterval};O.exports=_}}),Fn=Z({"external/npm/node_modules/domino/lib/impl.js"(M,O){var _=Se();M=O.exports={CSSStyleDeclaration:Pr(),CharacterData:fr(),Comment:Mn(),DOMImplementation:pr(),DOMTokenList:kn(),Document:jr(),DocumentFragment:An(),DocumentType:Vr(),Element:Bt(),HTMLParser:Gr(),NamedNodeMap:Cn(),Node:xe(),NodeList:kt(),NodeFilter:hr(),ProcessingInstruction:On(),Text:Dn(),Window:Pn()},_.merge(M,Hn()),_.merge(M,Ur().elements),_.merge(M,Bn().elements)}}),Pn=Z({"external/npm/node_modules/domino/lib/Window.js"(M,O){var _=pr(),f=Tn(),i=ui(),a=Se();O.exports=l;function l(c){this.document=c||new _(null).createHTMLDocument(""),this.document._scripting_enabled=!0,this.document.defaultView=this,this.location=new i(this,this.document._address||"about:blank")}l.prototype=Object.create(f.prototype,{console:{value:console},history:{value:{back:a.nyi,forward:a.nyi,go:a.nyi}},navigator:{value:fi()},window:{get:function(){return this}},self:{get:function(){return this}},frames:{get:function(){return this}},parent:{get:function(){return this}},top:{get:function(){return this}},length:{value:0},frameElement:{value:null},opener:{value:null},onload:{get:function(){return this._getEventHandler("load")},set:function(c){this._setEventHandler("load",c)}},getComputedStyle:{value:function(u){return u.style}}}),a.expose(hi(),l),a.expose(Fn(),l)}}),pi=Z({"external/npm/node_modules/domino/lib/index.js"(M){var O=pr(),_=Gr();Pn();var f=Fn();M.createDOMImplementation=function(){return new O(null)},M.createDocument=function(i,a){if(i||a){var l=new _;return l.parse(i||"",!0),l.document()}return new O(null).createHTMLDocument("")},M.createIncrementalHTMLParser=function(){var i=new _;return{write:function(a){a.length>0&&i.parse(a,!1,function(){return!0})},end:function(a){i.parse(a||"",!0,function(){return!0})},process:function(a){return i.parse("",!1,a)},document:function(){return i.document()}}},M.createWindow=function(i,a){var l=M.createDocument(i);return a!==void 0&&(l._address=a),new f.Window(l)},M.impl=f}}),_n=pi();function di(){Object.assign(globalThis,_n.impl),globalThis.KeyboardEvent=_n.impl.Event}di();
