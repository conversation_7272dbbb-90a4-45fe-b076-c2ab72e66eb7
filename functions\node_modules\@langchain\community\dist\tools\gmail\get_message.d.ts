import { z } from "zod";
import { InferInteropZodOutput } from "@langchain/core/utils/types";
import { GmailBaseToolParams, GmailBaseTool } from "./base.js";
export declare class GmailGetMessage extends GmailBaseTool {
    name: string;
    schema: z.ZodObject<{
        messageId: z.ZodString;
    }, "strip", z.ZodType<PERSON>ny, {
        messageId: string;
    }, {
        messageId: string;
    }>;
    description: string;
    constructor(fields?: GmailBaseToolParams);
    _call(arg: InferInteropZodOutput<typeof this.schema>): Promise<string>;
}
export type GetMessageSchema = {
    messageId: string;
};
